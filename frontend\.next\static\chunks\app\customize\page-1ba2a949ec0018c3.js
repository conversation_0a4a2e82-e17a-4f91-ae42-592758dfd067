(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[462],{17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>c,av:()=>o,j7:()=>n,tU:()=>i});var t=s(95155);s(12115);var r=s(60704),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",a),...s})}},23517:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>I});var t=s(95155),r=s(12115),l=s(11581),i=s(37784),n=s(30285),c=s(66695),o=s(26126),d=s(6262),x=s(54481),m=s(72115),u=s(40133),h=s(91788),g=s(66516);function b(e){let{configuration:a,className:s=""}=e,[l,i]=(0,r.useState)(0),[b,p]=(0,r.useState)(1),[j,v]=(0,r.useState)(!1),f={black:"#000000",navy:"#1e3a8a",burgundy:"#7c2d12",forest:"#166534",purple:"#7c3aed",gray:"#4b5563",gold:"#fbbf24",silver:"#e5e7eb",white:"#ffffff",blue:"#3b82f6",red:"#ef4444"};return(0,t.jsx)(c.Zp,{className:"overflow-hidden ".concat(s),children:(0,t.jsxs)(c.Wu,{className:"p-0",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(o.E,{variant:"outline",className:"arabic-text",children:"معاينة مباشرة"})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{p(e=>Math.max(e-.2,.5))},children:(0,t.jsx)(d.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{p(e=>Math.min(e+.2,2))},children:(0,t.jsx)(x.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{v(!0),i(e=>e+90),setTimeout(()=>v(!1),500)},children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{i(0),p(1)},children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"relative aspect-square bg-gradient-to-br from-gray-100 via-white to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-800 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center transition-transform duration-500 ".concat(j?"animate-pulse":""),style:{transform:"rotate(".concat(l,"deg) scale(").concat(b,")"),transformOrigin:"center"},children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"w-32 h-40 rounded-t-full relative",style:{backgroundColor:f[a.gown.color]||"#000000",opacity:"luxury"===a.gown.fabric?.9:.8},children:[(0,t.jsx)("div",{className:"absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-white/20 to-transparent rounded-t-full"}),(0,t.jsx)("div",{className:"absolute -left-6 top-4 w-12 h-16 rounded-full transform -rotate-12",style:{backgroundColor:f[a.gown.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -right-6 top-4 w-12 h-16 rounded-full transform rotate-12",style:{backgroundColor:f[a.gown.color]||"#000000"}}),a.accessories.hood&&(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 w-20 h-12 rounded-t-full border-2",style:{backgroundColor:f[a.gown.color]||"#000000",borderColor:f[a.cap.color]||"#000000"}})]}),(0,t.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,t.jsx)("div",{className:"w-16 h-4 rounded-full",style:{backgroundColor:f[a.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-20 h-20 border-4 border-gray-300",style:{backgroundColor:f[a.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-1 h-8 transform rotate-12",style:{backgroundColor:f[a.cap.tassel.color]||"#fbbf24"},children:(0,t.jsx)("div",{className:"absolute bottom-0 w-3 h-3 rounded-full",style:{backgroundColor:f[a.cap.tassel.color]||"#fbbf24"}})})]}),a.stole.enabled&&(0,t.jsx)("div",{className:"absolute top-8 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-32 rounded-full opacity-90",style:{backgroundColor:f[a.stole.color]||"#fbbf24"},children:a.stole.embroidery&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-yellow-200/50 to-transparent rounded-full"})})}),a.accessories.sash&&(0,t.jsx)("div",{className:"absolute top-12 left-0 w-full h-4 transform -rotate-12 opacity-80",style:{backgroundColor:"#ef4444"}}),a.accessories.medal&&(0,t.jsx)("div",{className:"absolute top-16 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-6 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 border-2 border-yellow-300"})})]})}),(0,t.jsx)("div",{className:"absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-pulse"}),(0,t.jsx)("div",{className:"absolute bottom-8 left-8 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce delay-300"}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse delay-500"})]}),(0,t.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-900 border-t",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الثوب:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:f[a.gown.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===a.gown.color?"أسود":"navy"===a.gown.color?"أزرق داكن":"burgundy"===a.gown.color?"بورجوندي":a.gown.color})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"القبعة:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:f[a.cap.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===a.cap.color?"أسود":"navy"===a.cap.color?"أزرق داكن":a.cap.color})]})]}),a.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الوشاح:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:f[a.stole.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"gold"===a.stole.color?"ذهبي":"silver"===a.stole.color?"فضي":a.stole.color}),a.stole.embroidery&&(0,t.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"مطرز"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تحميل"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"مشاركة"})]})]})]})]})})}var p=s(62523),j=s(85057),v=s(17313),f=s(38564),N=s(33127),y=s(47924),w=s(5196),k=s(51976);let C={classic:{name:"كلاسيكي",icon:"\uD83C\uDFA9",description:"الألوان التقليدية الأنيقة"},modern:{name:"عصري",icon:"✨",description:"ألوان معاصرة وجريئة"},premium:{name:"فاخر",icon:"\uD83D\uDC8E",description:"ألوان راقية ومميزة"}};function z(e){var a,s,l;let{title:i,selectedColor:d,onColorChange:x,colors:m,showCategories:u=!0,showSearch:h=!1,allowCustom:g=!1,className:b=""}=e,[z,A]=(0,r.useState)(""),[S,_]=(0,r.useState)("all"),[J,$]=(0,r.useState)("#000000"),[L,Z]=(0,r.useState)([]),X=m.filter(e=>{let a=e.name.toLowerCase().includes(z.toLowerCase()),s="all"===S||e.category===S;return a&&s}),E=e=>{Z(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},R=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return Array.from({length:5},(a,s)=>(0,t.jsx)(f.A,{className:"h-3 w-3 ".concat(s<e?"text-yellow-400 fill-current":"text-gray-300")},s))};return(0,t.jsxs)(c.Zp,{className:b,children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),i]}),h&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(p.p,{placeholder:"ابحث عن لون...",value:z,onChange:e=>A(e.target.value),className:"pl-10 arabic-text"})]})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[u&&(0,t.jsx)(v.tU,{value:S,onValueChange:_,children:(0,t.jsxs)(v.j7,{className:"category-grid grid w-full grid-cols-4",children:[(0,t.jsx)(v.Xi,{value:"all",className:"arabic-text",children:"الكل"}),Object.entries(C).map(e=>{let[a,s]=e;return(0,t.jsxs)(v.Xi,{value:a,className:"arabic-text",children:[(0,t.jsx)("span",{className:"mr-1",children:s.icon}),s.name]},a)})]})}),(0,t.jsx)("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3",children:X.map(e=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{onClick:()=>x(e.value),className:"relative w-full aspect-square rounded-lg border-3 transition-all duration-200 hover:scale-105 hover:shadow-lg ".concat(d===e.value?"border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800":"border-gray-200 dark:border-gray-700 hover:border-gray-300"),style:{backgroundColor:e.hex},children:[d===e.value&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-900 rounded-full p-1",children:(0,t.jsx)(w.A,{className:"h-4 w-4 text-blue-600"})})}),e.isNew&&(0,t.jsx)(o.E,{className:"absolute -top-2 -right-2 bg-green-500 text-xs px-1 py-0",children:"جديد"})]}),(0,t.jsx)("div",{onClick:a=>{a.stopPropagation(),E(e.value)},className:"absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10",role:"button",tabIndex:0,onKeyDown:a=>{("Enter"===a.key||" "===a.key)&&(a.preventDefault(),a.stopPropagation(),E(e.value))},children:(0,t.jsx)(k.A,{className:"h-3 w-3 ".concat(L.includes(e.value)?"text-red-500 fill-current":"text-white drop-shadow-lg")})}),(0,t.jsxs)("div",{className:"mt-2 text-center",children:[(0,t.jsx)("div",{className:"text-xs font-medium arabic-text truncate",children:e.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500 uppercase",children:e.hex}),e.popularity&&(0,t.jsx)("div",{className:"flex justify-center gap-0.5 mt-1",children:R(e.popularity)})]})]},e.value))}),g&&(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(j.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"لون مخصص"}),(0,t.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,t.jsx)("input",{type:"color",value:J,onChange:e=>$(e.target.value),className:"w-12 h-12 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(p.p,{value:J,onChange:e=>$(e.target.value),placeholder:"#000000",className:"font-mono"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>x(J),className:"arabic-text",children:"تطبيق"})]})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(j.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"تركيبات شائعة"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("button",{onClick:()=>x("black"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-black border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-yellow-400 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"كلاسيكي"})]}),(0,t.jsxs)("button",{onClick:()=>x("navy"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-blue-900 border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-300 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"أنيق"})]})]})]}),d&&(0,t.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full border-2 border-white shadow-sm",style:{backgroundColor:null==(a=m.find(e=>e.value===d))?void 0:a.hex}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:null==(s=m.find(e=>e.value===d))?void 0:s.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:null==(l=m.find(e=>e.value===d))?void 0:l.hex})]})]})})]})]})}var A=s(88539),S=s(54165),_=s(10488),J=s(18175),$=s(75684),L=s(71366),Z=s(4229),X=s(24357),E=s(97939),R=s(81304);function U(e){let{designData:a,designName:s="تصميمي المخصص",onSave:l,onShare:i,className:o=""}=e,[d,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),[b,v]=(0,r.useState)(s),[f,N]=(0,r.useState)(""),[y,w]=(0,r.useState)(!1),[C]=(0,r.useState)("https://graduation-toqs.com/design/".concat(Date.now())),z=async()=>{if(b.trim()){w(!0);try{await (null==l?void 0:l(b,f)),x(!1)}catch(e){}finally{w(!1)}}},U=e=>{null==i||i(e);let a="شاهد تصميم زي التخرج المخصص الخاص بي على Graduation Toqs!";switch(e){case"facebook":window.open("https://www.facebook.com/sharer/sharer.php?u=".concat(encodeURIComponent(C)),"_blank");break;case"twitter":window.open("https://twitter.com/intent/tweet?text=".concat(encodeURIComponent(a),"&url=").concat(encodeURIComponent(C)),"_blank");break;case"whatsapp":window.open("https://wa.me/?text=".concat(encodeURIComponent(a+" "+C)),"_blank");break;case"copy":navigator.clipboard.writeText(C)}},F=e=>{console.log("Downloading design as ".concat(e))},B=[{id:"facebook",name:"Facebook",icon:(0,t.jsx)(_.A,{className:"h-5 w-5"}),color:"bg-blue-600 hover:bg-blue-700"},{id:"twitter",name:"Twitter",icon:(0,t.jsx)(J.A,{className:"h-5 w-5"}),color:"bg-sky-500 hover:bg-sky-600"},{id:"instagram",name:"Instagram",icon:(0,t.jsx)($.A,{className:"h-5 w-5"}),color:"bg-pink-600 hover:bg-pink-700"},{id:"whatsapp",name:"WhatsApp",icon:(0,t.jsx)(L.A,{className:"h-5 w-5"}),color:"bg-green-600 hover:bg-green-700"}];return(0,t.jsxs)("div",{className:"space-y-4 ".concat(o),children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)(S.lG,{open:d,onOpenChange:x,children:[(0,t.jsx)(S.zM,{asChild:!0,children:(0,t.jsxs)(n.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"حفظ التصميم"]})}),(0,t.jsxs)(S.Cf,{children:[(0,t.jsxs)(S.c7,{children:[(0,t.jsx)(S.L3,{className:"arabic-text",children:"حفظ التصميم"}),(0,t.jsx)(S.rr,{className:"arabic-text",children:"احفظ تصميمك المخصص لتتمكن من الوصول إليه لاحقاً"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{htmlFor:"design-title",className:"arabic-text",children:"اسم التصميم"}),(0,t.jsx)(p.p,{id:"design-title",value:b,onChange:e=>v(e.target.value),placeholder:"أدخل اسم التصميم",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{htmlFor:"design-description",className:"arabic-text",children:"وصف التصميم (اختياري)"}),(0,t.jsx)(A.T,{id:"design-description",value:f,onChange:e=>N(e.target.value),placeholder:"أضف وصفاً لتصميمك...",className:"arabic-text",rows:3})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{onClick:z,disabled:!b.trim()||y,className:"flex-1 arabic-text",children:y?"جاري الحفظ...":"حفظ"}),(0,t.jsx)(n.$,{variant:"outline",onClick:()=>x(!1),className:"arabic-text",children:"إلغاء"})]})]})]})]}),(0,t.jsxs)(S.lG,{open:m,onOpenChange:u,children:[(0,t.jsx)(S.zM,{asChild:!0,children:(0,t.jsxs)(n.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"مشاركة"]})}),(0,t.jsxs)(S.Cf,{children:[(0,t.jsxs)(S.c7,{children:[(0,t.jsx)(S.L3,{className:"arabic-text",children:"مشاركة التصميم"}),(0,t.jsx)(S.rr,{className:"arabic-text",children:"شارك تصميمك المميز مع الأصدقاء والعائلة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"رابط التصميم"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.p,{value:C,readOnly:!0,className:"flex-1"}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>U("copy"),children:(0,t.jsx)(X.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text mb-3 block",children:"مشاركة على وسائل التواصل"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:B.map(e=>(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>U(e.id),className:"".concat(e.color," text-white border-0 arabic-text"),children:[e.icon,(0,t.jsx)("span",{className:"mr-2",children:e.name})]},e.id))})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,t.jsx)(E.A,{className:"h-16 w-16 text-gray-400"})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"رمز QR للمشاركة السريعة"})]})]})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"text-lg arabic-text",children:"تحميل التصميم"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"احصل على نسخة من تصميمك بصيغ مختلفة"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:[{format:"png",name:"صورة PNG",description:"جودة عالية للطباعة"},{format:"jpg",name:"صورة JPG",description:"حجم أصغر للمشاركة"},{format:"pdf",name:"ملف PDF",description:"للطباعة الاحترافية"},{format:"svg",name:"ملف SVG",description:"قابل للتحرير"}].map(e=>(0,t.jsxs)("button",{onClick:()=>F(e.format),className:"flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]}),(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})]},e.format))}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>{window.print()},className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"طباعة التصميم"]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"text-lg arabic-text",children:"إحصائيات التصميم"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"12"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"مشاهدة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"3"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إعجاب"})]})]})})]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"إضافة للمفضلة"]})]})}var F=s(59409),B=s(80333),D=s(27809),M=s(6752),P=s(17951),T=s(17608),W=s(53311);let q={gown:{color:"black",style:"classic",size:"M",fabric:"premium"},cap:{color:"black",style:"traditional",tassel:{color:"gold",style:"classic"}},stole:{enabled:!1,color:"gold",pattern:"plain",text:"",embroidery:!1},accessories:{hood:!1,sash:!1,medal:!1}},G={colors:[{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:5},{name:"أزرق داكن",value:"navy",hex:"#1e3a8a",category:"classic",popularity:4},{name:"بورجوندي",value:"burgundy",hex:"#7c2d12",category:"premium",popularity:3},{name:"أخضر داكن",value:"forest",hex:"#166534",category:"modern",popularity:2},{name:"بنفسجي",value:"purple",hex:"#7c3aed",category:"modern",popularity:3,isNew:!0},{name:"رمادي",value:"gray",hex:"#4b5563",category:"classic",popularity:3}],tasselColors:[{name:"ذهبي",value:"gold",hex:"#fbbf24",category:"classic",popularity:5},{name:"فضي",value:"silver",hex:"#e5e7eb",category:"premium",popularity:4},{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:4},{name:"أبيض",value:"white",hex:"#ffffff",category:"classic",popularity:3},{name:"أزرق",value:"blue",hex:"#3b82f6",category:"modern",popularity:2},{name:"أحمر",value:"red",hex:"#ef4444",category:"modern",popularity:2,isNew:!0}],gownStyles:[{name:"كلاسيكي",value:"classic",description:"التصميم التقليدي الأنيق"},{name:"عصري",value:"modern",description:"تصميم معاصر مع لمسات حديثة"},{name:"فاخر",value:"luxury",description:"تصميم راقي مع تفاصيل مميزة"}],fabrics:[{name:"قياسي",value:"standard",price:0},{name:"مميز",value:"premium",price:50},{name:"فاخر",value:"luxury",price:100}],sizes:["XS","S","M","L","XL","XXL"]};function I(){let{t:e}=(0,l.B)(),[a,s]=(0,r.useState)(q),[d,x]=(0,r.useState)("gown"),[m,h]=(0,r.useState)(299.99),g=(e,a)=>{s(s=>({...s,[e]:{...s[e],...a}}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:"\uD83C\uDFA8 تخصيص زي التخرج"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 arabic-text",children:"صمم زي التخرج المثالي الذي يعكس شخصيتك"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-6",children:(0,t.jsxs)("div",{className:"sticky top-24",children:[(0,t.jsx)(b,{configuration:a,className:"mb-6"}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"ملخص السعر"})}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الثوب الأساسي:"}),(0,t.jsx)("span",{children:"299 درهم"})]}),a.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الوشاح:"}),(0,t.jsx)("span",{children:"50 درهم"})]}),a.accessories.hood&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"غطاء الرأس:"}),(0,t.jsx)("span",{children:"30 درهم"})]})]}),(0,t.jsx)("div",{className:"border-t pt-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-semibold arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-blue-600",children:[m," درهم"]})]})}),(0,t.jsxs)(n.$,{className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"إضافة للسلة"]})]})]}),(0,t.jsx)(U,{designData:a,designName:"تصميم زي التخرج المخصص",onSave:(e,a)=>{console.log("Saving design:",e,a)},onShare:e=>{console.log("Sharing on:",e)}})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"خيارات التخصيص"}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{s(q)},children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"إعادة تعيين"]})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(v.tU,{value:d,onValueChange:x,children:[(0,t.jsxs)(v.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(v.Xi,{value:"gown",className:"arabic-text",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"الثوب"]}),(0,t.jsxs)(v.Xi,{value:"cap",className:"arabic-text",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"القبعة"]}),(0,t.jsxs)(v.Xi,{value:"stole",className:"arabic-text",children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"الوشاح"]}),(0,t.jsxs)(v.Xi,{value:"accessories",className:"arabic-text",children:[(0,t.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"الإكسسوارات"]})]}),(0,t.jsxs)(v.av,{value:"gown",className:"space-y-6 mt-6",children:[(0,t.jsx)(z,{title:"لون الثوب",colors:G.colors,selectedColor:a.gown.color,onColorChange:e=>g("gown",{color:e}),showCategories:!0,showSearch:!0,allowCustom:!0}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"نمط الثوب"}),(0,t.jsx)("div",{className:"grid gap-3",children:G.gownStyles.map(e=>(0,t.jsxs)("button",{onClick:()=>g("gown",{style:e.value}),className:"p-4 rounded-lg border-2 text-left transition-colors ".concat(a.gown.style===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"),children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]},e.value))})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"المقاس"}),(0,t.jsxs)(F.l6,{value:a.gown.size,onValueChange:e=>g("gown",{size:e}),children:[(0,t.jsx)(F.bq,{children:(0,t.jsx)(F.yv,{})}),(0,t.jsx)(F.gC,{children:G.sizes.map(e=>(0,t.jsx)(F.eb,{value:e,children:e},e))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"نوع القماش"}),(0,t.jsx)("div",{className:"grid gap-3",children:G.fabrics.map(e=>(0,t.jsxs)("button",{onClick:()=>g("gown",{fabric:e.value}),className:"p-4 rounded-lg border-2 flex justify-between items-center transition-colors ".concat(a.gown.fabric===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"),children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:e.name}),e.price>0&&(0,t.jsxs)(o.E,{variant:"secondary",children:["+",e.price," درهم"]})]},e.value))})]})]}),(0,t.jsxs)(v.av,{value:"cap",className:"space-y-6 mt-6",children:[(0,t.jsx)(z,{title:"لون القبعة",colors:G.colors,selectedColor:a.cap.color,onColorChange:e=>g("cap",{color:e}),showCategories:!0}),(0,t.jsx)(z,{title:"لون الشرابة",colors:G.tasselColors,selectedColor:a.cap.tassel.color,onColorChange:e=>g("cap",{tassel:{...a.cap.tassel,color:e}}),showCategories:!1})]}),(0,t.jsxs)(v.av,{value:"stole",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(B.d,{id:"stole-enabled",checked:a.stole.enabled,onCheckedChange:e=>g("stole",{enabled:e})}),(0,t.jsx)(j.J,{htmlFor:"stole-enabled",className:"arabic-text",children:"إضافة وشاح التخرج"})]}),a.stole.enabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(z,{title:"لون الوشاح",colors:G.tasselColors,selectedColor:a.stole.color,onColorChange:e=>g("stole",{color:e}),showCategories:!1}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(B.d,{id:"stole-embroidery",checked:a.stole.embroidery,onCheckedChange:e=>g("stole",{embroidery:e})}),(0,t.jsx)(j.J,{htmlFor:"stole-embroidery",className:"arabic-text",children:"تطريز مخصص (+50 درهم)"})]})]})]}),(0,t.jsx)(v.av,{value:"accessories",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"غطاء الرأس الأكاديمي"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للدرجات العليا (+30 درهم)"})]}),(0,t.jsx)(B.d,{checked:a.accessories.hood,onCheckedChange:e=>g("accessories",{hood:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"حزام الشرف"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للمتفوقين (+25 درهم)"})]}),(0,t.jsx)(B.d,{checked:a.accessories.sash,onCheckedChange:e=>g("accessories",{sash:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"ميدالية التخرج"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"تذكار مميز (+40 درهم)"})]}),(0,t.jsx)(B.d,{checked:a.accessories.medal,onCheckedChange:e=>g("accessories",{medal:e})})]})]})})]})})]})})]})]})]})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>x,Es:()=>u,L3:()=>h,c7:()=>m,lG:()=>n,rr:()=>g,zM:()=>c});var t=s(95155);s(12115);var r=s(15452),l=s(54416),i=s(59434);function n(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function o(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function x(e){let{className:a,children:s,showCloseButton:n=!0,...c}=e;return(0,t.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...c,children:[s,n&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...s})}function g(e){let{className:a,...s}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...s})}},59409:(e,a,s)=>{"use strict";s.d(a,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>o,yv:()=>d});var t=s(95155);s(12115);var r=s(22918),l=s(66474),i=s(5196),n=s(47863),c=s(59434);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"select",...a})}function d(e){let{...a}=e;return(0,t.jsx)(r.WT,{"data-slot":"select-value",...a})}function x(e){let{className:a,size:s="default",children:i,...n}=e;return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[i,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:a,children:s,position:l="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...i,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,t.jsx)(g,{})]})})}function u(e){let{className:a,children:s,...l}=e;return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...l,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:s})]})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(n.A,{className:"size-4"})})}function g(e){let{className:a,...s}=e;return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(l.A,{className:"size-4"})})}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>l});var t=s(95155);s(12115);var r=s(59434);function l(e){let{className:a,type:s,...l}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...l})}},78862:(e,a,s)=>{Promise.resolve().then(s.bind(s,23517))},80333:(e,a,s)=>{"use strict";s.d(a,{d:()=>i});var t=s(95155);s(12115);var r=s(4884),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(95155);s(12115);var r=s(40968),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(95155);s(12115);var r=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,1672,1056,1686,2632,7443,7784,8441,1684,7358],()=>a(78862)),_N_E=e.O()}]);