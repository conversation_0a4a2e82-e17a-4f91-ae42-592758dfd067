"use strict";exports.id=3044,exports.ids=[3044],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>X,Hs:()=>w,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Y,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),l=r(96963),i=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),y=r(8730),v=r(60687),x="Dialog",[b,w]=(0,s.A)(x),[D,j]=b(x),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,i.i)({prop:o,defaultProp:a??!1,onChange:s,caller:x});return(0,v.jsx)(D,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=x;var C="DialogTrigger",k=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=j(C,r),l=(0,a.s)(t,s.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Z(s.open),...n,ref:l,onClick:(0,o.m)(e.onClick,s.onOpenToggle)})});k.displayName=C;var I="DialogPortal",[E,N]=b(I,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,s=j(I,t);return(0,v.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=I;var P="DialogOverlay",F=n.forwardRef((e,t)=>{let r=N(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(P,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(_,{...o,ref:t})}):null});F.displayName=P;var A=(0,y.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(P,r);return(0,v.jsx)(m.A,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":Z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),M="DialogContent",G=n.forwardRef((e,t)=>{let r=N(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(M,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(S,{...o,ref:t}):(0,v.jsx)(T,{...o,ref:t})})});G.displayName=M;var S=n.forwardRef((e,t)=>{let r=j(M,e.__scopeDialog),s=n.useRef(null),l=(0,a.s)(t,r.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=j(M,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,c=j(M,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...i,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:c.titleId}),(0,v.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(L,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});$.displayName=L;var q="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(q,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});H.displayName=q;var V="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(V,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}W.displayName=V;var U="DialogTitleWarning",[X,z]=(0,s.q)(U,{contentName:M,titleName:L,docsSlug:"dialog"}),J=({titleId:e})=>{let t=z(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},K=({contentRef:e,descriptionId:t})=>{let r=z("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Y=R,Q=k,ee=O,et=F,er=G,en=$,eo=H,ea=W},78148:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(43210),o=r(14163),a=r(60687),s=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},88233:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90270:(e,t,r)=>{r.d(t,{bL:()=>D,zi:()=>j});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),l=r(65551),i=r(83721),d=r(18853),u=r(14163),c=r(60687),p="Switch",[f,g]=(0,s.A)(p),[m,h]=f(p),y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:s,checked:i,defaultChecked:d,required:f,disabled:g,value:h="on",onCheckedChange:y,form:v,...x}=e,[D,j]=n.useState(null),R=(0,a.s)(t,e=>j(e)),C=n.useRef(!1),k=!D||v||!!D.closest("form"),[I,E]=(0,l.i)({prop:i,defaultProp:d??!1,onChange:y,caller:p});return(0,c.jsxs)(m,{scope:r,checked:I,disabled:g,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":I,"aria-required":f,"data-state":w(I),"data-disabled":g?"":void 0,disabled:g,value:h,...x,ref:R,onClick:(0,o.m)(e.onClick,e=>{E(e=>!e),k&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),k&&(0,c.jsx)(b,{control:D,bubbles:!C.current,name:s,value:h,checked:I,required:f,disabled:g,form:v,style:{transform:"translateX(-100%)"}})]})});y.displayName=p;var v="SwitchThumb",x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=h(v,r);return(0,c.jsx)(u.sG.span,{"data-state":w(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});x.displayName=v;var b=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:o=!0,...s},l)=>{let u=n.useRef(null),p=(0,a.s)(u,l),f=(0,i.Z)(r),g=(0,d.X)(t);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[f,r,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:p,style:{...s.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var D=y,j=x}};