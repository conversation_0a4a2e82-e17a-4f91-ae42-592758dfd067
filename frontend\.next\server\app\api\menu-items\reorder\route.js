"use strict";(()=>{var e={};e.id=2076,e.ids=[2076],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49079:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{PUT:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(38561);async function p(e){try{let{items:r}=await e.json();if(!r||!Array.isArray(r))return i.NextResponse.json({error:"قائمة العناصر مطلوبة"},{status:400});let t=u.CN.getMenuItems();return r.forEach((e,r)=>{let s=t.findIndex(r=>r.id===e.id);-1!==s&&(t[s].order_index=r+1,t[s].updated_at=new Date().toISOString())}),u.CN.saveMenuItems(t),i.NextResponse.json({message:"تم تحديث ترتيب القائمة بنجاح"})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/menu-items/reorder/route",pathname:"/api/menu-items/reorder",filename:"route",bundlePath:"app/api/menu-items/reorder/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\reorder\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:c,serverHooks:m}=d;function l(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:c})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(49079));module.exports=s})();