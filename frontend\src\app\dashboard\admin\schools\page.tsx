'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  ArrowLeft,
  School,
  Users,
  MapPin,
  Phone,
  Mail,
  Globe,
  Calendar,
  Filter,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { MockSchool } from '@/lib/mockData'
import SchoolForm from '@/components/admin/SchoolForm'

interface SchoolStats {
  total: number
  active: number
  inactive: number
  totalStudents: number
  averageStudents: number
}

export default function SchoolsManagement() {
  const [schools, setSchools] = useState<MockSchool[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    active: 0,
    inactive: 0,
    totalStudents: 0,
    averageStudents: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [cityFilter, setCityFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingSchool, setEditingSchool] = useState<MockSchool | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // جلب المدارس
  const fetchSchools = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        include_inactive: 'true',
        search: searchTerm,
        city: cityFilter === 'all' ? '' : cityFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      })

      const response = await fetch(`/api/schools?${params}`)
      const data = await response.json()

      if (response.ok) {
        setSchools(data.schools)
        setStats(data.stats)
      } else {
        console.error('Error fetching schools:', data.error)
      }
    } catch (error) {
      console.error('Error fetching schools:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSchools()
  }, [searchTerm, cityFilter, statusFilter, sortBy, sortOrder])

  // فلترة المدارس حسب الحالة
  const filteredSchools = schools.filter(school => {
    if (statusFilter === 'active') return school.is_active
    if (statusFilter === 'inactive') return !school.is_active
    return true
  })

  // إضافة أو تحديث مدرسة
  const handleSubmit = async (schoolData: Partial<MockSchool>) => {
    try {
      setIsSubmitting(true)
      
      const url = editingSchool ? `/api/schools/${editingSchool.id}` : '/api/schools'
      const method = editingSchool ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(schoolData),
      })

      const data = await response.json()

      if (response.ok) {
        await fetchSchools()
        setIsDialogOpen(false)
        setEditingSchool(null)
      } else {
        alert(data.error || 'حدث خطأ أثناء حفظ المدرسة')
      }
    } catch (error) {
      console.error('Error submitting school:', error)
      alert('حدث خطأ أثناء حفظ المدرسة')
    } finally {
      setIsSubmitting(false)
    }
  }

  // حذف مدرسة
  const handleDelete = async (school: MockSchool) => {
    if (!confirm(`هل أنت متأكد من حذف مدرسة "${school.name}"؟`)) {
      return
    }

    try {
      const response = await fetch(`/api/schools/${school.id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (response.ok) {
        await fetchSchools()
      } else {
        alert(data.error || 'حدث خطأ أثناء حذف المدرسة')
      }
    } catch (error) {
      console.error('Error deleting school:', error)
      alert('حدث خطأ أثناء حذف المدرسة')
    }
  }

  // تغيير حالة النشاط
  const toggleSchoolStatus = async (school: MockSchool) => {
    try {
      const response = await fetch(`/api/schools/${school.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...school,
          is_active: !school.is_active
        }),
      })

      const data = await response.json()

      if (response.ok) {
        await fetchSchools()
      } else {
        alert(data.error || 'حدث خطأ أثناء تحديث حالة المدرسة')
      }
    } catch (error) {
      console.error('Error updating school status:', error)
      alert('حدث خطأ أثناء تحديث حالة المدرسة')
    }
  }

  const resetForm = () => {
    setEditingSchool(null)
  }

  const openEditDialog = (school: MockSchool) => {
    setEditingSchool(school)
    setIsDialogOpen(true)
  }

  // الحصول على قائمة المدن الفريدة
  const uniqueCities = Array.from(new Set(schools.map(s => s.city).filter(Boolean)))

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة المدارس 🏫
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة المدارس المسجلة والشراكات التعليمية
              </p>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مدرسة جديدة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="arabic-text">
                    {editingSchool ? 'تحديث المدرسة' : 'إضافة مدرسة جديدة'}
                  </DialogTitle>
                </DialogHeader>
                <SchoolForm
                  school={editingSchool || undefined}
                  onSubmit={handleSubmit}
                  onCancel={() => setIsDialogOpen(false)}
                  isLoading={isSubmitting}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي المدارس</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
                </div>
                <School className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">المدارس النشطة</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="h-4 w-4 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">المدارس غير النشطة</p>
                  <p className="text-2xl font-bold text-red-600">{stats.inactive}</p>
                </div>
                <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                  <div className="h-4 w-4 bg-red-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي الطلاب</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.totalStudents.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط الطلاب</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.averageStudents.toLocaleString()}</p>
                </div>
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-orange-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="arabic-text flex items-center gap-2">
              <Filter className="h-5 w-5" />
              البحث والفلترة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المدارس..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={cityFilter} onValueChange={setCityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع المدن" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المدن</SelectItem>
                  {uniqueCities.map((city) => (
                    <SelectItem key={city} value={city!}>{city}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشطة</SelectItem>
                  <SelectItem value="inactive">غير نشطة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">تاريخ الإنشاء</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="student_count">عدد الطلاب</SelectItem>
                  <SelectItem value="city">المدينة</SelectItem>
                  <SelectItem value="graduation_date">تاريخ التخرج</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="flex items-center gap-2"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                {sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* جدول المدارس */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">قائمة المدارس ({filteredSchools.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400 arabic-text">جاري التحميل...</p>
              </div>
            ) : filteredSchools.length === 0 ? (
              <div className="text-center py-8">
                <School className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 arabic-text">لا توجد مدارس مطابقة للبحث</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-4 arabic-text">المدرسة</th>
                      <th className="text-right p-4 arabic-text">المدينة</th>
                      <th className="text-right p-4 arabic-text">عدد الطلاب</th>
                      <th className="text-right p-4 arabic-text">تاريخ التخرج</th>
                      <th className="text-right p-4 arabic-text">الحالة</th>
                      <th className="text-right p-4 arabic-text">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSchools.map((school) => (
                      <tr key={school.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="p-4">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white arabic-text">{school.name}</p>
                            {school.name_en && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">{school.name_en}</p>
                            )}
                            {school.email && (
                              <div className="flex items-center gap-1 mt-1">
                                <Mail className="h-3 w-3 text-gray-400" />
                                <p className="text-xs text-gray-500">{school.email}</p>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-900 dark:text-white arabic-text">{school.city || 'غير محدد'}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-900 dark:text-white">{school.student_count.toLocaleString()}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          {school.graduation_date ? (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-900 dark:text-white">
                                {new Date(school.graduation_date).toLocaleDateString('ar-AE')}
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-500 arabic-text">غير محدد</span>
                          )}
                        </td>
                        <td className="p-4">
                          <Badge 
                            variant={school.is_active ? "default" : "secondary"}
                            className="cursor-pointer"
                            onClick={() => toggleSchoolStatus(school)}
                          >
                            {school.is_active ? 'نشطة' : 'غير نشطة'}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(school)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(school)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
