"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5486],{6101:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>u});var r=n(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function i(...e){return r.useCallback(u(...e),e)}},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),l=n(6101),u=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[l,i]=r.useState(),a=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=o(a.current);c.current="mounted"===d?e:"none"},[d]),(0,u.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,l=o(t);e?f("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,u.N)(()=>{if(l){var e;let t,n=null!=(e=l.ownerDocument.defaultView)?e:window,r=e=>{let r=o(a.current).includes(e.animationName);if(e.target===l&&r&&(f("ANIMATION_END"),!s.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},u=e=>{e.target===l&&(c.current=o(a.current))};return l.addEventListener("animationstart",u),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{n.clearTimeout(t),l.removeEventListener("animationstart",u),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,l.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,l=r&&"isReactWarning"in r&&r.isReactWarning;return l?e.ref:(l=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:s}):null};function o(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},39033:(e,t,n)=>{n.d(t,{c:()=>l});var r=n(12115);function l(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>u});var r=n(12115),l=n(95155);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,l.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(l){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),o=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,s=n?.[e]?.[o]||i,c=r.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:u})};return a.displayName=t+"Provider",[a,function(n,l){let a=l?.[e]?.[o]||i,s=r.useContext(a);if(s)return s;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(u,...t)]}},52712:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(12115),l=globalThis?.document?r.useLayoutEffect:()=>{}},63655:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>o});var r=n(12115),l=n(47650),u=n(99708),i=n(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...u}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l?n:t,{...u,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return u(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:o}=t,a=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let u=l(t)||l(r);return i[e][u]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return u(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...s}[t]):({...o,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},94315:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(12115);n(95155);var l=r.createContext(void 0);function u(e){let t=r.useContext(l);return e||t||"ltr"}},99708:(e,t,n)=>{n.d(t,{DX:()=>o,Dc:()=>s,TL:()=>i});var r=n(12115),l=n(6101),u=n(95155);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...u}=e;if(r.isValidElement(n)){var i;let e,o,a=(i=n,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],u=t[r];/^on[A-Z]/.test(r)?l&&u?n[r]=(...e)=>{let t=u(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...u}:"className"===r&&(n[r]=[l,u].filter(Boolean).join(" "))}return{...e,...n}}(u,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,l.t)(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...i}=e,o=r.Children.toArray(l),a=o.find(c);if(a){let e=a.props.children,l=o.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,u.jsx)(t,{...i,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var o=i("Slot"),a=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);