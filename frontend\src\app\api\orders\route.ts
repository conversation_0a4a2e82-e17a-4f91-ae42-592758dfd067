import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockOrder } from '@/lib/mockData'

// GET - جلب جميع الطلبات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const customer = searchParams.get('customer')
    const school = searchParams.get('school')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const sortBy = searchParams.get('sort_by') || 'created_at'
    const sortOrder = searchParams.get('sort_order') || 'desc'

    // جلب البيانات الوهمية
    let orders = MockDataManager.getOrders()

    // تطبيق الفلاتر
    if (status && status !== 'all') {
      orders = orders.filter(order => order.status === status)
    }

    if (customer) {
      orders = orders.filter(order => 
        order.customer_name.toLowerCase().includes(customer.toLowerCase()) ||
        order.customer_email.toLowerCase().includes(customer.toLowerCase()) ||
        order.order_number.toLowerCase().includes(customer.toLowerCase())
      )
    }

    if (school && school !== 'all') {
      orders = orders.filter(order => order.school_id === school)
    }

    if (dateFrom) {
      orders = orders.filter(order => order.created_at >= dateFrom)
    }

    if (dateTo) {
      orders = orders.filter(order => order.created_at <= dateTo)
    }

    // ترتيب النتائج
    orders.sort((a, b) => {
      let aValue: any = a[sortBy as keyof MockOrder]
      let bValue: any = b[sortBy as keyof MockOrder]

      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })

    // تطبيق pagination
    const totalOrders = orders.length
    if (limit && offset) {
      const limitNum = parseInt(limit)
      const offsetNum = parseInt(offset)
      orders = orders.slice(offsetNum, offsetNum + limitNum)
    }

    // حساب الإحصائيات
    const allOrders = MockDataManager.getOrders()
    const stats = {
      total: allOrders.length,
      pending: allOrders.filter(o => o.status === 'pending').length,
      confirmed: allOrders.filter(o => o.status === 'confirmed').length,
      in_production: allOrders.filter(o => o.status === 'in_production').length,
      shipped: allOrders.filter(o => o.status === 'shipped').length,
      delivered: allOrders.filter(o => o.status === 'delivered').length,
      cancelled: allOrders.filter(o => o.status === 'cancelled').length,
      total_revenue: allOrders
        .filter(o => o.payment_status === 'paid')
        .reduce((sum, o) => sum + o.total, 0),
      pending_payments: allOrders
        .filter(o => o.payment_status === 'pending')
        .reduce((sum, o) => sum + o.total, 0)
    }

    return NextResponse.json({ 
      orders,
      total: totalOrders,
      stats
    })
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب الطلبات' },
      { status: 500 }
    )
  }
}

// POST - إضافة طلب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_id,
      customer_name,
      customer_email,
      customer_phone,
      items,
      shipping_address,
      payment_method,
      notes,
      school_id,
      school_name
    } = body

    // التحقق من البيانات المطلوبة
    if (!customer_name || !customer_email || !items || items.length === 0 || !shipping_address) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // حساب المبالغ
    const subtotal = items.reduce((sum: number, item: any) => sum + (item.unit_price * item.quantity), 0)
    const tax = subtotal * 0.05 // ضريبة 5%
    const shipping_cost = subtotal > 500 ? 0 : 25 // شحن مجاني للطلبات أكثر من 500 درهم
    const total = subtotal + tax + shipping_cost

    // جلب الطلبات الحالية
    const orders = MockDataManager.getOrders()

    // إنشاء الطلب الجديد
    const newOrder: MockOrder = {
      id: MockDataManager.generateId(),
      order_number: MockDataManager.generateOrderNumber(),
      customer_id: customer_id || MockDataManager.generateId(),
      customer_name,
      customer_email,
      customer_phone,
      status: 'pending',
      items: items.map((item: any) => ({
        ...item,
        id: MockDataManager.generateId(),
        order_id: MockDataManager.generateId()
      })),
      subtotal,
      tax,
      shipping_cost,
      total,
      payment_status: 'pending',
      payment_method,
      shipping_address,
      notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      school_id,
      school_name
    }

    // حفظ الطلب
    orders.push(newOrder)
    MockDataManager.saveOrders(orders)

    return NextResponse.json({ 
      message: 'تم إنشاء الطلب بنجاح',
      order: newOrder 
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: 'خطأ في إنشاء الطلب' },
      { status: 500 }
    )
  }
}
