"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8432],{14186:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},16785:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},27809:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},33109:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},37108:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},38564:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},42103:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]])},53904:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},55863:(e,a,t)=>{t.d(a,{C1:()=>A,bL:()=>b});var r=t(12115),n=t(46081),l=t(63655),o=t(95155),d="Progress",[i,c]=(0,n.A)(d),[s,u]=i(d),p=r.forwardRef((e,a)=>{var t,r,n,d;let{__scopeProgress:i,value:c=null,max:u,getValueLabel:p=v,...y}=e;(u||0===u)&&!f(u)&&console.error((t="".concat(u),r="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=f(u)?u:100;null===c||x(c,h)||console.error((n="".concat(c),d="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(d,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=x(c,h)?c:null,A=m(b)?p(b,h):void 0;return(0,o.jsx)(s,{scope:i,value:b,max:h,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":m(b)?b:void 0,"aria-valuetext":A,role:"progressbar","data-state":k(b,h),"data-value":null!=b?b:void 0,"data-max":h,...y,ref:a})})});p.displayName=d;var y="ProgressIndicator",h=r.forwardRef((e,a)=>{var t;let{__scopeProgress:r,...n}=e,d=u(y,r);return(0,o.jsx)(l.sG.div,{"data-state":k(d.value,d.max),"data-value":null!=(t=d.value)?t:void 0,"data-max":d.max,...n,ref:a})});function v(e,a){return"".concat(Math.round(e/a*100),"%")}function k(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function m(e){return"number"==typeof e}function f(e){return m(e)&&!isNaN(e)&&e>0}function x(e,a){return m(e)&&!isNaN(e)&&e<=a&&e>=0}h.displayName=y;var b=p,A=h},55868:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},60704:(e,a,t)=>{t.d(a,{B8:()=>I,UC:()=>V,bL:()=>L,l9:()=>R});var r=t(12115),n=t(85185),l=t(46081),o=t(89196),d=t(28905),i=t(63655),c=t(94315),s=t(5845),u=t(61285),p=t(95155),y="Tabs",[h,v]=(0,l.A)(y,[o.RG]),k=(0,o.RG)(),[m,f]=h(y),x=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:n,defaultValue:l,orientation:o="horizontal",dir:d,activationMode:h="automatic",...v}=e,k=(0,c.jH)(d),[f,x]=(0,s.i)({prop:r,onChange:n,defaultProp:null!=l?l:"",caller:y});return(0,p.jsx)(m,{scope:t,baseId:(0,u.B)(),value:f,onValueChange:x,orientation:o,dir:k,activationMode:h,children:(0,p.jsx)(i.sG.div,{dir:k,"data-orientation":o,...v,ref:a})})});x.displayName=y;var b="TabsList",A=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...n}=e,l=f(b,t),d=k(t);return(0,p.jsx)(o.bL,{asChild:!0,...d,orientation:l.orientation,dir:l.dir,loop:r,children:(0,p.jsx)(i.sG.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:a})})});A.displayName=b;var g="TabsTrigger",M=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...d}=e,c=f(g,t),s=k(t),u=C(c.baseId,r),y=N(c.baseId,r),h=r===c.value;return(0,p.jsx)(o.q7,{asChild:!0,...s,focusable:!l,active:h,children:(0,p.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":y,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...d,ref:a,onMouseDown:(0,n.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||l||!e||c.onValueChange(r)})})})});M.displayName=g;var w="TabsContent",j=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,forceMount:l,children:o,...c}=e,s=f(w,t),u=C(s.baseId,n),y=N(s.baseId,n),h=n===s.value,v=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(d.C,{present:l||h,children:t=>{let{present:r}=t;return(0,p.jsx)(i.sG.div,{"data-state":h?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:y,tabIndex:0,...c,ref:a,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&o})}})});function C(e,a){return"".concat(e,"-trigger-").concat(a)}function N(e,a){return"".concat(e,"-content-").concat(a)}j.displayName=w;var L=x,I=A,R=M,V=j},68500:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},72713:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},91788:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);