(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3419],{4516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},14186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>i});var t=s(95155);s(12115);var r=s(55863),l=s(59434);function i(e){let{className:a,value:s,...i}=e;return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...i,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},28883:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},35742:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},37108:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},40646:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,a,s)=>{"use strict";s.d(a,{b:()=>c});var t=s(12115),r=s(63655),l=s(95155),i=t.forwardRef((e,a)=>(0,l.jsx)(r.sG.label,{...e,ref:a,onMouseDown:a=>{var s;a.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));i.displayName="Label";var c=i},55863:(e,a,s)=>{"use strict";s.d(a,{C1:()=>N,bL:()=>v});var t=s(12115),r=s(46081),l=s(63655),i=s(95155),c="Progress",[n,d]=(0,r.A)(c),[x,m]=n(c),o=t.forwardRef((e,a)=>{var s,t,r,c;let{__scopeProgress:n,value:d=null,max:m,getValueLabel:o=p,...u}=e;(m||0===m)&&!b(m)&&console.error((s="".concat(m),t="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=b(m)?m:100;null===d||y(d,h)||console.error((r="".concat(d),c="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(c,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let v=y(d,h)?d:null,N=j(v)?o(v,h):void 0;return(0,i.jsx)(x,{scope:n,value:v,max:h,children:(0,i.jsx)(l.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":j(v)?v:void 0,"aria-valuetext":N,role:"progressbar","data-state":g(v,h),"data-value":null!=v?v:void 0,"data-max":h,...u,ref:a})})});o.displayName=c;var u="ProgressIndicator",h=t.forwardRef((e,a)=>{var s;let{__scopeProgress:t,...r}=e,c=m(u,t);return(0,i.jsx)(l.sG.div,{"data-state":g(c.value,c.max),"data-value":null!=(s=c.value)?s:void 0,"data-max":c.max,...r,ref:a})});function p(e,a){return"".concat(Math.round(e/a*100),"%")}function g(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function j(e){return"number"==typeof e}function b(e){return j(e)&&!isNaN(e)&&e>0}function y(e,a){return j(e)&&!isNaN(e)&&e<=a&&e>=0}h.displayName=u;var v=o,N=h},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>l});var t=s(95155);s(12115);var r=s(59434);function l(e){let{className:a,type:s,...l}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...l})}},66516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},69074:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(95155);s(12115);var r=s(40968),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},85339:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92121:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>C});var t=s(95155),r=s(12115),l=s(40283),i=s(37784),c=s(66695),n=s(30285),d=s(62523),x=s(85057),m=s(26126),o=s(24944),u=s(40646),h=s(29799),p=s(14186),g=s(47924),j=s(85339),b=s(69074),y=s(37108),v=s(4516),N=s(19420),f=s(35742),k=s(91788),w=s(66516),A=s(28883);function C(){let{user:e,profile:a}=(0,l.A)(),[s,C]=(0,r.useState)(""),[Z,T]=(0,r.useState)(null),[D,M]=(0,r.useState)(!1),[R,q]=(0,r.useState)(""),S=async()=>{if(!s.trim())return void q("يرجى إدخال رقم الطلب أو رقم التتبع");M(!0),q(""),setTimeout(()=>{T({orderNumber:s,currentStatus:"في الطريق",estimatedDelivery:"2024-01-22T15:00:00Z",trackingNumber:"TRK-"+s,carrier:"شركة التوصيل السريع",progress:75,events:[{id:"1",status:"تم تأكيد الطلب",description:"تم استلام طلبك وتأكيده بنجاح",location:"مركز المعالجة - دبي",timestamp:"2024-01-20T09:00:00Z",isCompleted:!0},{id:"2",status:"قيد التحضير",description:"جاري تحضير منتجاتك للشحن",location:"مستودع الإنتاج - دبي",timestamp:"2024-01-20T14:30:00Z",isCompleted:!0},{id:"3",status:"تم الشحن",description:"تم شحن طلبك وهو في طريقه إليك",location:"مركز التوزيع - دبي",timestamp:"2024-01-21T08:00:00Z",isCompleted:!0},{id:"4",status:"في الطريق",description:"الطلب في طريقه للتوصيل",location:"مركز التوزيع المحلي - الشارقة",timestamp:"2024-01-21T16:45:00Z",isCompleted:!0},{id:"5",status:"خرج للتوصيل",description:"الطلب مع مندوب التوصيل",location:"في الطريق إلى العنوان",timestamp:"2024-01-22T10:00:00Z",isCompleted:!1},{id:"6",status:"تم التسليم",description:"تم تسليم الطلب بنجاح",location:"عنوان العميل",timestamp:"",isCompleted:!1}],customerInfo:{name:(null==a?void 0:a.full_name)||"أحمد محمد",phone:(null==a?void 0:a.phone)||"+971501234567",address:"شارع الشيخ زايد، دبي، الإمارات العربية المتحدة"},items:[{name:"زي التخرج الكلاسيكي",quantity:1,image:"/api/placeholder/80/80"},{name:"قبعة التخرج المميزة",quantity:1,image:"/api/placeholder/80/80"}]}),M(!1)},1500)},B=(e,a)=>a?(0,t.jsx)(u.A,{className:"h-5 w-5 text-green-600"}):"خرج للتوصيل"===e?(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400"});return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"تتبع الطلب \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تابع حالة طلبك ومكان وصوله"})]}),(0,t.jsxs)(c.Zp,{className:"mb-8",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"البحث عن طلب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أدخل رقم الطلب أو رقم التتبع لمعرفة حالة طلبك"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"tracking",className:"arabic-text",children:"رقم الطلب أو رقم التتبع"}),(0,t.jsx)(d.p,{id:"tracking",placeholder:"مثال: GT-240120-001 أو TRK-123456",value:s,onChange:e=>C(e.target.value),className:"arabic-text"})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsxs)(n.$,{onClick:S,disabled:D,children:[D?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع"})]})})]}),R&&(0,t.jsxs)("div",{className:"mt-3 flex items-center gap-2 text-red-600",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:R})]})]})]}),Z&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"حالة الطلب الحالية"}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:["رقم الطلب: ",Z.orderNumber]})]}),(0,t.jsx)(m.E,{className:(e=>{switch(e){case"تم تأكيد الطلب":return"bg-blue-100 text-blue-800";case"قيد التحضير":return"bg-yellow-100 text-yellow-800";case"تم الشحن":return"bg-purple-100 text-purple-800";case"في الطريق":return"bg-orange-100 text-orange-800";case"خرج للتوصيل":case"تم التسليم":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(Z.currentStatus),children:Z.currentStatus})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تقدم الطلب"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[Z.progress,"%"]})]}),(0,t.jsx)(o.k,{value:Z.progress,className:"h-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"التوصيل المتوقع"}),(0,t.jsx)("p",{className:"font-medium",children:new Date(Z.estimatedDelivery).toLocaleDateString("en-US")})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"شركة الشحن"}),(0,t.jsx)("p",{className:"font-medium arabic-text",children:Z.carrier})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"رقم التتبع"}),(0,t.jsx)("p",{className:"font-medium font-mono",children:Z.trackingNumber})]})]})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تاريخ الطلب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"تتبع مراحل طلبك من التأكيد حتى التسليم"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-6",children:Z.events.map((e,a)=>(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full border-2 flex items-center justify-center ".concat(e.isCompleted?"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800":"خرج للتوصيل"===e.status?"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800":"bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700"),children:B(e.status,e.isCompleted)}),a<Z.events.length-1&&(0,t.jsx)("div",{className:"w-0.5 h-12 mt-2 ".concat(e.isCompleted?"bg-green-200":"bg-gray-200")})]}),(0,t.jsxs)("div",{className:"flex-1 pb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,t.jsx)("h3",{className:"font-medium arabic-text ".concat(e.isCompleted?"text-gray-900 dark:text-white":"text-gray-500"),children:e.status}),e.timestamp&&(0,t.jsx)("span",{className:"text-sm text-gray-500",children:new Date(e.timestamp).toLocaleString("en-US")})]}),(0,t.jsx)("p",{className:"text-sm arabic-text ".concat(e.isCompleted?"text-gray-600 dark:text-gray-400":"text-gray-400"),children:e.description}),(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(v.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{className:"text-xs arabic-text ".concat(e.isCompleted?"text-gray-500":"text-gray-400"),children:e.location})]})]})]},e.id))})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"منتجات الطلب"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:Z.items.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0",children:(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:["الكمية: ",e.quantity]})]})]},a))})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"عنوان التوصيل"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:Z.customerInfo.address})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:Z.customerInfo.phone})]})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إجراءات سريعة"})}),(0,t.jsxs)(c.Wu,{className:"space-y-3",children:[(0,t.jsxs)(n.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"تتبع على الخريطة"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"تحميل تفاصيل التتبع"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"مشاركة حالة الطلب"]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تحتاج مساعدة؟"})}),(0,t.jsxs)(c.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text",children:"اتصل بنا"}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"+971 4 123 4567"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text",children:"راسلنا"}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"<EMAIL>"})]})]}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full arabic-text",children:"تواصل مع الدعم"})]})]})]})]}),!Z&&!D&&s&&(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"text-center py-12",children:[(0,t.jsx)(y.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text",children:"لم يتم العثور على الطلب"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 arabic-text",children:"تأكد من رقم الطلب أو رقم التتبع وحاول مرة أخرى"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,t.jsxs)(n.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"اتصل بالدعم"]}),(0,t.jsx)(n.$,{className:"arabic-text",asChild:!0,children:(0,t.jsx)("a",{href:"/dashboard/student",children:"لوحة التحكم"})})]})]})})]})]})}},94711:(e,a,s)=>{Promise.resolve().then(s.bind(s,92121))}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,2632,7443,7784,8441,1684,7358],()=>a(94711)),_N_E=e.O()}]);