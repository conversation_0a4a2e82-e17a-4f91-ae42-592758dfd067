"use strict";(()=>{var e={};e.id=7409,e.ids=[7409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27128:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>c});var o=r(96559),n=r(48088),a=r(37719),i=r(32190),u=r(38561);async function d(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_inactive"),s=t.get("city"),o=t.get("search"),n=t.get("sort_by")||"created_at",a=t.get("sort_order")||"desc",d=u.CN.getSchools();if(r||(d=d.filter(e=>e.is_active)),s&&(d=d.filter(e=>e.city?.toLowerCase().includes(s.toLowerCase()))),o){let e=o.toLowerCase();d=d.filter(t=>t.name.toLowerCase().includes(e)||t.name_en?.toLowerCase().includes(e)||t.email?.toLowerCase().includes(e)||t.city?.toLowerCase().includes(e))}d.sort((e,t)=>{let r=e[n],s=t[n];return(null==r&&(r=""),null==s&&(s=""),("created_at"===n||"updated_at"===n||"graduation_date"===n)&&(r=new Date(r).getTime(),s=new Date(s).getTime()),"student_count"===n&&(r=Number(r)||0,s=Number(s)||0),"asc"===a)?r>s?1:-1:r<s?1:-1});let c={total:d.length,active:d.filter(e=>e.is_active).length,inactive:d.filter(e=>!e.is_active).length,totalStudents:d.reduce((e,t)=>e+t.student_count,0),averageStudents:d.length>0?Math.round(d.reduce((e,t)=>e+t.student_count,0)/d.length):0};return i.NextResponse.json({schools:d,stats:c,total:d.length})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e){try{let{name:t,name_en:r,name_fr:s,address:o,city:n,phone:a,email:d,website:c,logo_url:l,graduation_date:p,student_count:g,is_active:v,settings:h}=await e.json();if(!t)return i.NextResponse.json({error:"اسم المدرسة مطلوب"},{status:400});if(void 0!==g&&(isNaN(g)||g<0))return i.NextResponse.json({error:"عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً"},{status:400});if(d&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return i.NextResponse.json({error:"البريد الإلكتروني غير صحيح"},{status:400});let x=u.CN.getSchools();if(d&&x.find(e=>e.email===d))return i.NextResponse.json({error:"البريد الإلكتروني موجود بالفعل"},{status:400});let _={id:u.CN.generateId(),name:t,name_en:r||void 0,name_fr:s||void 0,address:o||void 0,city:n||void 0,phone:a||void 0,email:d||void 0,website:c||void 0,logo_url:l||void 0,graduation_date:p||void 0,student_count:g||0,is_active:v??!0,settings:h||{},created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return x.push(_),u.CN.saveSchools(x),i.NextResponse.json({message:"تم إضافة المدرسة بنجاح",school:_},{status:201})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/schools/route",pathname:"/api/schools",filename:"route",bundlePath:"app/api/schools/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:v}=l;function h(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(27128));module.exports=s})();