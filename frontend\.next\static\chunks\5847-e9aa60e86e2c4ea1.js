"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5847],{1243:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5623:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13717:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17649:(e,a,t)=>{t.d(a,{UC:()=>H,VY:()=>F,ZD:()=>E,ZL:()=>V,bL:()=>z,hE:()=>T,hJ:()=>q,l9:()=>I,rc:()=>G});var r=t(12115),n=t(46081),l=t(6101),o=t(15452),i=t(85185),d=t(99708),s=t(95155),c="AlertDialog",[u,p]=(0,n.A)(c,[o.Hs]),y=(0,o.Hs)(),h=e=>{let{__scopeAlertDialog:a,...t}=e,r=y(a);return(0,s.jsx)(o.bL,{...r,...t,modal:!0})};h.displayName=c;var v=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,n=y(t);return(0,s.jsx)(o.l9,{...n,...r,ref:a})});v.displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:a,...t}=e,r=y(a);return(0,s.jsx)(o.ZL,{...r,...t})};f.displayName="AlertDialogPortal";var m=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,n=y(t);return(0,s.jsx)(o.hJ,{...n,...r,ref:a})});m.displayName="AlertDialogOverlay";var k="AlertDialogContent",[g,A]=u(k),x=(0,d.Dc)("AlertDialogContent"),b=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,children:n,...d}=e,c=y(t),u=r.useRef(null),p=(0,l.s)(a,u),h=r.useRef(null);return(0,s.jsx)(o.G$,{contentName:k,titleName:w,docsSlug:"alert-dialog",children:(0,s.jsx)(g,{scope:t,cancelRef:h,children:(0,s.jsxs)(o.UC,{role:"alertdialog",...c,...d,ref:p,onOpenAutoFocus:(0,i.m)(d.onOpenAutoFocus,e=>{var a;e.preventDefault(),null==(a=h.current)||a.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(x,{children:n}),(0,s.jsx)(L,{contentRef:u})]})})})});b.displayName=k;var w="AlertDialogTitle",M=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,n=y(t);return(0,s.jsx)(o.hE,{...n,...r,ref:a})});M.displayName=w;var j="AlertDialogDescription",D=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,n=y(t);return(0,s.jsx)(o.VY,{...n,...r,ref:a})});D.displayName=j;var N=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,n=y(t);return(0,s.jsx)(o.bm,{...n,...r,ref:a})});N.displayName="AlertDialogAction";var C="AlertDialogCancel",R=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,{cancelRef:n}=A(C,t),i=y(t),d=(0,l.s)(a,n);return(0,s.jsx)(o.bm,{...i,...r,ref:d})});R.displayName=C;var L=e=>{let{contentRef:a}=e,t="`".concat(k,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(k,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(k,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=a.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(t)},[t,a]),null},z=h,I=v,V=f,q=m,H=b,G=N,E=R,T=M,F=D},27213:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},32568:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},33109:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},35169:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},38564:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40646:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42118:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},55863:(e,a,t)=>{t.d(a,{C1:()=>x,bL:()=>A});var r=t(12115),n=t(46081),l=t(63655),o=t(95155),i="Progress",[d,s]=(0,n.A)(i),[c,u]=d(i),p=r.forwardRef((e,a)=>{var t,r,n,i;let{__scopeProgress:d,value:s=null,max:u,getValueLabel:p=v,...y}=e;(u||0===u)&&!k(u)&&console.error((t="".concat(u),r="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=k(u)?u:100;null===s||g(s,h)||console.error((n="".concat(s),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let A=g(s,h)?s:null,x=m(A)?p(A,h):void 0;return(0,o.jsx)(c,{scope:d,value:A,max:h,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":m(A)?A:void 0,"aria-valuetext":x,role:"progressbar","data-state":f(A,h),"data-value":null!=A?A:void 0,"data-max":h,...y,ref:a})})});p.displayName=i;var y="ProgressIndicator",h=r.forwardRef((e,a)=>{var t;let{__scopeProgress:r,...n}=e,i=u(y,r);return(0,o.jsx)(l.sG.div,{"data-state":f(i.value,i.max),"data-value":null!=(t=i.value)?t:void 0,"data-max":i.max,...n,ref:a})});function v(e,a){return"".concat(Math.round(e/a*100),"%")}function f(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function m(e){return"number"==typeof e}function k(e){return m(e)&&!isNaN(e)&&e>0}function g(e,a){return m(e)&&!isNaN(e)&&e<=a&&e>=0}h.displayName=y;var A=p,x=h},60704:(e,a,t)=>{t.d(a,{B8:()=>R,UC:()=>z,bL:()=>C,l9:()=>L});var r=t(12115),n=t(85185),l=t(46081),o=t(89196),i=t(28905),d=t(63655),s=t(94315),c=t(5845),u=t(61285),p=t(95155),y="Tabs",[h,v]=(0,l.A)(y,[o.RG]),f=(0,o.RG)(),[m,k]=h(y),g=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:n,defaultValue:l,orientation:o="horizontal",dir:i,activationMode:h="automatic",...v}=e,f=(0,s.jH)(i),[k,g]=(0,c.i)({prop:r,onChange:n,defaultProp:null!=l?l:"",caller:y});return(0,p.jsx)(m,{scope:t,baseId:(0,u.B)(),value:k,onValueChange:g,orientation:o,dir:f,activationMode:h,children:(0,p.jsx)(d.sG.div,{dir:f,"data-orientation":o,...v,ref:a})})});g.displayName=y;var A="TabsList",x=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...n}=e,l=k(A,t),i=f(t);return(0,p.jsx)(o.bL,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:r,children:(0,p.jsx)(d.sG.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:a})})});x.displayName=A;var b="TabsTrigger",w=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...i}=e,s=k(b,t),c=f(t),u=D(s.baseId,r),y=N(s.baseId,r),h=r===s.value;return(0,p.jsx)(o.q7,{asChild:!0,...c,focusable:!l,active:h,children:(0,p.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":y,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...i,ref:a,onMouseDown:(0,n.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;h||l||!e||s.onValueChange(r)})})})});w.displayName=b;var M="TabsContent",j=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,forceMount:l,children:o,...s}=e,c=k(M,t),u=D(c.baseId,n),y=N(c.baseId,n),h=n===c.value,v=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:l||h,children:t=>{let{present:r}=t;return(0,p.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:y,tabIndex:0,...s,ref:a,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&o})}})});function D(e,a){return"".concat(e,"-trigger-").concat(a)}function N(e,a){return"".concat(e,"-content-").concat(a)}j.displayName=M;var C=g,R=x,L=w,z=j},84616:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94449:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}}]);