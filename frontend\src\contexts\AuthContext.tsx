"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'

// أنواع البيانات للمصادقة
export enum UserRole {
  STUDENT = 'student',
  SCHOOL = 'school',
  ADMIN = 'admin',
  DELIVERY = 'delivery'
}

export interface UserProfile {
  id: string
  email: string
  full_name: string
  role: UserRole
  phone?: string
  school_name?: string
  created_at: string
  updated_at: string
}

// نوع مبسط للمستخدم للتطوير
interface User {
  id: string
  email?: string
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signUp: (email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) => Promise<{ data: unknown, error: string | null }>
  signIn: (email: string, password: string) => Promise<{ data: unknown, error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ data: unknown, error: string | null }>
  hasRole: (requiredRole: UserRole) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true) // بدء التحميل بـ true
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    // للتطوير - استرجاع المستخدم من localStorage
    const loadUserData = async () => {
      try {
        const savedUser = localStorage.getItem('mockUser')
        const savedProfile = localStorage.getItem('mockProfile')

        if (savedUser && savedProfile) {
          const userData = JSON.parse(savedUser)
          const profileData = JSON.parse(savedProfile)

          // التحقق من صحة البيانات وانتهاء الصلاحية
          if (userData && profileData && userData.id && profileData.id) {
            // التحقق من انتهاء صلاحية الجلسة (24 ساعة)
            const sessionTimestamp = localStorage.getItem('sessionTimestamp')
            const now = Date.now()
            const sessionAge = sessionTimestamp ? now - parseInt(sessionTimestamp) : 0
            const maxSessionAge = 24 * 60 * 60 * 1000 // 24 ساعة

            if (sessionTimestamp && sessionAge < maxSessionAge) {
              setUser(userData)
              setProfile(profileData)
              console.log('User data loaded from localStorage:', { userData, profileData })
            } else {
              // انتهت صلاحية الجلسة
              console.log('Session expired, clearing user data')
              localStorage.removeItem('mockUser')
              localStorage.removeItem('mockProfile')
              localStorage.removeItem('sessionTimestamp')
            }
          } else {
            // إذا كانت البيانات غير صحيحة، امسحها
            localStorage.removeItem('mockUser')
            localStorage.removeItem('mockProfile')
            localStorage.removeItem('sessionTimestamp')
          }
        }
      } catch (error) {
        console.error('Error loading user from localStorage:', error)
        // في حالة خطأ، امسح البيانات المعطوبة
        localStorage.removeItem('mockUser')
        localStorage.removeItem('mockProfile')
      } finally {
        setLoading(false)
      }
    }

    loadUserData()
  }, [mounted])

  // تجديد الجلسة عند النشاط
  useEffect(() => {
    if (!user || !profile) return

    const refreshSession = () => {
      try {
        localStorage.setItem('sessionTimestamp', Date.now().toString())
      } catch (error) {
        console.error('Error refreshing session:', error)
      }
    }

    // تجديد الجلسة عند النقر أو الحركة
    const events = ['click', 'keypress', 'scroll', 'mousemove']
    events.forEach(event => {
      document.addEventListener(event, refreshSession, { passive: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, refreshSession)
      })
    }
  }, [user, profile])

  const signUp = async (email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) => {
    // للتطوير - محاكاة تسجيل حساب جديد
    console.log('Sign up:', email, userData)
    return { data: { user: { id: '1', email } }, error: null }
  }

  const signIn = async (email: string, password: string) => {
    // للتطوير - محاكاة تسجيل الدخول
    console.log('Sign in:', email)
    const mockUser = { id: '1', email }

    // تحديد الدور بناءً على الإيميل للتطوير
    let role = UserRole.STUDENT
    if (email.includes('admin')) {
      role = UserRole.ADMIN
    } else if (email.includes('school')) {
      role = UserRole.SCHOOL
    } else if (email.includes('delivery')) {
      role = UserRole.DELIVERY
    }

    const mockProfile: UserProfile = {
      id: '1',
      email,
      full_name: email.split('@')[0] || 'مستخدم',
      role,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    setUser(mockUser)
    setProfile(mockProfile)

    // حفظ في localStorage للتطوير مع التحقق من النجاح
    try {
      localStorage.setItem('mockUser', JSON.stringify(mockUser))
      localStorage.setItem('mockProfile', JSON.stringify(mockProfile))
      localStorage.setItem('sessionTimestamp', Date.now().toString())
      console.log('User data saved to localStorage:', { mockUser, mockProfile })
    } catch (error) {
      console.error('Error saving user data to localStorage:', error)
    }

    // إعادة التوجيه بناءً على الدور
    setTimeout(() => {
      if (role === UserRole.ADMIN) {
        window.location.href = '/dashboard/admin'
      } else if (role === UserRole.SCHOOL) {
        window.location.href = '/dashboard/school'
      } else if (role === UserRole.DELIVERY) {
        window.location.href = '/dashboard/delivery'
      } else {
        window.location.href = '/dashboard/student'
      }
    }, 100)

    return { data: { user: mockUser }, error: null }
  }

  const signOut = async () => {
    try {
      setUser(null)
      setProfile(null)

      // حذف من localStorage
      localStorage.removeItem('mockUser')
      localStorage.removeItem('mockProfile')
      localStorage.removeItem('sessionTimestamp')
      console.log('User data cleared from localStorage')

      return { error: null }
    } catch (error) {
      console.error('Error during sign out:', error)
      return { error: 'فشل في تسجيل الخروج' }
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { data: null, error: 'No user logged in' }

    const updatedProfile = { ...profile, ...updates } as UserProfile
    setProfile(updatedProfile)
    return { data: updatedProfile, error: null }
  }

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!profile) return false

    // نظام هرمي بسيط للأدوار
    const roleHierarchy = {
      [UserRole.ADMIN]: 4,
      [UserRole.SCHOOL]: 3,
      [UserRole.DELIVERY]: 2,
      [UserRole.STUDENT]: 1
    }

    return roleHierarchy[profile.role] >= roleHierarchy[requiredRole]
  }

  const value = {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    hasRole
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
