[{"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\analytics\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\health\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\reorder\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\[id]\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\slug\\[slug]\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\[id]\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\upload\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\forgot-password\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\reset-password\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\blog\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\catalog\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\checkout\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\pages-management\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\delivery\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\school\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\student\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\notifications\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\order-confirmation\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\pages\\[slug]\\page.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\product\\[id]\\page.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\reviews\\page.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\support\\page.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\track-order\\page.tsx": "40", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\unauthorized\\page.tsx": "41", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminDashboardHeader.tsx": "42", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminQuickNav.tsx": "43", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminStatsCards.tsx": "44", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\EditProductDialog.tsx": "45", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\ImageUploader.tsx": "46", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\ProductForm.tsx": "47", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\QuickAdminActions.tsx": "48", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\LoginForm.tsx": "49", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "50", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\RegisterForm.tsx": "51", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\UserMenu.tsx": "52", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx": "53", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\ColorPalette.tsx": "54", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\DesignActions.tsx": "55", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\GraduationOutfitPreview.tsx": "56", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\dashboard\\StatsCard.tsx": "57", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\language-toggle.tsx": "58", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\media\\MediaLibrary.tsx": "59", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\Navigation.tsx": "60", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\notifications\\NotificationDropdown.tsx": "61", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\orders\\OrderStatusBadge.tsx": "62", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx": "63", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-toggle.tsx": "64", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\alert-dialog.tsx": "65", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\alert.tsx": "66", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\avatar.tsx": "67", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\badge.tsx": "68", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\button.tsx": "69", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\card.tsx": "70", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\checkbox.tsx": "71", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\confirm-dialog.tsx": "72", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\dialog.tsx": "73", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "74", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\input.tsx": "75", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\label.tsx": "76", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\navigation-menu.tsx": "77", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\progress.tsx": "78", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\radio-group.tsx": "79", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\scroll-area.tsx": "80", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\select.tsx": "81", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\separator.tsx": "82", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\slider.tsx": "83", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\switch.tsx": "84", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\table.tsx": "85", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\tabs.tsx": "86", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\textarea.tsx": "87", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\toast.tsx": "88", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\__tests__\\LanguageToggle.test.tsx": "89", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\__tests__\\Navigation.test.tsx": "90", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx": "91", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx": "92", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\hooks\\useTranslation.ts": "93", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\i18n.ts": "94", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts": "95", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase\\client.ts": "96", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase\\server.ts": "97", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase.ts": "98", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\utils.ts": "99", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\utils\\errorMonitoring.ts": "100", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\utils\\performance.ts": "101", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\route.ts": "102", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\[id]\\route.ts": "103", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\[id]\\route.ts": "104", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\route.ts": "105", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\[id]\\route.ts": "106", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\orders\\page.tsx": "107", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\schools\\page.tsx": "108", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\users\\page.tsx": "109", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx": "110", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx": "111", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\EditUserDialog.tsx": "112", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\OrderDetailsDialog.tsx": "113", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\OrderStatusBadge.tsx": "114", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\SchoolForm.tsx": "115", "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\UserForm.tsx": "116"}, {"size": 8570, "mtime": 1752026710400, "results": "117", "hashOfConfig": "118"}, {"size": 25381, "mtime": 1752059117148, "results": "119", "hashOfConfig": "118"}, {"size": 2797, "mtime": 1752086217151, "results": "120", "hashOfConfig": "118"}, {"size": 4365, "mtime": 1752086273670, "results": "121", "hashOfConfig": "118"}, {"size": 320, "mtime": 1752085988521, "results": "122", "hashOfConfig": "118"}, {"size": 1313, "mtime": 1752100073085, "results": "123", "hashOfConfig": "118"}, {"size": 3143, "mtime": 1752081541636, "results": "124", "hashOfConfig": "118"}, {"size": 4516, "mtime": 1752104504079, "results": "125", "hashOfConfig": "118"}, {"size": 4816, "mtime": 1752101606404, "results": "126", "hashOfConfig": "118"}, {"size": 2195, "mtime": 1752072553679, "results": "127", "hashOfConfig": "118"}, {"size": 3357, "mtime": 1752083115395, "results": "128", "hashOfConfig": "118"}, {"size": 3793, "mtime": 1752099898127, "results": "129", "hashOfConfig": "118"}, {"size": 3156, "mtime": 1752100890076, "results": "130", "hashOfConfig": "118"}, {"size": 6844, "mtime": 1752083890077, "results": "131", "hashOfConfig": "118"}, {"size": 1839, "mtime": 1752025225223, "results": "132", "hashOfConfig": "118"}, {"size": 10580, "mtime": 1752083999409, "results": "133", "hashOfConfig": "118"}, {"size": 20560, "mtime": 1752107511198, "results": "134", "hashOfConfig": "118"}, {"size": 17040, "mtime": 1752056992848, "results": "135", "hashOfConfig": "118"}, {"size": 11376, "mtime": 1752111100787, "results": "136", "hashOfConfig": "118"}, {"size": 24906, "mtime": 1752057136397, "results": "137", "hashOfConfig": "118"}, {"size": 18248, "mtime": 1752027938847, "results": "138", "hashOfConfig": "118"}, {"size": 15971, "mtime": 1752111618855, "results": "139", "hashOfConfig": "118"}, {"size": 28423, "mtime": 1752103063667, "results": "140", "hashOfConfig": "118"}, {"size": 17736, "mtime": 1752116129952, "results": "141", "hashOfConfig": "118"}, {"size": 20967, "mtime": 1752072790541, "results": "142", "hashOfConfig": "118"}, {"size": 38714, "mtime": 1752111519652, "results": "143", "hashOfConfig": "118"}, {"size": 21711, "mtime": 1752055293265, "results": "144", "hashOfConfig": "118"}, {"size": 1774, "mtime": 1752110211719, "results": "145", "hashOfConfig": "118"}, {"size": 37449, "mtime": 1752105056127, "results": "146", "hashOfConfig": "118"}, {"size": 28323, "mtime": 1752104741614, "results": "147", "hashOfConfig": "118"}, {"size": 1689, "mtime": 1752080522850, "results": "148", "hashOfConfig": "118"}, {"size": 18640, "mtime": 1752057952701, "results": "149", "hashOfConfig": "118"}, {"size": 18779, "mtime": 1752107424978, "results": "150", "hashOfConfig": "118"}, {"size": 4483, "mtime": 1752028010421, "results": "151", "hashOfConfig": "118"}, {"size": 11492, "mtime": 1752107374516, "results": "152", "hashOfConfig": "118"}, {"size": 15216, "mtime": 1752026261505, "results": "153", "hashOfConfig": "118"}, {"size": 17847, "mtime": 1752107471996, "results": "154", "hashOfConfig": "118"}, {"size": 23425, "mtime": 1752059016412, "results": "155", "hashOfConfig": "118"}, {"size": 24203, "mtime": 1752058058852, "results": "156", "hashOfConfig": "118"}, {"size": 19684, "mtime": 1752107498786, "results": "157", "hashOfConfig": "118"}, {"size": 1953, "mtime": 1752025424668, "results": "158", "hashOfConfig": "118"}, {"size": 1220, "mtime": 1752080251896, "results": "159", "hashOfConfig": "118"}, {"size": 1677, "mtime": 1752080132567, "results": "160", "hashOfConfig": "118"}, {"size": 3810, "mtime": 1752113328108, "results": "161", "hashOfConfig": "118"}, {"size": 2633, "mtime": 1752086575040, "results": "162", "hashOfConfig": "118"}, {"size": 20165, "mtime": 1752109990584, "results": "163", "hashOfConfig": "118"}, {"size": 23721, "mtime": 1752086491038, "results": "164", "hashOfConfig": "118"}, {"size": 5604, "mtime": 1752114638411, "results": "165", "hashOfConfig": "118"}, {"size": 4978, "mtime": 1752083924150, "results": "166", "hashOfConfig": "118"}, {"size": 1681, "mtime": 1752105485832, "results": "167", "hashOfConfig": "118"}, {"size": 9467, "mtime": 1752060429622, "results": "168", "hashOfConfig": "118"}, {"size": 4301, "mtime": 1752110093862, "results": "169", "hashOfConfig": "118"}, {"size": 11699, "mtime": 1752058308640, "results": "170", "hashOfConfig": "118"}, {"size": 10164, "mtime": 1752111045799, "results": "171", "hashOfConfig": "118"}, {"size": 11158, "mtime": 1752027647437, "results": "172", "hashOfConfig": "118"}, {"size": 11158, "mtime": 1752027542597, "results": "173", "hashOfConfig": "118"}, {"size": 8756, "mtime": 1752111030771, "results": "174", "hashOfConfig": "118"}, {"size": 3057, "mtime": 1752071201203, "results": "175", "hashOfConfig": "118"}, {"size": 19830, "mtime": 1752111010573, "results": "176", "hashOfConfig": "118"}, {"size": 23365, "mtime": 1752108552816, "results": "177", "hashOfConfig": "118"}, {"size": 9782, "mtime": 1752061184817, "results": "178", "hashOfConfig": "118"}, {"size": 8438, "mtime": 1752107437757, "results": "179", "hashOfConfig": "118"}, {"size": 432, "mtime": 1752061306437, "results": "180", "hashOfConfig": "118"}, {"size": 1237, "mtime": 1752022677091, "results": "181", "hashOfConfig": "118"}, {"size": 4433, "mtime": 1752087065395, "results": "182", "hashOfConfig": "118"}, {"size": 1614, "mtime": 1752025189468, "results": "183", "hashOfConfig": "118"}, {"size": 1097, "mtime": 1752025307490, "results": "184", "hashOfConfig": "118"}, {"size": 1631, "mtime": 1752026168817, "results": "185", "hashOfConfig": "118"}, {"size": 2123, "mtime": 1752022529326, "results": "186", "hashOfConfig": "118"}, {"size": 1989, "mtime": 1752022529412, "results": "187", "hashOfConfig": "118"}, {"size": 1226, "mtime": 1752057176155, "results": "188", "hashOfConfig": "118"}, {"size": 1510, "mtime": 1752086590526, "results": "189", "hashOfConfig": "118"}, {"size": 3982, "mtime": 1752027718765, "results": "190", "hashOfConfig": "118"}, {"size": 8284, "mtime": 1752022529535, "results": "191", "hashOfConfig": "118"}, {"size": 967, "mtime": 1752022601887, "results": "192", "hashOfConfig": "118"}, {"size": 611, "mtime": 1752022602014, "results": "193", "hashOfConfig": "118"}, {"size": 6664, "mtime": 1752022602311, "results": "194", "hashOfConfig": "118"}, {"size": 740, "mtime": 1752054768096, "results": "195", "hashOfConfig": "118"}, {"size": 1466, "mtime": 1752057176103, "results": "196", "hashOfConfig": "118"}, {"size": 1645, "mtime": 1752057862892, "results": "197", "hashOfConfig": "118"}, {"size": 6253, "mtime": 1752022602166, "results": "198", "hashOfConfig": "118"}, {"size": 699, "mtime": 1752057031007, "results": "199", "hashOfConfig": "118"}, {"size": 2001, "mtime": 1752027466709, "results": "200", "hashOfConfig": "118"}, {"size": 1177, "mtime": 1752027466779, "results": "201", "hashOfConfig": "118"}, {"size": 2765, "mtime": 1752101162453, "results": "202", "hashOfConfig": "118"}, {"size": 1969, "mtime": 1752026317765, "results": "203", "hashOfConfig": "118"}, {"size": 759, "mtime": 1752022602210, "results": "204", "hashOfConfig": "118"}, {"size": 3384, "mtime": 1752086612720, "results": "205", "hashOfConfig": "118"}, {"size": 3320, "mtime": 1752071395463, "results": "206", "hashOfConfig": "118"}, {"size": 5371, "mtime": 1752071428644, "results": "207", "hashOfConfig": "118"}, {"size": 8135, "mtime": 1752110823805, "results": "208", "hashOfConfig": "118"}, {"size": 9350, "mtime": 1752057773869, "results": "209", "hashOfConfig": "118"}, {"size": 1295, "mtime": 1752023342710, "results": "210", "hashOfConfig": "118"}, {"size": 459, "mtime": 1752023250035, "results": "211", "hashOfConfig": "118"}, {"size": 28241, "mtime": 1752115589343, "results": "212", "hashOfConfig": "118"}, {"size": 212, "mtime": 1752079261306, "results": "213", "hashOfConfig": "118"}, {"size": 1103, "mtime": 1752079113610, "results": "214", "hashOfConfig": "118"}, {"size": 1071, "mtime": 1752030200020, "results": "215", "hashOfConfig": "118"}, {"size": 166, "mtime": 1752022287444, "results": "216", "hashOfConfig": "118"}, {"size": 12959, "mtime": 1752061012933, "results": "217", "hashOfConfig": "118"}, {"size": 10030, "mtime": 1752059700377, "results": "218", "hashOfConfig": "118"}, {"size": 5671, "mtime": 1752115637434, "results": "219", "hashOfConfig": "118"}, {"size": 3253, "mtime": 1752115666453, "results": "220", "hashOfConfig": "118"}, {"size": 4338, "mtime": 1752107316133, "results": "221", "hashOfConfig": "118"}, {"size": 5489, "mtime": 1752114345187, "results": "222", "hashOfConfig": "118"}, {"size": 4552, "mtime": 1752114367202, "results": "223", "hashOfConfig": "118"}, {"size": 21328, "mtime": 1752116075707, "results": "224", "hashOfConfig": "118"}, {"size": 19131, "mtime": 1752114961719, "results": "225", "hashOfConfig": "118"}, {"size": 22919, "mtime": 1752113635173, "results": "226", "hashOfConfig": "118"}, {"size": 5732, "mtime": 1752112139716, "results": "227", "hashOfConfig": "118"}, {"size": 7665, "mtime": 1752112052138, "results": "228", "hashOfConfig": "118"}, {"size": 18389, "mtime": 1752112651000, "results": "229", "hashOfConfig": "118"}, {"size": 13144, "mtime": 1752115880880, "results": "230", "hashOfConfig": "118"}, {"size": 3612, "mtime": 1752115833345, "results": "231", "hashOfConfig": "118"}, {"size": 15640, "mtime": 1752114435715, "results": "232", "hashOfConfig": "118"}, {"size": 19164, "mtime": 1752112568509, "results": "233", "hashOfConfig": "118"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "94ygdp", {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\about\\page.tsx", ["582", "583"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\analytics\\page.tsx", ["584", "585", "586", "587"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\reorder\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\route.ts", ["588", "589"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\slug\\[slug]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\forgot-password\\page.tsx", ["590"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\auth\\reset-password\\page.tsx", ["591", "592"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\blog\\page.tsx", ["593", "594", "595"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx", ["596", "597", "598", "599", "600", "601", "602", "603"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\catalog\\page.tsx", ["604", "605", "606", "607"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\checkout\\page.tsx", ["608", "609", "610", "611", "612", "613", "614", "615"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx", ["616", "617", "618", "619", "620", "621", "622", "623", "624", "625"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx", ["626", "627", "628"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx", ["629", "630"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\page.tsx", ["631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\pages-management\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx", ["649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\delivery\\page.tsx", ["662", "663", "664", "665", "666", "667"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\school\\page.tsx", ["668", "669", "670", "671", "672"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\student\\page.tsx", ["673", "674", "675", "676"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\notifications\\page.tsx", ["677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\order-confirmation\\page.tsx", ["690", "691", "692"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\pages\\[slug]\\page.tsx", ["693", "694", "695", "696", "697", "698", "699"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\product\\[id]\\page.tsx", ["700", "701", "702", "703"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx", ["704"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\reviews\\page.tsx", ["705", "706", "707", "708", "709"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\support\\page.tsx", ["710", "711", "712", "713", "714", "715", "716"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\track-order\\page.tsx", ["717", "718", "719"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\unauthorized\\page.tsx", ["720"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminDashboardHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminQuickNav.tsx", ["721", "722", "723"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\AdminStatsCards.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\EditProductDialog.tsx", ["724", "725", "726", "727", "728"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\ImageUploader.tsx", ["729", "730", "731"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\ProductForm.tsx", ["732", "733", "734"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\QuickAdminActions.tsx", ["735"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\auth\\UserMenu.tsx", ["736", "737", "738"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx", ["739", "740", "741", "742", "743"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\ColorPalette.tsx", ["744", "745"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\DesignActions.tsx", ["746", "747", "748", "749", "750", "751"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\customize\\GraduationOutfitPreview.tsx", ["752", "753"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\dashboard\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\language-toggle.tsx", ["754"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\media\\MediaLibrary.tsx", ["755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\Navigation.tsx", ["768", "769"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\notifications\\NotificationDropdown.tsx", ["770", "771", "772"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\orders\\OrderStatusBadge.tsx", ["773"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\confirm-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\__tests__\\LanguageToggle.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\__tests__\\Navigation.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx", ["774"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx", ["775", "776"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\hooks\\useTranslation.ts", ["777"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\i18n.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts", ["778", "779"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase\\server.ts", ["780", "781", "782", "783"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\utils\\errorMonitoring.ts", ["784", "785", "786", "787", "788", "789", "790", "791"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\utils\\performance.ts", ["792", "793", "794", "795", "796", "797", "798", "799", "800"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\route.ts", ["801", "802", "803", "804"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\[id]\\route.ts", ["805"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\[id]\\route.ts", ["806"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\route.ts", ["807", "808"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\orders\\page.tsx", ["809", "810", "811", "812", "813", "814", "815", "816", "817"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\schools\\page.tsx", ["818", "819", "820", "821"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\users\\page.tsx", ["822", "823", "824", "825", "826"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx", ["827"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\EditUserDialog.tsx", ["828", "829", "830", "831", "832", "833", "834", "835", "836"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\OrderDetailsDialog.tsx", ["837", "838", "839", "840", "841", "842"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\OrderStatusBadge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\SchoolForm.tsx", ["843", "844", "845", "846", "847", "848"], [], "C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\admin\\UserForm.tsx", ["849", "850", "851", "852", "853", "854", "855", "856"], [], {"ruleId": "857", "severity": 2, "message": "858", "line": 7, "column": 3, "nodeType": null, "messageId": "859", "endLine": 7, "endColumn": 16}, {"ruleId": "857", "severity": 2, "message": "860", "line": 18, "column": 11, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "861", "line": 20, "column": 3, "nodeType": null, "messageId": "859", "endLine": 20, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "862", "line": 22, "column": 3, "nodeType": null, "messageId": "859", "endLine": 22, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "863", "line": 69, "column": 11, "nodeType": null, "messageId": "859", "endLine": 69, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 69, "column": 17, "nodeType": null, "messageId": "859", "endLine": 69, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "865", "line": 2, "column": 27, "nodeType": null, "messageId": "859", "endLine": 2, "endColumn": 35}, {"ruleId": "857", "severity": 2, "message": "866", "line": 2, "column": 37, "nodeType": null, "messageId": "859", "endLine": 2, "endColumn": 52}, {"ruleId": "857", "severity": 2, "message": "867", "line": 51, "column": 14, "nodeType": null, "messageId": "859", "endLine": 51, "endColumn": 17}, {"ruleId": "857", "severity": 2, "message": "860", "line": 17, "column": 11, "nodeType": null, "messageId": "859", "endLine": 17, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "867", "line": 74, "column": 14, "nodeType": null, "messageId": "859", "endLine": 74, "endColumn": 17}, {"ruleId": "857", "severity": 2, "message": "862", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "863", "line": 56, "column": 11, "nodeType": null, "messageId": "859", "endLine": 56, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 56, "column": 17, "nodeType": null, "messageId": "859", "endLine": 56, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "868", "line": 6, "column": 29, "nodeType": null, "messageId": "859", "endLine": 6, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "869", "line": 10, "column": 10, "nodeType": null, "messageId": "859", "endLine": 10, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "870", "line": 17, "column": 3, "nodeType": null, "messageId": "859", "endLine": 17, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "871", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "872", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 13}, {"ruleId": "873", "severity": 2, "message": "874", "line": 39, "column": 20, "nodeType": "875", "messageId": "876", "endLine": 39, "endColumn": 23, "suggestions": "877"}, {"ruleId": "857", "severity": 2, "message": "863", "line": 52, "column": 11, "nodeType": null, "messageId": "859", "endLine": 52, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 52, "column": 17, "nodeType": null, "messageId": "859", "endLine": 52, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "858", "line": 12, "column": 3, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 16}, {"ruleId": "857", "severity": 2, "message": "862", "line": 14, "column": 3, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "860", "line": 90, "column": 11, "nodeType": null, "messageId": "859", "endLine": 90, "endColumn": 12}, {"ruleId": "878", "severity": 1, "message": "879", "line": 193, "column": 19, "nodeType": "880", "endLine": 197, "endColumn": 21}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "882", "line": 17, "column": 3, "nodeType": null, "messageId": "859", "endLine": 17, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "883", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "884", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "861", "line": 20, "column": 3, "nodeType": null, "messageId": "859", "endLine": 20, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "885", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "863", "line": 50, "column": 11, "nodeType": null, "messageId": "859", "endLine": 50, "endColumn": 15}, {"ruleId": "873", "severity": 2, "message": "874", "line": 391, "column": 73, "nodeType": "875", "messageId": "876", "endLine": 391, "endColumn": 76, "suggestions": "886"}, {"ruleId": "857", "severity": 2, "message": "868", "line": 10, "column": 29, "nodeType": null, "messageId": "859", "endLine": 10, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "887", "line": 14, "column": 10, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 16}, {"ruleId": "857", "severity": 2, "message": "888", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 10}, {"ruleId": "857", "severity": 2, "message": "889", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "890", "line": 26, "column": 3, "nodeType": null, "messageId": "859", "endLine": 26, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "891", "line": 27, "column": 3, "nodeType": null, "messageId": "859", "endLine": 27, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "892", "line": 28, "column": 3, "nodeType": null, "messageId": "859", "endLine": 28, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "860", "line": 122, "column": 11, "nodeType": null, "messageId": "859", "endLine": 122, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "893", "line": 125, "column": 22, "nodeType": null, "messageId": "859", "endLine": 125, "endColumn": 35}, {"ruleId": "873", "severity": 2, "message": "874", "line": 127, "column": 77, "nodeType": "875", "messageId": "876", "endLine": 127, "endColumn": 80, "suggestions": "894"}, {"ruleId": "857", "severity": 2, "message": "895", "line": 37, "column": 3, "nodeType": null, "messageId": "859", "endLine": 37, "endColumn": 10}, {"ruleId": "857", "severity": 2, "message": "896", "line": 40, "column": 3, "nodeType": null, "messageId": "859", "endLine": 40, "endColumn": 10}, {"ruleId": "857", "severity": 2, "message": "897", "line": 41, "column": 3, "nodeType": null, "messageId": "859", "endLine": 41, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "898", "line": 11, "column": 10, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 18}, {"ruleId": "857", "severity": 2, "message": "899", "line": 435, "column": 11, "nodeType": null, "messageId": "859", "endLine": 435, "endColumn": 27}, {"ruleId": "857", "severity": 2, "message": "868", "line": 6, "column": 29, "nodeType": null, "messageId": "859", "endLine": 6, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "900", "line": 9, "column": 10, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 18}, {"ruleId": "857", "severity": 2, "message": "901", "line": 11, "column": 10, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "902", "line": 15, "column": 10, "nodeType": null, "messageId": "859", "endLine": 15, "endColumn": 23}, {"ruleId": "857", "severity": 2, "message": "903", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "861", "line": 22, "column": 3, "nodeType": null, "messageId": "859", "endLine": 22, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "891", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "904", "line": 25, "column": 3, "nodeType": null, "messageId": "859", "endLine": 25, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "890", "line": 27, "column": 3, "nodeType": null, "messageId": "859", "endLine": 27, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "905", "line": 28, "column": 3, "nodeType": null, "messageId": "859", "endLine": 28, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "906", "line": 29, "column": 3, "nodeType": null, "messageId": "859", "endLine": 29, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "862", "line": 30, "column": 3, "nodeType": null, "messageId": "859", "endLine": 30, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "907", "line": 33, "column": 3, "nodeType": null, "messageId": "859", "endLine": 33, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "908", "line": 42, "column": 3, "nodeType": null, "messageId": "859", "endLine": 42, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "909", "line": 43, "column": 3, "nodeType": null, "messageId": "859", "endLine": 43, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "882", "line": 44, "column": 3, "nodeType": null, "messageId": "859", "endLine": 44, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "863", "line": 76, "column": 11, "nodeType": null, "messageId": "859", "endLine": 76, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 76, "column": 17, "nodeType": null, "messageId": "859", "endLine": 76, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "910", "line": 12, "column": 79, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 92}, {"ruleId": "857", "severity": 2, "message": "862", "line": 26, "column": 3, "nodeType": null, "messageId": "859", "endLine": 26, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "904", "line": 30, "column": 3, "nodeType": null, "messageId": "859", "endLine": 30, "endColumn": 9}, {"ruleId": "911", "severity": 1, "message": "912", "line": 117, "column": 6, "nodeType": "913", "endLine": 117, "endColumn": 8, "suggestions": "914"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 165, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 165, "endColumn": 22, "suggestions": "915"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 166, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 166, "endColumn": 22, "suggestions": "916"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 242, "column": 48, "nodeType": "875", "messageId": "876", "endLine": 242, "endColumn": 51, "suggestions": "917"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 249, "column": 50, "nodeType": "875", "messageId": "876", "endLine": 249, "endColumn": 53, "suggestions": "918"}, {"ruleId": "857", "severity": 2, "message": "919", "line": 276, "column": 13, "nodeType": null, "messageId": "859", "endLine": 276, "endColumn": 17}, {"ruleId": "873", "severity": 2, "message": "874", "line": 298, "column": 51, "nodeType": "875", "messageId": "876", "endLine": 298, "endColumn": 54, "suggestions": "920"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 305, "column": 50, "nodeType": "875", "messageId": "876", "endLine": 305, "endColumn": 53, "suggestions": "921"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 397, "column": 42, "nodeType": "875", "messageId": "876", "endLine": 397, "endColumn": 45, "suggestions": "922"}, {"ruleId": "878", "severity": 1, "message": "879", "line": 675, "column": 31, "nodeType": "880", "endLine": 679, "endColumn": 33}, {"ruleId": "857", "severity": 2, "message": "900", "line": 9, "column": 10, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 18}, {"ruleId": "857", "severity": 2, "message": "885", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "861", "line": 21, "column": 3, "nodeType": null, "messageId": "859", "endLine": 21, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "905", "line": 29, "column": 3, "nodeType": null, "messageId": "859", "endLine": 29, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "863", "line": 60, "column": 11, "nodeType": null, "messageId": "859", "endLine": 60, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 60, "column": 17, "nodeType": null, "messageId": "859", "endLine": 60, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "869", "line": 12, "column": 10, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "923", "line": 14, "column": 3, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "861", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "863", "line": 65, "column": 11, "nodeType": null, "messageId": "859", "endLine": 65, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 65, "column": 17, "nodeType": null, "messageId": "859", "endLine": 65, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "861", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 11}, {"ruleId": "873", "severity": 2, "message": "874", "line": 52, "column": 20, "nodeType": "875", "messageId": "876", "endLine": 52, "endColumn": 23, "suggestions": "924"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 60, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 60, "endColumn": 22, "suggestions": "925"}, {"ruleId": "857", "severity": 2, "message": "863", "line": 64, "column": 11, "nodeType": null, "messageId": "859", "endLine": 64, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "868", "line": 7, "column": 29, "nodeType": null, "messageId": "859", "endLine": 7, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "926", "line": 11, "column": 10, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "927", "line": 11, "column": 16, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 27}, {"ruleId": "857", "severity": 2, "message": "928", "line": 11, "column": 29, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 37}, {"ruleId": "857", "severity": 2, "message": "929", "line": 11, "column": 39, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 50}, {"ruleId": "857", "severity": 2, "message": "862", "line": 15, "column": 3, "nodeType": null, "messageId": "859", "endLine": 15, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "930", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 16}, {"ruleId": "857", "severity": 2, "message": "931", "line": 25, "column": 3, "nodeType": null, "messageId": "859", "endLine": 25, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "863", "line": 30, "column": 11, "nodeType": null, "messageId": "859", "endLine": 30, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 30, "column": 17, "nodeType": null, "messageId": "859", "endLine": 30, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "932", "line": 44, "column": 28, "nodeType": null, "messageId": "859", "endLine": 44, "endColumn": 47}, {"ruleId": "873", "severity": 2, "message": "874", "line": 193, "column": 63, "nodeType": "875", "messageId": "876", "endLine": 193, "endColumn": 66, "suggestions": "933"}, {"ruleId": "857", "severity": 2, "message": "934", "line": 9, "column": 10, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 19}, {"ruleId": "857", "severity": 2, "message": "931", "line": 23, "column": 3, "nodeType": null, "messageId": "859", "endLine": 23, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "863", "line": 58, "column": 11, "nodeType": null, "messageId": "859", "endLine": 58, "endColumn": 15}, {"ruleId": "873", "severity": 2, "message": "874", "line": 136, "column": 52, "nodeType": "875", "messageId": "876", "endLine": 136, "endColumn": 55, "suggestions": "935"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 139, "column": 60, "nodeType": "875", "messageId": "876", "endLine": 139, "endColumn": 63, "suggestions": "936"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 153, "column": 38, "nodeType": "875", "messageId": "876", "endLine": 153, "endColumn": 41, "suggestions": "937"}, {"ruleId": "938", "severity": 2, "message": "939", "line": 192, "column": 17, "nodeType": "880", "endLine": 192, "endColumn": 29}, {"ruleId": "938", "severity": 2, "message": "939", "line": 219, "column": 17, "nodeType": "880", "endLine": 219, "endColumn": 29}, {"ruleId": "938", "severity": 2, "message": "939", "line": 240, "column": 15, "nodeType": "880", "endLine": 240, "endColumn": 27}, {"ruleId": "878", "severity": 1, "message": "879", "line": 289, "column": 15, "nodeType": "880", "endLine": 293, "endColumn": 17}, {"ruleId": "857", "severity": 2, "message": "940", "line": 67, "column": 9, "nodeType": null, "messageId": "859", "endLine": 67, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "860", "line": 68, "column": 11, "nodeType": null, "messageId": "859", "endLine": 68, "endColumn": 12}, {"ruleId": "878", "severity": 1, "message": "879", "line": 108, "column": 15, "nodeType": "880", "endLine": 112, "endColumn": 17}, {"ruleId": "878", "severity": 1, "message": "879", "line": 144, "column": 19, "nodeType": "880", "endLine": 148, "endColumn": 21}, {"ruleId": "857", "severity": 2, "message": "941", "line": 117, "column": 14, "nodeType": null, "messageId": "859", "endLine": 117, "endColumn": 19}, {"ruleId": "857", "severity": 2, "message": "862", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "942", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "943", "line": 27, "column": 3, "nodeType": null, "messageId": "859", "endLine": 27, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "863", "line": 63, "column": 11, "nodeType": null, "messageId": "859", "endLine": 63, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 63, "column": 17, "nodeType": null, "messageId": "859", "endLine": 63, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "944", "line": 17, "column": 3, "nodeType": null, "messageId": "859", "endLine": 17, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "945", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "931", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "863", "line": 61, "column": 11, "nodeType": null, "messageId": "859", "endLine": 61, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 61, "column": 17, "nodeType": null, "messageId": "859", "endLine": 61, "endColumn": 24}, {"ruleId": "873", "severity": 2, "message": "874", "line": 300, "column": 101, "nodeType": "875", "messageId": "876", "endLine": 300, "endColumn": 104, "suggestions": "946"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 314, "column": 101, "nodeType": "875", "messageId": "876", "endLine": 314, "endColumn": 104, "suggestions": "947"}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "890", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "863", "line": 60, "column": 11, "nodeType": null, "messageId": "859", "endLine": 60, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "860", "line": 10, "column": 11, "nodeType": null, "messageId": "859", "endLine": 10, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "948", "line": 10, "column": 3, "nodeType": null, "messageId": "859", "endLine": 10, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "949", "line": 11, "column": 3, "nodeType": null, "messageId": "859", "endLine": 11, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "950", "line": 12, "column": 3, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "873", "severity": 2, "message": "874", "line": 29, "column": 35, "nodeType": "875", "messageId": "876", "endLine": 29, "endColumn": 38, "suggestions": "951"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 36, "column": 25, "nodeType": "875", "messageId": "876", "endLine": 36, "endColumn": 28, "suggestions": "952"}, {"ruleId": "857", "severity": 2, "message": "953", "line": 45, "column": 10, "nodeType": null, "messageId": "859", "endLine": 45, "endColumn": 17}, {"ruleId": "873", "severity": 2, "message": "874", "line": 71, "column": 41, "nodeType": "875", "messageId": "876", "endLine": 71, "endColumn": 44, "suggestions": "954"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 372, "column": 32, "nodeType": "875", "messageId": "876", "endLine": 372, "endColumn": 35, "suggestions": "955"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 388, "column": 44, "nodeType": "875", "messageId": "876", "endLine": 388, "endColumn": 47, "suggestions": "956"}, {"ruleId": "878", "severity": 1, "message": "879", "line": 450, "column": 23, "nodeType": "880", "endLine": 454, "endColumn": 25}, {"ruleId": "857", "severity": 2, "message": "904", "line": 14, "column": 3, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "957", "line": 23, "column": 3, "nodeType": null, "messageId": "859", "endLine": 23, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "907", "line": 24, "column": 3, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "958", "line": 16, "column": 3, "nodeType": null, "messageId": "859", "endLine": 16, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "959", "line": 14, "column": 10, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 16}, {"ruleId": "857", "severity": 2, "message": "960", "line": 14, "column": 18, "nodeType": null, "messageId": "859", "endLine": 14, "endColumn": 32}, {"ruleId": "857", "severity": 2, "message": "961", "line": 60, "column": 9, "nodeType": null, "messageId": "859", "endLine": 60, "endColumn": 20}, {"ruleId": "857", "severity": 2, "message": "962", "line": 5, "column": 41, "nodeType": null, "messageId": "859", "endLine": 5, "endColumn": 50}, {"ruleId": "857", "severity": 2, "message": "963", "line": 8, "column": 10, "nodeType": null, "messageId": "859", "endLine": 8, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "882", "line": 16, "column": 3, "nodeType": null, "messageId": "859", "endLine": 16, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "964", "line": 17, "column": 3, "nodeType": null, "messageId": "859", "endLine": 17, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "863", "line": 43, "column": 11, "nodeType": null, "messageId": "859", "endLine": 43, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "927", "line": 9, "column": 16, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 27}, {"ruleId": "857", "severity": 2, "message": "965", "line": 12, "column": 3, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "963", "line": 9, "column": 10, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "884", "line": 28, "column": 3, "nodeType": null, "messageId": "859", "endLine": 28, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "966", "line": 29, "column": 3, "nodeType": null, "messageId": "859", "endLine": 29, "endColumn": 7}, {"ruleId": "873", "severity": 2, "message": "874", "line": 35, "column": 15, "nodeType": "875", "messageId": "876", "endLine": 35, "endColumn": 18, "suggestions": "967"}, {"ruleId": "857", "severity": 2, "message": "968", "line": 43, "column": 3, "nodeType": null, "messageId": "859", "endLine": 43, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "941", "line": 64, "column": 14, "nodeType": null, "messageId": "859", "endLine": 64, "endColumn": 19}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "888", "line": 12, "column": 3, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 10}, {"ruleId": "857", "severity": 2, "message": "969", "line": 4, "column": 10, "nodeType": null, "messageId": "859", "endLine": 4, "endColumn": 19}, {"ruleId": "857", "severity": 2, "message": "868", "line": 4, "column": 29, "nodeType": null, "messageId": "859", "endLine": 4, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "970", "line": 4, "column": 46, "nodeType": null, "messageId": "859", "endLine": 4, "endColumn": 56}, {"ruleId": "857", "severity": 2, "message": "962", "line": 4, "column": 58, "nodeType": null, "messageId": "859", "endLine": 4, "endColumn": 67}, {"ruleId": "857", "severity": 2, "message": "926", "line": 9, "column": 10, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "927", "line": 9, "column": 16, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 27}, {"ruleId": "857", "severity": 2, "message": "928", "line": 9, "column": 29, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 37}, {"ruleId": "857", "severity": 2, "message": "929", "line": 9, "column": 39, "nodeType": null, "messageId": "859", "endLine": 9, "endColumn": 50}, {"ruleId": "857", "severity": 2, "message": "862", "line": 18, "column": 3, "nodeType": null, "messageId": "859", "endLine": 18, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "905", "line": 23, "column": 3, "nodeType": null, "messageId": "859", "endLine": 23, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "892", "line": 25, "column": 3, "nodeType": null, "messageId": "859", "endLine": 25, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "861", "line": 29, "column": 3, "nodeType": null, "messageId": "859", "endLine": 29, "endColumn": 11}, {"ruleId": "971", "severity": 1, "message": "972", "line": 165, "column": 28, "nodeType": "880", "endLine": 165, "endColumn": 57}, {"ruleId": "971", "severity": 1, "message": "972", "line": 333, "column": 17, "nodeType": "880", "endLine": 333, "endColumn": 51}, {"ruleId": "857", "severity": 2, "message": "863", "line": 232, "column": 11, "nodeType": null, "messageId": "859", "endLine": 232, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "864", "line": 232, "column": 17, "nodeType": null, "messageId": "859", "endLine": 232, "endColumn": 24}, {"ruleId": "857", "severity": 2, "message": "973", "line": 16, "column": 3, "nodeType": null, "messageId": "859", "endLine": 16, "endColumn": 8}, {"ruleId": "873", "severity": 2, "message": "874", "line": 235, "column": 53, "nodeType": "875", "messageId": "876", "endLine": 235, "endColumn": 56, "suggestions": "974"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 239, "column": 53, "nodeType": "875", "messageId": "876", "endLine": 239, "endColumn": 56, "suggestions": "975"}, {"ruleId": "857", "severity": 2, "message": "885", "line": 10, "column": 3, "nodeType": null, "messageId": "859", "endLine": 10, "endColumn": 14}, {"ruleId": "857", "severity": 2, "message": "976", "line": 146, "column": 40, "nodeType": null, "messageId": "859", "endLine": 146, "endColumn": 48}, {"ruleId": "873", "severity": 2, "message": "874", "line": 32, "column": 29, "nodeType": "875", "messageId": "876", "endLine": 32, "endColumn": 32, "suggestions": "977"}, {"ruleId": "911", "severity": 1, "message": "978", "line": 68, "column": 6, "nodeType": "913", "endLine": 68, "endColumn": 12, "suggestions": "979"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 39, "column": 16, "nodeType": "875", "messageId": "876", "endLine": 39, "endColumn": 19, "suggestions": "980"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 73, "column": 35, "nodeType": "875", "messageId": "876", "endLine": 73, "endColumn": 38, "suggestions": "981"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 91, "column": 29, "nodeType": "875", "messageId": "876", "endLine": 91, "endColumn": 32, "suggestions": "982"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 13, "column": 51, "nodeType": "875", "messageId": "876", "endLine": 13, "endColumn": 54, "suggestions": "983"}, {"ruleId": "857", "severity": 2, "message": "941", "line": 16, "column": 20, "nodeType": null, "messageId": "859", "endLine": 16, "endColumn": 25}, {"ruleId": "873", "severity": 2, "message": "874", "line": 22, "column": 39, "nodeType": "875", "messageId": "876", "endLine": 22, "endColumn": 42, "suggestions": "984"}, {"ruleId": "857", "severity": 2, "message": "941", "line": 25, "column": 20, "nodeType": null, "messageId": "859", "endLine": 25, "endColumn": 25}, {"ruleId": "873", "severity": 2, "message": "874", "line": 22, "column": 29, "nodeType": "875", "messageId": "876", "endLine": 22, "endColumn": 32, "suggestions": "985"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 26, "column": 13, "nodeType": "875", "messageId": "876", "endLine": 26, "endColumn": 16, "suggestions": "986"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 158, "column": 52, "nodeType": "875", "messageId": "876", "endLine": 158, "endColumn": 55, "suggestions": "987"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 162, "column": 51, "nodeType": "875", "messageId": "876", "endLine": 162, "endColumn": 54, "suggestions": "988"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 166, "column": 51, "nodeType": "875", "messageId": "876", "endLine": 166, "endColumn": 54, "suggestions": "989"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 170, "column": 67, "nodeType": "875", "messageId": "876", "endLine": 170, "endColumn": 70, "suggestions": "990"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 184, "column": 67, "nodeType": "875", "messageId": "876", "endLine": 184, "endColumn": 70, "suggestions": "991"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 384, "column": 48, "nodeType": "875", "messageId": "876", "endLine": 384, "endColumn": 51, "suggestions": "992"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 8, "column": 29, "nodeType": "875", "messageId": "876", "endLine": 8, "endColumn": 32, "suggestions": "993"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 29, "column": 56, "nodeType": "875", "messageId": "876", "endLine": 29, "endColumn": 59, "suggestions": "994"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 62, "column": 75, "nodeType": "875", "messageId": "876", "endLine": 62, "endColumn": 78, "suggestions": "995"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 87, "column": 31, "nodeType": "875", "messageId": "876", "endLine": 87, "endColumn": 34, "suggestions": "996"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 157, "column": 28, "nodeType": "875", "messageId": "876", "endLine": 157, "endColumn": 31, "suggestions": "997"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 158, "column": 37, "nodeType": "875", "messageId": "876", "endLine": 158, "endColumn": 40, "suggestions": "998"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 169, "column": 41, "nodeType": "875", "messageId": "876", "endLine": 169, "endColumn": 44, "suggestions": "999"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 303, "column": 28, "nodeType": "875", "messageId": "876", "endLine": 303, "endColumn": 31, "suggestions": "1000"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 307, "column": 43, "nodeType": "875", "messageId": "876", "endLine": 307, "endColumn": 46, "suggestions": "1001"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 48, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 48, "endColumn": 22, "suggestions": "1002"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 49, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 49, "endColumn": 22, "suggestions": "1003"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 129, "column": 55, "nodeType": "875", "messageId": "876", "endLine": 129, "endColumn": 58, "suggestions": "1004"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 146, "column": 31, "nodeType": "875", "messageId": "876", "endLine": 146, "endColumn": 34, "suggestions": "1005"}, {"ruleId": "857", "severity": 2, "message": "1006", "line": 2, "column": 27, "nodeType": null, "messageId": "859", "endLine": 2, "endColumn": 36}, {"ruleId": "857", "severity": 2, "message": "865", "line": 2, "column": 27, "nodeType": null, "messageId": "859", "endLine": 2, "endColumn": 35}, {"ruleId": "873", "severity": 2, "message": "874", "line": 40, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 40, "endColumn": 22, "suggestions": "1007"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 41, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 41, "endColumn": 22, "suggestions": "1008"}, {"ruleId": "857", "severity": 2, "message": "868", "line": 6, "column": 29, "nodeType": null, "messageId": "859", "endLine": 6, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "862", "line": 25, "column": 3, "nodeType": null, "messageId": "859", "endLine": 25, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "942", "line": 33, "column": 3, "nodeType": null, "messageId": "859", "endLine": 33, "endColumn": 13}, {"ruleId": "857", "severity": 2, "message": "1009", "line": 36, "column": 3, "nodeType": null, "messageId": "859", "endLine": 36, "endColumn": 10}, {"ruleId": "873", "severity": 2, "message": "874", "line": 51, "column": 10, "nodeType": "875", "messageId": "876", "endLine": 51, "endColumn": 13, "suggestions": "1010"}, {"ruleId": "911", "severity": 1, "message": "1011", "line": 129, "column": 6, "nodeType": "913", "endLine": 129, "endColumn": 8, "suggestions": "1012"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 132, "column": 62, "nodeType": "875", "messageId": "876", "endLine": 132, "endColumn": 65, "suggestions": "1013"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 217, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 217, "endColumn": 22, "suggestions": "1014"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 218, "column": 19, "nodeType": "875", "messageId": "876", "endLine": 218, "endColumn": 22, "suggestions": "1015"}, {"ruleId": "857", "severity": 2, "message": "890", "line": 15, "column": 3, "nodeType": null, "messageId": "859", "endLine": 15, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "883", "line": 20, "column": 3, "nodeType": null, "messageId": "859", "endLine": 20, "endColumn": 8}, {"ruleId": "857", "severity": 2, "message": "1016", "line": 22, "column": 3, "nodeType": null, "messageId": "859", "endLine": 22, "endColumn": 8}, {"ruleId": "911", "severity": 1, "message": "1017", "line": 88, "column": 6, "nodeType": "913", "endLine": 88, "endColumn": 63, "suggestions": "1018"}, {"ruleId": "857", "severity": 2, "message": "868", "line": 7, "column": 29, "nodeType": null, "messageId": "859", "endLine": 7, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "862", "line": 34, "column": 3, "nodeType": null, "messageId": "859", "endLine": 34, "endColumn": 9}, {"ruleId": "857", "severity": 2, "message": "890", "line": 38, "column": 3, "nodeType": null, "messageId": "859", "endLine": 38, "endColumn": 6}, {"ruleId": "857", "severity": 2, "message": "953", "line": 127, "column": 10, "nodeType": null, "messageId": "859", "endLine": 127, "endColumn": 17}, {"ruleId": "857", "severity": 2, "message": "1019", "line": 127, "column": 19, "nodeType": null, "messageId": "859", "endLine": 127, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "860", "line": 22, "column": 11, "nodeType": null, "messageId": "859", "endLine": 22, "endColumn": 12}, {"ruleId": "857", "severity": 2, "message": "881", "line": 3, "column": 20, "nodeType": null, "messageId": "859", "endLine": 3, "endColumn": 29}, {"ruleId": "857", "severity": 2, "message": "898", "line": 7, "column": 10, "nodeType": null, "messageId": "859", "endLine": 7, "endColumn": 18}, {"ruleId": "857", "severity": 2, "message": "884", "line": 27, "column": 3, "nodeType": null, "messageId": "859", "endLine": 27, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "883", "line": 28, "column": 3, "nodeType": null, "messageId": "859", "endLine": 28, "endColumn": 8}, {"ruleId": "873", "severity": 2, "message": "874", "line": 55, "column": 24, "nodeType": "875", "messageId": "876", "endLine": 55, "endColumn": 27, "suggestions": "1020"}, {"ruleId": "857", "severity": 2, "message": "1021", "line": 157, "column": 15, "nodeType": null, "messageId": "859", "endLine": 157, "endColumn": 35}, {"ruleId": "873", "severity": 2, "message": "874", "line": 171, "column": 61, "nodeType": "875", "messageId": "876", "endLine": 171, "endColumn": 64, "suggestions": "1022"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 244, "column": 72, "nodeType": "875", "messageId": "876", "endLine": 244, "endColumn": 75, "suggestions": "1023"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 423, "column": 72, "nodeType": "875", "messageId": "876", "endLine": 423, "endColumn": 75, "suggestions": "1024"}, {"ruleId": "857", "severity": 2, "message": "963", "line": 6, "column": 10, "nodeType": null, "messageId": "859", "endLine": 6, "endColumn": 15}, {"ruleId": "857", "severity": 2, "message": "861", "line": 19, "column": 3, "nodeType": null, "messageId": "859", "endLine": 19, "endColumn": 11}, {"ruleId": "857", "severity": 2, "message": "948", "line": 20, "column": 3, "nodeType": null, "messageId": "859", "endLine": 20, "endColumn": 11}, {"ruleId": "873", "severity": 2, "message": "874", "line": 74, "column": 40, "nodeType": "875", "messageId": "876", "endLine": 74, "endColumn": 43, "suggestions": "1025"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 90, "column": 22, "nodeType": "875", "messageId": "876", "endLine": 90, "endColumn": 25, "suggestions": "1026"}, {"ruleId": "857", "severity": 2, "message": "941", "line": 108, "column": 14, "nodeType": null, "messageId": "859", "endLine": 108, "endColumn": 19}, {"ruleId": "857", "severity": 2, "message": "861", "line": 12, "column": 10, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 18}, {"ruleId": "857", "severity": 2, "message": "1027", "line": 12, "column": 20, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 26}, {"ruleId": "857", "severity": 2, "message": "884", "line": 12, "column": 35, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 39}, {"ruleId": "857", "severity": 2, "message": "1016", "line": 12, "column": 41, "nodeType": null, "messageId": "859", "endLine": 12, "endColumn": 46}, {"ruleId": "873", "severity": 2, "message": "874", "line": 94, "column": 49, "nodeType": "875", "messageId": "876", "endLine": 94, "endColumn": 52, "suggestions": "1028"}, {"ruleId": "873", "severity": 2, "message": "874", "line": 109, "column": 49, "nodeType": "875", "messageId": "876", "endLine": 109, "endColumn": 52, "suggestions": "1029"}, {"ruleId": "857", "severity": 2, "message": "868", "line": 24, "column": 29, "nodeType": null, "messageId": "859", "endLine": 24, "endColumn": 44}, {"ruleId": "857", "severity": 2, "message": "884", "line": 27, "column": 3, "nodeType": null, "messageId": "859", "endLine": 27, "endColumn": 7}, {"ruleId": "857", "severity": 2, "message": "883", "line": 28, "column": 3, "nodeType": null, "messageId": "859", "endLine": 28, "endColumn": 8}, {"ruleId": "873", "severity": 2, "message": "874", "line": 38, "column": 24, "nodeType": "875", "messageId": "876", "endLine": 38, "endColumn": 27, "suggestions": "1030"}, {"ruleId": "857", "severity": 2, "message": "1031", "line": 138, "column": 15, "nodeType": null, "messageId": "859", "endLine": 138, "endColumn": 30}, {"ruleId": "873", "severity": 2, "message": "874", "line": 148, "column": 61, "nodeType": "875", "messageId": "876", "endLine": 148, "endColumn": 64, "suggestions": "1032"}, {"ruleId": "857", "severity": 2, "message": "1033", "line": 157, "column": 9, "nodeType": null, "messageId": "859", "endLine": 157, "endColumn": 20}, {"ruleId": "873", "severity": 2, "message": "874", "line": 232, "column": 72, "nodeType": "875", "messageId": "876", "endLine": 232, "endColumn": 75, "suggestions": "1034"}, "@typescript-eslint/no-unused-vars", "'GraduationCap' is defined but never used.", "unusedVar", "'t' is assigned a value but never used.", "'Calendar' is defined but never used.", "'Filter' is defined but never used.", "'user' is assigned a value but never used.", "'profile' is assigned a value but never used.", "'MockPage' is defined but never used.", "'MockPageContent' is defined but never used.", "'err' is defined but never used.", "'CardDescription' is defined but never used.", "'Label' is defined but never used.", "'Heart' is defined but never used.", "'Gift' is defined but never used.", "'CreditCard' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1035", "1036"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'useEffect' is defined but never used.", "'User' is defined but never used.", "'Phone' is defined but never used.", "'Mail' is defined but never used.", "'AlertCircle' is defined but never used.", ["1037", "1038"], "'Slider' is defined but never used.", "'Palette' is defined but never used.", "'Save' is defined but never used.", "'Eye' is defined but never used.", "'Download' is defined but never used.", "'Share2' is defined but never used.", "'setTotalPrice' is assigned a value but never used.", ["1039", "1040"], "'Package' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'Textarea' is defined but never used.", "'updatedMenuItems' is assigned a value but never used.", "'Progress' is defined but never used.", "'Input' is defined but never used.", "'AdminQuickNav' is defined but never used.", "'Shield' is defined but never used.", "'Upload' is defined but never used.", "'Edit' is defined but never used.", "'Search' is defined but never used.", "'DollarSign' is defined but never used.", "'ArrowRight' is defined but never used.", "'Building' is defined but never used.", "'DialogTrigger' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["1041"], ["1042", "1043"], ["1044", "1045"], ["1046", "1047"], ["1048", "1049"], "'data' is assigned a value but never used.", ["1050", "1051"], ["1052", "1053"], ["1054", "1055"], "'School' is defined but never used.", ["1056", "1057"], ["1058", "1059"], "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'MessageSquare' is defined but never used.", "'Star' is defined but never used.", "'setSelectedPriority' is assigned a value but never used.", ["1060", "1061"], "'Separator' is defined but never used.", ["1062", "1063"], ["1064", "1065"], ["1066", "1067"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'params' is assigned a value but never used.", "'error' is defined but never used.", "'TrendingUp' is defined but never used.", "'ShoppingBag' is defined but never used.", "'Clock' is defined but never used.", "'CheckCircle' is defined but never used.", ["1068", "1069"], ["1070", "1071"], "'FileText' is defined but never used.", "'Users' is defined but never used.", "'Settings' is defined but never used.", ["1072", "1073"], ["1074", "1075"], "'loading' is assigned a value but never used.", ["1076", "1077"], ["1078", "1079"], ["1080", "1081"], "'Ruler' is defined but never used.", "'Plus' is defined but never used.", "'Avatar' is defined but never used.", "'AvatarFallback' is defined but never used.", "'getInitials' is assigned a value but never used.", "'CardTitle' is defined but never used.", "'Badge' is defined but never used.", "'Bot' is defined but never used.", "'Sparkles' is defined but never used.", "'Link' is defined but never used.", ["1082", "1083"], "'designData' is defined but never used.", "'Languages' is defined but never used.", "'CardHeader' is defined but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'Check' is defined but never used.", ["1084", "1085"], ["1086", "1087"], "'password' is defined but never used.", ["1088", "1089"], "React Hook useEffect has missing dependencies: 'addMockNotification' and 'loadNotifications'. Either include them or remove the dependency array.", ["1090"], ["1091", "1092"], ["1093", "1094"], ["1095", "1096"], ["1097", "1098"], ["1099", "1100"], ["1101", "1102"], ["1103", "1104"], ["1105", "1106"], ["1107", "1108"], ["1109", "1110"], ["1111", "1112"], ["1113", "1114"], ["1115", "1116"], ["1117", "1118"], ["1119", "1120"], ["1121", "1122"], ["1123", "1124"], ["1125", "1126"], ["1127", "1128"], ["1129", "1130"], ["1131", "1132"], ["1133", "1134"], ["1135", "1136"], ["1137", "1138"], ["1139", "1140"], ["1141", "1142"], "'MockOrder' is defined but never used.", ["1143", "1144"], ["1145", "1146"], "'XCircle' is defined but never used.", ["1147", "1148"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["1149"], ["1150", "1151"], ["1152", "1153"], ["1154", "1155"], "'Globe' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSchools'. Either include it or remove the dependency array.", ["1156"], "'setLoading' is assigned a value but never used.", ["1157", "1158"], "'confirm_new_password' is assigned a value but never used.", ["1159", "1160"], ["1161", "1162"], ["1163", "1164"], ["1165", "1166"], ["1167", "1168"], "'MapPin' is defined but never used.", ["1169", "1170"], ["1171", "1172"], ["1173", "1174"], "'confirmPassword' is assigned a value but never used.", ["1175", "1176"], "'getRoleIcon' is assigned a value but never used.", ["1177", "1178"], {"messageId": "1179", "fix": "1180", "desc": "1181"}, {"messageId": "1182", "fix": "1183", "desc": "1184"}, {"messageId": "1179", "fix": "1185", "desc": "1181"}, {"messageId": "1182", "fix": "1186", "desc": "1184"}, {"messageId": "1179", "fix": "1187", "desc": "1181"}, {"messageId": "1182", "fix": "1188", "desc": "1184"}, {"desc": "1189", "fix": "1190"}, {"messageId": "1179", "fix": "1191", "desc": "1181"}, {"messageId": "1182", "fix": "1192", "desc": "1184"}, {"messageId": "1179", "fix": "1193", "desc": "1181"}, {"messageId": "1182", "fix": "1194", "desc": "1184"}, {"messageId": "1179", "fix": "1195", "desc": "1181"}, {"messageId": "1182", "fix": "1196", "desc": "1184"}, {"messageId": "1179", "fix": "1197", "desc": "1181"}, {"messageId": "1182", "fix": "1198", "desc": "1184"}, {"messageId": "1179", "fix": "1199", "desc": "1181"}, {"messageId": "1182", "fix": "1200", "desc": "1184"}, {"messageId": "1179", "fix": "1201", "desc": "1181"}, {"messageId": "1182", "fix": "1202", "desc": "1184"}, {"messageId": "1179", "fix": "1203", "desc": "1181"}, {"messageId": "1182", "fix": "1204", "desc": "1184"}, {"messageId": "1179", "fix": "1205", "desc": "1181"}, {"messageId": "1182", "fix": "1206", "desc": "1184"}, {"messageId": "1179", "fix": "1207", "desc": "1181"}, {"messageId": "1182", "fix": "1208", "desc": "1184"}, {"messageId": "1179", "fix": "1209", "desc": "1181"}, {"messageId": "1182", "fix": "1210", "desc": "1184"}, {"messageId": "1179", "fix": "1211", "desc": "1181"}, {"messageId": "1182", "fix": "1212", "desc": "1184"}, {"messageId": "1179", "fix": "1213", "desc": "1181"}, {"messageId": "1182", "fix": "1214", "desc": "1184"}, {"messageId": "1179", "fix": "1215", "desc": "1181"}, {"messageId": "1182", "fix": "1216", "desc": "1184"}, {"messageId": "1179", "fix": "1217", "desc": "1181"}, {"messageId": "1182", "fix": "1218", "desc": "1184"}, {"messageId": "1179", "fix": "1219", "desc": "1181"}, {"messageId": "1182", "fix": "1220", "desc": "1184"}, {"messageId": "1179", "fix": "1221", "desc": "1181"}, {"messageId": "1182", "fix": "1222", "desc": "1184"}, {"messageId": "1179", "fix": "1223", "desc": "1181"}, {"messageId": "1182", "fix": "1224", "desc": "1184"}, {"messageId": "1179", "fix": "1225", "desc": "1181"}, {"messageId": "1182", "fix": "1226", "desc": "1184"}, {"messageId": "1179", "fix": "1227", "desc": "1181"}, {"messageId": "1182", "fix": "1228", "desc": "1184"}, {"messageId": "1179", "fix": "1229", "desc": "1181"}, {"messageId": "1182", "fix": "1230", "desc": "1184"}, {"messageId": "1179", "fix": "1231", "desc": "1181"}, {"messageId": "1182", "fix": "1232", "desc": "1184"}, {"messageId": "1179", "fix": "1233", "desc": "1181"}, {"messageId": "1182", "fix": "1234", "desc": "1184"}, {"messageId": "1179", "fix": "1235", "desc": "1181"}, {"messageId": "1182", "fix": "1236", "desc": "1184"}, {"messageId": "1179", "fix": "1237", "desc": "1181"}, {"messageId": "1182", "fix": "1238", "desc": "1184"}, {"desc": "1239", "fix": "1240"}, {"messageId": "1179", "fix": "1241", "desc": "1181"}, {"messageId": "1182", "fix": "1242", "desc": "1184"}, {"messageId": "1179", "fix": "1243", "desc": "1181"}, {"messageId": "1182", "fix": "1244", "desc": "1184"}, {"messageId": "1179", "fix": "1245", "desc": "1181"}, {"messageId": "1182", "fix": "1246", "desc": "1184"}, {"messageId": "1179", "fix": "1247", "desc": "1181"}, {"messageId": "1182", "fix": "1248", "desc": "1184"}, {"messageId": "1179", "fix": "1249", "desc": "1181"}, {"messageId": "1182", "fix": "1250", "desc": "1184"}, {"messageId": "1179", "fix": "1251", "desc": "1181"}, {"messageId": "1182", "fix": "1252", "desc": "1184"}, {"messageId": "1179", "fix": "1253", "desc": "1181"}, {"messageId": "1182", "fix": "1254", "desc": "1184"}, {"messageId": "1179", "fix": "1255", "desc": "1181"}, {"messageId": "1182", "fix": "1256", "desc": "1184"}, {"messageId": "1179", "fix": "1257", "desc": "1181"}, {"messageId": "1182", "fix": "1258", "desc": "1184"}, {"messageId": "1179", "fix": "1259", "desc": "1181"}, {"messageId": "1182", "fix": "1260", "desc": "1184"}, {"messageId": "1179", "fix": "1261", "desc": "1181"}, {"messageId": "1182", "fix": "1262", "desc": "1184"}, {"messageId": "1179", "fix": "1263", "desc": "1181"}, {"messageId": "1182", "fix": "1264", "desc": "1184"}, {"messageId": "1179", "fix": "1265", "desc": "1181"}, {"messageId": "1182", "fix": "1266", "desc": "1184"}, {"messageId": "1179", "fix": "1267", "desc": "1181"}, {"messageId": "1182", "fix": "1268", "desc": "1184"}, {"messageId": "1179", "fix": "1269", "desc": "1181"}, {"messageId": "1182", "fix": "1270", "desc": "1184"}, {"messageId": "1179", "fix": "1271", "desc": "1181"}, {"messageId": "1182", "fix": "1272", "desc": "1184"}, {"messageId": "1179", "fix": "1273", "desc": "1181"}, {"messageId": "1182", "fix": "1274", "desc": "1184"}, {"messageId": "1179", "fix": "1275", "desc": "1181"}, {"messageId": "1182", "fix": "1276", "desc": "1184"}, {"messageId": "1179", "fix": "1277", "desc": "1181"}, {"messageId": "1182", "fix": "1278", "desc": "1184"}, {"messageId": "1179", "fix": "1279", "desc": "1181"}, {"messageId": "1182", "fix": "1280", "desc": "1184"}, {"messageId": "1179", "fix": "1281", "desc": "1181"}, {"messageId": "1182", "fix": "1282", "desc": "1184"}, {"messageId": "1179", "fix": "1283", "desc": "1181"}, {"messageId": "1182", "fix": "1284", "desc": "1184"}, {"messageId": "1179", "fix": "1285", "desc": "1181"}, {"messageId": "1182", "fix": "1286", "desc": "1184"}, {"messageId": "1179", "fix": "1287", "desc": "1181"}, {"messageId": "1182", "fix": "1288", "desc": "1184"}, {"messageId": "1179", "fix": "1289", "desc": "1181"}, {"messageId": "1182", "fix": "1290", "desc": "1184"}, {"messageId": "1179", "fix": "1291", "desc": "1181"}, {"messageId": "1182", "fix": "1292", "desc": "1184"}, {"messageId": "1179", "fix": "1293", "desc": "1181"}, {"messageId": "1182", "fix": "1294", "desc": "1184"}, {"messageId": "1179", "fix": "1295", "desc": "1181"}, {"messageId": "1182", "fix": "1296", "desc": "1184"}, {"messageId": "1179", "fix": "1297", "desc": "1181"}, {"messageId": "1182", "fix": "1298", "desc": "1184"}, {"desc": "1299", "fix": "1300"}, {"messageId": "1179", "fix": "1301", "desc": "1181"}, {"messageId": "1182", "fix": "1302", "desc": "1184"}, {"messageId": "1179", "fix": "1303", "desc": "1181"}, {"messageId": "1182", "fix": "1304", "desc": "1184"}, {"messageId": "1179", "fix": "1305", "desc": "1181"}, {"messageId": "1182", "fix": "1306", "desc": "1184"}, {"desc": "1307", "fix": "1308"}, {"messageId": "1179", "fix": "1309", "desc": "1181"}, {"messageId": "1182", "fix": "1310", "desc": "1184"}, {"messageId": "1179", "fix": "1311", "desc": "1181"}, {"messageId": "1182", "fix": "1312", "desc": "1184"}, {"messageId": "1179", "fix": "1313", "desc": "1181"}, {"messageId": "1182", "fix": "1314", "desc": "1184"}, {"messageId": "1179", "fix": "1315", "desc": "1181"}, {"messageId": "1182", "fix": "1316", "desc": "1184"}, {"messageId": "1179", "fix": "1317", "desc": "1181"}, {"messageId": "1182", "fix": "1318", "desc": "1184"}, {"messageId": "1179", "fix": "1319", "desc": "1181"}, {"messageId": "1182", "fix": "1320", "desc": "1184"}, {"messageId": "1179", "fix": "1321", "desc": "1181"}, {"messageId": "1182", "fix": "1322", "desc": "1184"}, {"messageId": "1179", "fix": "1323", "desc": "1181"}, {"messageId": "1182", "fix": "1324", "desc": "1184"}, {"messageId": "1179", "fix": "1325", "desc": "1181"}, {"messageId": "1182", "fix": "1326", "desc": "1184"}, {"messageId": "1179", "fix": "1327", "desc": "1181"}, {"messageId": "1182", "fix": "1328", "desc": "1184"}, {"messageId": "1179", "fix": "1329", "desc": "1181"}, {"messageId": "1182", "fix": "1330", "desc": "1184"}, "suggestUnknown", {"range": "1331", "text": "1332"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1333", "text": "1334"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1335", "text": "1332"}, {"range": "1336", "text": "1334"}, {"range": "1337", "text": "1332"}, {"range": "1338", "text": "1334"}, "Update the dependencies array to be: [fetchProducts]", {"range": "1339", "text": "1340"}, {"range": "1341", "text": "1332"}, {"range": "1342", "text": "1334"}, {"range": "1343", "text": "1332"}, {"range": "1344", "text": "1334"}, {"range": "1345", "text": "1332"}, {"range": "1346", "text": "1334"}, {"range": "1347", "text": "1332"}, {"range": "1348", "text": "1334"}, {"range": "1349", "text": "1332"}, {"range": "1350", "text": "1334"}, {"range": "1351", "text": "1332"}, {"range": "1352", "text": "1334"}, {"range": "1353", "text": "1332"}, {"range": "1354", "text": "1334"}, {"range": "1355", "text": "1332"}, {"range": "1356", "text": "1334"}, {"range": "1357", "text": "1332"}, {"range": "1358", "text": "1334"}, {"range": "1359", "text": "1332"}, {"range": "1360", "text": "1334"}, {"range": "1361", "text": "1332"}, {"range": "1362", "text": "1334"}, {"range": "1363", "text": "1332"}, {"range": "1364", "text": "1334"}, {"range": "1365", "text": "1332"}, {"range": "1366", "text": "1334"}, {"range": "1367", "text": "1332"}, {"range": "1368", "text": "1334"}, {"range": "1369", "text": "1332"}, {"range": "1370", "text": "1334"}, {"range": "1371", "text": "1332"}, {"range": "1372", "text": "1334"}, {"range": "1373", "text": "1332"}, {"range": "1374", "text": "1334"}, {"range": "1375", "text": "1332"}, {"range": "1376", "text": "1334"}, {"range": "1377", "text": "1332"}, {"range": "1378", "text": "1334"}, {"range": "1379", "text": "1332"}, {"range": "1380", "text": "1334"}, {"range": "1381", "text": "1332"}, {"range": "1382", "text": "1334"}, {"range": "1383", "text": "1332"}, {"range": "1384", "text": "1334"}, {"range": "1385", "text": "1332"}, {"range": "1386", "text": "1334"}, {"range": "1387", "text": "1332"}, {"range": "1388", "text": "1334"}, "Update the dependencies array to be: [addMockNotification, loadNotifications, user]", {"range": "1389", "text": "1390"}, {"range": "1391", "text": "1332"}, {"range": "1392", "text": "1334"}, {"range": "1393", "text": "1332"}, {"range": "1394", "text": "1334"}, {"range": "1395", "text": "1332"}, {"range": "1396", "text": "1334"}, {"range": "1397", "text": "1332"}, {"range": "1398", "text": "1334"}, {"range": "1399", "text": "1332"}, {"range": "1400", "text": "1334"}, {"range": "1401", "text": "1332"}, {"range": "1402", "text": "1334"}, {"range": "1403", "text": "1332"}, {"range": "1404", "text": "1334"}, {"range": "1405", "text": "1332"}, {"range": "1406", "text": "1334"}, {"range": "1407", "text": "1332"}, {"range": "1408", "text": "1334"}, {"range": "1409", "text": "1332"}, {"range": "1410", "text": "1334"}, {"range": "1411", "text": "1332"}, {"range": "1412", "text": "1334"}, {"range": "1413", "text": "1332"}, {"range": "1414", "text": "1334"}, {"range": "1415", "text": "1332"}, {"range": "1416", "text": "1334"}, {"range": "1417", "text": "1332"}, {"range": "1418", "text": "1334"}, {"range": "1419", "text": "1332"}, {"range": "1420", "text": "1334"}, {"range": "1421", "text": "1332"}, {"range": "1422", "text": "1334"}, {"range": "1423", "text": "1332"}, {"range": "1424", "text": "1334"}, {"range": "1425", "text": "1332"}, {"range": "1426", "text": "1334"}, {"range": "1427", "text": "1332"}, {"range": "1428", "text": "1334"}, {"range": "1429", "text": "1332"}, {"range": "1430", "text": "1334"}, {"range": "1431", "text": "1332"}, {"range": "1432", "text": "1334"}, {"range": "1433", "text": "1332"}, {"range": "1434", "text": "1334"}, {"range": "1435", "text": "1332"}, {"range": "1436", "text": "1334"}, {"range": "1437", "text": "1332"}, {"range": "1438", "text": "1334"}, {"range": "1439", "text": "1332"}, {"range": "1440", "text": "1334"}, {"range": "1441", "text": "1332"}, {"range": "1442", "text": "1334"}, {"range": "1443", "text": "1332"}, {"range": "1444", "text": "1334"}, {"range": "1445", "text": "1332"}, {"range": "1446", "text": "1334"}, {"range": "1447", "text": "1332"}, {"range": "1448", "text": "1334"}, "Update the dependencies array to be: [fetchOrders]", {"range": "1449", "text": "1450"}, {"range": "1451", "text": "1332"}, {"range": "1452", "text": "1334"}, {"range": "1453", "text": "1332"}, {"range": "1454", "text": "1334"}, {"range": "1455", "text": "1332"}, {"range": "1456", "text": "1334"}, "Update the dependencies array to be: [searchTerm, cityFilter, statusFilter, sortBy, sortOrder, fetchSchools]", {"range": "1457", "text": "1458"}, {"range": "1459", "text": "1332"}, {"range": "1460", "text": "1334"}, {"range": "1461", "text": "1332"}, {"range": "1462", "text": "1334"}, {"range": "1463", "text": "1332"}, {"range": "1464", "text": "1334"}, {"range": "1465", "text": "1332"}, {"range": "1466", "text": "1334"}, {"range": "1467", "text": "1332"}, {"range": "1468", "text": "1334"}, {"range": "1469", "text": "1332"}, {"range": "1470", "text": "1334"}, {"range": "1471", "text": "1332"}, {"range": "1472", "text": "1334"}, {"range": "1473", "text": "1332"}, {"range": "1474", "text": "1334"}, {"range": "1475", "text": "1332"}, {"range": "1476", "text": "1334"}, {"range": "1477", "text": "1332"}, {"range": "1478", "text": "1334"}, {"range": "1479", "text": "1332"}, {"range": "1480", "text": "1334"}, [884, 887], "unknown", [884, 887], "never", [14209, 14212], [14209, 14212], [4086, 4089], [4086, 4089], [3764, 3766], "[fetchProducts]", [5009, 5012], [5009, 5012], [5060, 5063], [5060, 5063], [7278, 7281], [7278, 7281], [7493, 7496], [7493, 7496], [8811, 8814], [8811, 8814], [9026, 9029], [9026, 9029], [11457, 11460], [11457, 11460], [1137, 1140], [1137, 1140], [1259, 1262], [1259, 1262], [7587, 7590], [7587, 7590], [3768, 3771], [3768, 3771], [3889, 3892], [3889, 3892], [4447, 4450], [4447, 4450], [9781, 9784], [9781, 9784], [10516, 10519], [10516, 10519], [577, 580], [577, 580], [726, 729], [726, 729], [1695, 1698], [1695, 1698], [11435, 11438], [11435, 11438], [12000, 12003], [12000, 12003], [734, 737], [734, 737], [8437, 8440], [8437, 8440], [8642, 8645], [8642, 8645], [935, 938], [935, 938], [2084, 2090], "[addMockNotification, loadNotifications, user]", [1111, 1114], [1111, 1114], [1453, 1456], [1453, 1456], [1785, 1788], [1785, 1788], [441, 444], [441, 444], [777, 780], [777, 780], [372, 375], [372, 375], [446, 449], [446, 449], [3998, 4001], [3998, 4001], [4172, 4175], [4172, 4175], [4345, 4348], [4345, 4348], [4534, 4537], [4534, 4537], [4880, 4883], [4880, 4883], [10354, 10357], [10354, 10357], [170, 173], [170, 173], [703, 706], [703, 706], [1620, 1623], [1620, 1623], [2160, 2163], [2160, 2163], [4226, 4229], [4226, 4229], [4285, 4288], [4285, 4288], [4666, 4669], [4666, 4669], [8863, 8866], [8863, 8866], [9086, 9089], [9086, 9089], [1580, 1583], [1580, 1583], [1633, 1636], [1633, 1636], [3972, 3975], [3972, 3975], [4624, 4627], [4624, 4627], [1301, 1304], [1301, 1304], [1355, 1358], [1355, 1358], [1411, 1414], [1411, 1414], [3524, 3526], "[fetchOrders]", [3612, 3615], [3612, 3615], [5912, 5915], [5912, 5915], [5961, 5964], [5961, 5964], [2427, 2484], "[searchTerm, cityFilter, statusFilter, sortBy, sortOrder, fetchSchools]", [1183, 1186], [1183, 1186], [4663, 4666], [4663, 4666], [7825, 7828], [7825, 7828], [15906, 15909], [15906, 15909], [1805, 1808], [1805, 1808], [2299, 2302], [2299, 2302], [2681, 2684], [2681, 2684], [2956, 2959], [2956, 2959], [811, 814], [811, 814], [3969, 3972], [3969, 3972], [7532, 7535], [7532, 7535]]