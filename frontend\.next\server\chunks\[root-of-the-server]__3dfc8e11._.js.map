{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockOrder {\n  id: string\n  order_number: string\n  customer_id: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  items: MockOrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_id?: string\n  school_name?: string\n}\n\nexport interface MockOrderItem {\n  id: string\n  order_id: string\n  product_id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// بيانات وهمية للطلبات\nexport const mockOrders: MockOrder[] = [\n  {\n    id: '1',\n    order_number: 'GT-240120-001',\n    customer_id: 'student-1',\n    customer_name: 'أحمد محمد علي',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-50-123-4567',\n    status: 'in_production',\n    items: [\n      {\n        id: '1',\n        order_id: '1',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'أسود',\n          size: 'L',\n          embroidery: 'أحمد علي - بكالوريوس هندسة'\n        }\n      },\n      {\n        id: '2',\n        order_id: '1',\n        product_id: '2',\n        product_name: 'قبعة التخرج الأكاديمية',\n        product_image: '/images/products/cap-academic-1.jpg',\n        category: 'cap',\n        quantity: 1,\n        unit_price: 89.99,\n        total_price: 89.99,\n        customizations: {\n          color: 'أسود',\n          size: 'M'\n        }\n      }\n    ],\n    subtotal: 389.98,\n    tax: 19.50,\n    shipping_cost: 25.00,\n    total: 434.48,\n    payment_status: 'paid',\n    payment_method: 'credit_card',\n    shipping_address: {\n      street: 'شارع الجامعة، مبنى 12، شقة 304',\n      city: 'العين',\n      state: 'أبوظبي',\n      postal_code: '17666',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-001-2024',\n    notes: 'يرجى التسليم قبل حفل التخرج',\n    created_at: '2024-01-20T10:30:00Z',\n    updated_at: '2024-01-22T14:15:00Z',\n    delivery_date: '2024-02-15T00:00:00Z',\n    school_id: '1',\n    school_name: 'جامعة الإمارات العربية المتحدة'\n  },\n  {\n    id: '2',\n    order_number: 'GT-**********',\n    customer_id: 'student-2',\n    customer_name: 'فاطمة سالم الزهراني',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-56-789-0123',\n    status: 'delivered',\n    items: [\n      {\n        id: '3',\n        order_id: '2',\n        product_id: '3',\n        product_name: 'ثوب التخرج المميز',\n        product_image: '/images/products/gown-premium-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 399.99,\n        total_price: 399.99,\n        customizations: {\n          color: 'أزرق داكن',\n          size: 'M',\n          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n        }\n      }\n    ],\n    subtotal: 399.99,\n    tax: 20.00,\n    shipping_cost: 30.00,\n    total: 449.99,\n    payment_status: 'paid',\n    payment_method: 'bank_transfer',\n    shipping_address: {\n      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n      city: 'الشارقة',\n      state: 'الشارقة',\n      postal_code: '27272',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-002-2024',\n    created_at: '2024-01-21T09:15:00Z',\n    updated_at: '2024-01-25T16:30:00Z',\n    delivery_date: '2024-01-28T00:00:00Z',\n    school_id: '2',\n    school_name: 'الجامعة الأمريكية في الشارقة'\n  },\n  {\n    id: '3',\n    order_number: 'GT-**********',\n    customer_id: 'student-3',\n    customer_name: 'خالد عبدالله المنصوري',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-52-456-7890',\n    status: 'pending',\n    items: [\n      {\n        id: '4',\n        order_id: '3',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'بورجوندي',\n          size: 'XL'\n        }\n      },\n      {\n        id: '5',\n        order_id: '3',\n        product_id: '4',\n        product_name: 'وشاح التخرج المطرز',\n        product_image: '/images/products/stole-embroidered-1.jpg',\n        category: 'stole',\n        quantity: 1,\n        unit_price: 149.99,\n        total_price: 149.99,\n        customizations: {\n          color: 'ذهبي',\n          embroidery: 'كلية الهندسة'\n        }\n      }\n    ],\n    subtotal: 449.98,\n    tax: 22.50,\n    shipping_cost: 25.00,\n    total: 497.48,\n    payment_status: 'pending',\n    shipping_address: {\n      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n      city: 'دبي',\n      state: 'دبي',\n      postal_code: '391186',\n      country: 'الإمارات العربية المتحدة'\n    },\n    created_at: '2024-01-22T14:45:00Z',\n    updated_at: '2024-01-22T14:45:00Z',\n    school_id: '3',\n    school_name: 'جامعة زايد'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;AAiJ1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,aAAa;IACf;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAA8E,EAAU;QACnH,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;AACF", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/categories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager, MockCategory } from '@/lib/mockData'\n\n// GET - جلب جميع الفئات\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const includeInactive = searchParams.get('include_inactive') === 'true'\n\n    // جلب البيانات الوهمية\n    let categories = MockDataManager.getCategories()\n\n    // تطبيق الفلاتر\n    if (!includeInactive) {\n      categories = categories.filter(category => category.is_active)\n    }\n\n    // ترتيب حسب order_index\n    categories.sort((a, b) => a.order_index - b.order_index)\n\n    return NextResponse.json({ \n      categories,\n      total: categories.length\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة فئة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name_ar,\n      name_en,\n      name_fr,\n      slug,\n      icon,\n      description,\n      is_active,\n      order_index\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name_ar || !slug) {\n      return NextResponse.json(\n        { error: 'الاسم العربي والرابط المختصر مطلوبان' },\n        { status: 400 }\n      )\n    }\n\n    // جلب الفئات الحالية\n    const categories = MockDataManager.getCategories()\n\n    // التحقق من عدم تكرار الـ slug\n    const existingCategory = categories.find(category => category.slug === slug)\n    if (existingCategory) {\n      return NextResponse.json(\n        { error: 'الرابط المختصر موجود بالفعل' },\n        { status: 400 }\n      )\n    }\n\n    // إنشاء الفئة الجديدة\n    const newCategory: MockCategory = {\n      id: MockDataManager.generateId(),\n      name_ar,\n      name_en: name_en || undefined,\n      name_fr: name_fr || undefined,\n      slug,\n      icon: icon || undefined,\n      description: description || undefined,\n      is_active: is_active ?? true,\n      order_index: order_index || categories.length + 1,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n\n    // حفظ الفئة\n    categories.push(newCategory)\n    MockDataManager.saveCategories(categories)\n\n    return NextResponse.json({ \n      message: 'تم إضافة الفئة بنجاح',\n      category: newCategory \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,wBAAwB;QAEjE,uBAAuB;QACvB,IAAI,aAAa,wHAAA,CAAA,kBAAe,CAAC,aAAa;QAE9C,gBAAgB;QAChB,IAAI,CAAC,iBAAiB;YACpB,aAAa,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,SAAS;QAC/D;QAEA,wBAAwB;QACxB,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,OAAO,WAAW,MAAM;QAC1B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,SAAS,EACT,WAAW,EACZ,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,aAAa;QAEhD,+BAA+B;QAC/B,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;QACvE,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,cAA4B;YAChC,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B;YACA,SAAS,WAAW;YACpB,SAAS,WAAW;YACpB;YACA,MAAM,QAAQ;YACd,aAAa,eAAe;YAC5B,WAAW,aAAa;YACxB,aAAa,eAAe,WAAW,MAAM,GAAG;YAChD,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,YAAY;QACZ,WAAW,IAAI,CAAC;QAChB,wHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU;QACZ,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}