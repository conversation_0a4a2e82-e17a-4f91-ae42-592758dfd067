import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockOrder } from '@/lib/mockData'

// GET - جلب طلب واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orders = MockDataManager.getOrders()
    const order = orders.find(o => o.id === params.id)

    if (!order) {
      return NextResponse.json(
        { error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({ order })
  } catch (error) {
    console.error('Error fetching order:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب الطلب' },
      { status: 500 }
    )
  }
}

// PUT - تحديث طلب
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      status,
      payment_status,
      tracking_number,
      delivery_date,
      notes,
      shipping_address
    } = body

    // جلب الطلبات الحالية
    const orders = MockDataManager.getOrders()
    const orderIndex = orders.findIndex(o => o.id === params.id)

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    // تحديث الطلب
    const updatedOrder = {
      ...orders[orderIndex],
      ...(status && { status }),
      ...(payment_status && { payment_status }),
      ...(tracking_number && { tracking_number }),
      ...(delivery_date && { delivery_date }),
      ...(notes && { notes }),
      ...(shipping_address && { shipping_address }),
      updated_at: new Date().toISOString()
    }

    orders[orderIndex] = updatedOrder
    MockDataManager.saveOrders(orders)

    return NextResponse.json({ 
      message: 'تم تحديث الطلب بنجاح',
      order: updatedOrder 
    })
  } catch (error) {
    console.error('Error updating order:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث الطلب' },
      { status: 500 }
    )
  }
}

// DELETE - حذف طلب
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب الطلبات الحالية
    const orders = MockDataManager.getOrders()
    const orderIndex = orders.findIndex(o => o.id === params.id)

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من إمكانية الحذف
    const order = orders[orderIndex]
    if (order.status === 'delivered' || order.status === 'shipped') {
      return NextResponse.json(
        { error: 'لا يمكن حذف طلب تم تسليمه أو شحنه' },
        { status: 400 }
      )
    }

    // حذف الطلب
    orders.splice(orderIndex, 1)
    MockDataManager.saveOrders(orders)

    return NextResponse.json({ 
      message: 'تم حذف الطلب بنجاح' 
    })
  } catch (error) {
    console.error('Error deleting order:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف الطلب' },
      { status: 500 }
    )
  }
}
