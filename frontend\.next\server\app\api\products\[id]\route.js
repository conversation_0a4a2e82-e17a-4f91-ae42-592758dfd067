"use strict";(()=>{var e={};e.id=3856,e.ids=[3856],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11382:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>d,PUT:()=>u});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),p=t(38561);async function d(e,{params:r}){try{let e=p.CN.getProducts().find(e=>e.id===r.id);if(!e)return i.NextResponse.json({error:"المنتج غير موجود"},{status:404});return i.NextResponse.json({product:e})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function u(e,{params:r}){try{let{name:t,description:s,category:o,price:n,rental_price:a,colors:d,sizes:u,images:c,stock_quantity:l,is_available:x,features:v,specifications:f}=await e.json(),g=p.CN.getProducts(),j=g.findIndex(e=>e.id===r.id);if(-1===j)return i.NextResponse.json({error:"المنتج غير موجود"},{status:404});let m={...g[j],name:t||g[j].name,description:s||g[j].description,category:o||g[j].category,price:void 0!==n?parseFloat(n):g[j].price,rental_price:void 0!==a?a?parseFloat(a):void 0:g[j].rental_price,colors:d||g[j].colors,sizes:u||g[j].sizes,images:c||g[j].images,stock_quantity:void 0!==l?parseInt(l):g[j].stock_quantity,is_available:void 0!==x?x:g[j].is_available,features:v||g[j].features,specifications:f||g[j].specifications,updated_at:new Date().toISOString()};return g[j]=m,p.CN.saveProducts(g),i.NextResponse.json({message:"تم تحديث المنتج بنجاح",product:m})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e,{params:r}){try{let e=p.CN.getProducts(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return i.NextResponse.json({error:"المنتج غير موجود"},{status:404});return e.splice(t,1),p.CN.saveProducts(e),i.NextResponse.json({message:"تم حذف المنتج بنجاح"})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:f}=l;function g(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(11382));module.exports=s})();