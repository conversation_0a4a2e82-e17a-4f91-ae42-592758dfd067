(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7949],{4516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>l});var t=s(95155);s(12115);var r=s(60704),i=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",a),...s})}},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>l});var t=s(95155);s(12115);var r=s(55863),i=s(59434);function l(e){let{className:a,value:s,...l}=e;return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...l,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},27809:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},28564:(e,a,s)=>{Promise.resolve().then(s.bind(s,87091))},37108:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},38564:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},55863:(e,a,s)=>{"use strict";s.d(a,{C1:()=>f,bL:()=>N});var t=s(12115),r=s(46081),i=s(63655),l=s(95155),c="Progress",[n,d]=(0,r.A)(c),[x,m]=n(c),o=t.forwardRef((e,a)=>{var s,t,r,c;let{__scopeProgress:n,value:d=null,max:m,getValueLabel:o=j,...h}=e;(m||0===m)&&!v(m)&&console.error((s="".concat(m),t="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let u=v(m)?m:100;null===d||b(d,u)||console.error((r="".concat(d),c="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(c,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let N=b(d,u)?d:null,f=g(N)?o(N,u):void 0;return(0,l.jsx)(x,{scope:n,value:N,max:u,children:(0,l.jsx)(i.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":g(N)?N:void 0,"aria-valuetext":f,role:"progressbar","data-state":p(N,u),"data-value":null!=N?N:void 0,"data-max":u,...h,ref:a})})});o.displayName=c;var h="ProgressIndicator",u=t.forwardRef((e,a)=>{var s;let{__scopeProgress:t,...r}=e,c=m(h,t);return(0,l.jsx)(i.sG.div,{"data-state":p(c.value,c.max),"data-value":null!=(s=c.value)?s:void 0,"data-max":c.max,...r,ref:a})});function j(e,a){return"".concat(Math.round(e/a*100),"%")}function p(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function b(e,a){return g(e)&&!isNaN(e)&&e<=a&&e>=0}u.displayName=h;var N=o,f=u},60704:(e,a,s)=>{"use strict";s.d(a,{B8:()=>C,UC:()=>M,bL:()=>$,l9:()=>D});var t=s(12115),r=s(85185),i=s(46081),l=s(89196),c=s(28905),n=s(63655),d=s(94315),x=s(5845),m=s(61285),o=s(95155),h="Tabs",[u,j]=(0,i.A)(h,[l.RG]),p=(0,l.RG)(),[g,v]=u(h),b=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:c,activationMode:u="automatic",...j}=e,p=(0,d.jH)(c),[v,b]=(0,x.i)({prop:t,onChange:r,defaultProp:null!=i?i:"",caller:h});return(0,o.jsx)(g,{scope:s,baseId:(0,m.B)(),value:v,onValueChange:b,orientation:l,dir:p,activationMode:u,children:(0,o.jsx)(n.sG.div,{dir:p,"data-orientation":l,...j,ref:a})})});b.displayName=h;var N="TabsList",f=t.forwardRef((e,a)=>{let{__scopeTabs:s,loop:t=!0,...r}=e,i=v(N,s),c=p(s);return(0,o.jsx)(l.bL,{asChild:!0,...c,orientation:i.orientation,dir:i.dir,loop:t,children:(0,o.jsx)(n.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:a})})});f.displayName=N;var y="TabsTrigger",w=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,disabled:i=!1,...c}=e,d=v(y,s),x=p(s),m=_(d.baseId,t),h=z(d.baseId,t),u=t===d.value;return(0,o.jsx)(l.q7,{asChild:!0,...x,focusable:!i,active:u,children:(0,o.jsx)(n.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...c,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||i||!e||d.onValueChange(t)})})})});w.displayName=y;var k="TabsContent",A=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,forceMount:i,children:l,...d}=e,x=v(k,s),m=_(x.baseId,r),h=z(x.baseId,r),u=r===x.value,j=t.useRef(u);return t.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(c.C,{present:i||u,children:s=>{let{present:t}=s;return(0,o.jsx)(n.sG.div,{"data-state":u?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:h,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:j.current?"0s":void 0},children:t&&l})}})});function _(e,a){return"".concat(e,"-trigger-").concat(a)}function z(e,a){return"".concat(e,"-content-").concat(a)}A.displayName=k;var $=b,C=f,D=w,M=A},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>i});var t=s(95155);s(12115);var r=s(59434);function i(e){let{className:a,type:s,...i}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},66516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},71366:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},81586:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},84616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87091:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Z});var t=s(95155),r=s(12115),i=s(40283),l=s(37784),c=s(66695),n=s(30285),d=s(26126),x=s(24944),m=s(17313),o=s(62523),h=s(23861),u=s(381),j=s(27809),p=s(51976),g=s(14186),v=s(38564),b=s(37108),N=s(47924),f=s(84616),y=s(87949),w=s(4516),k=s(81586),A=s(92657),_=s(91788),z=s(19420),$=s(71366),C=s(13717),D=s(66516),M=s(62525);function Z(){let{user:e,profile:a,loading:s}=(0,i.A)(),[Z,R]=(0,r.useState)("overview"),[L,B]=(0,r.useState)([]),[S,q]=(0,r.useState)([]),[T,V]=(0,r.useState)(!0);(0,r.useEffect)(()=>{B([{id:"1",status:"in_production",items:[{id:"1",name:"زي التخرج الكلاسيكي",image:"/api/placeholder/150/150",quantity:1,price:299.99}],total:299.99,created_at:"2024-01-15",delivery_date:"2024-02-01"},{id:"2",status:"delivered",items:[{id:"2",name:"قبعة التخرج المميزة",image:"/api/placeholder/150/150",quantity:1,price:89.99}],total:89.99,created_at:"2024-01-01",delivery_date:"2024-01-10"}]),q([{id:"1",name:"تصميمي المفضل",preview_image:"/api/placeholder/200/200",created_at:"2024-01-20",customizations:{}},{id:"2",name:"تصميم احتفال التخرج",preview_image:"/api/placeholder/200/200",created_at:"2024-01-18",customizations:{}}]),V(!1)},[]);let W=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"in_production":return"bg-purple-100 text-purple-800";case"shipped":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},E=e=>{switch(e){case"pending":return"في الانتظار";case"confirmed":return"مؤكد";case"in_production":return"قيد الإنتاج";case"shipped":return"تم الشحن";case"delivered":return"تم التسليم";default:return"غير معروف"}},I=e=>{switch(e){case"pending":return 20;case"confirmed":return 40;case"in_production":return 60;case"shipped":return 80;case"delivered":return 100;default:return 0}};return s||T?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:["مرحباً، ",(null==a?void 0:a.full_name)||"الطالب","! \uD83C\uDF93"]}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة طلباتك وتصاميمك المخصصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"الإشعارات"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الإعدادات"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلبات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:L.length})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"التصاميم المحفوظة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:S.length})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات النشطة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:L.filter(e=>"delivered"!==e.status).length})]}),(0,t.jsx)(g.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"النقاط المكتسبة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"1,250"})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,t.jsxs)(m.tU,{value:Z,onValueChange:R,children:[(0,t.jsxs)(m.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(m.Xi,{value:"overview",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"نظرة عامة"]}),(0,t.jsxs)(m.Xi,{value:"orders",className:"arabic-text",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"طلباتي"]}),(0,t.jsxs)(m.Xi,{value:"track-orders",className:"arabic-text",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"تتبع الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"designs",className:"arabic-text",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"تصاميمي"]}),(0,t.jsxs)(m.Xi,{value:"profile",className:"arabic-text",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الملف الشخصي"]})]}),(0,t.jsxs)(m.av,{value:"overview",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"الطلبات الأخيرة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"آخر الطلبات وحالتها الحالية"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)("div",{className:"space-y-4",children:L.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center",children:(0,t.jsx)(b.A,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.items.length," عنصر - ",e.total," درهم"]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)(d.E,{className:W(e.status),children:E(e.status)}),(0,t.jsx)("div",{className:"mt-2 w-32",children:(0,t.jsx)(x.k,{value:I(e.status),className:"h-2"})})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(n.$,{variant:"outline",className:"w-full arabic-text",children:"عرض جميع الطلبات"})})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إجراءات سريعة"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(n.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(f.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"طلب جديد"})]}),(0,t.jsxs)(n.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(y.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"تخصيص زي"})]}),(0,t.jsxs)(n.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(w.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الطلب"})]}),(0,t.jsxs)(n.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"الفواتير"})]})]})})]})]}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلباتي"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"طلب جديد"})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:L.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:["تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsx)(d.E,{className:W(e.status),children:E(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"space-y-3",children:e.items.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["الكمية: ",e.quantity," \xd7 ",e.price," درهم"]})]})]},e.id))}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تقدم الطلب"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[I(e.status),"%"]})]}),(0,t.jsx)(x.k,{value:I(e.status),className:"h-2"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"عرض التفاصيل"})]}),"shipped"===e.status&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الشحنة"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تحميل الفاتورة"})]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"track-orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تتبع الطلبات"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{placeholder:"أدخل رقم الطلب...",className:"w-64"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"بحث"})]})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:L.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:["تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsx)(d.E,{className:W(e.status),children:E(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:"مراحل الطلب"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم استلام الطلب"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString("ar-SA")})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(["processing","shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300")}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"قيد التحضير"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["processing","shipped","delivered"].includes(e.status)?"مكتمل":"في الانتظار"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(["shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300")}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم الشحن"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["shipped","delivered"].includes(e.status)?"مكتمل":"في الانتظار"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("delivered"===e.status?"bg-green-500":"bg-gray-300")}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم التسليم"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"delivered"===e.status?"مكتمل":"في الانتظار"})]})]})]})]}),"shipped"===e.status&&(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text mb-2",children:"معلومات الشحن"}),(0,t.jsxs)("p",{className:"text-sm arabic-text",children:["رقم التتبع: ",(0,t.jsxs)("span",{className:"font-mono",children:["TRK",e.id,"2024"]})]}),(0,t.jsx)("p",{className:"text-sm arabic-text",children:"الوقت المتوقع للوصول: 2-3 أيام عمل"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"اتصل بالدعم"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"محادثة مباشرة"})]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"designs",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تصاميمي المحفوظة"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تصميم جديد"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:S.map(e=>(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 rounded-t-lg"}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"font-medium arabic-text mb-2",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text mb-4",children:["تم الحفظ: ",new Date(e.created_at).toLocaleDateString("ar-SA")]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"عرض"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تعديل"})]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(M.A,{className:"h-4 w-4"})})]})]})]})},e.id))})]}),(0,t.jsx)(m.av,{value:"profile",className:"space-y-6 mt-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"الملف الشخصي"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إدارة معلوماتك الشخصية وإعدادات الحساب"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"الاسم الكامل"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:(null==a?void 0:a.full_name)||"غير محدد"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"البريد الإلكتروني"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded",children:(null==a?void 0:a.email)||"غير محدد"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"رقم الهاتف"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:(null==a?void 0:a.phone)||"غير محدد"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"اسم المدرسة"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:(null==a?void 0:a.school_name)||"غير محدد"})]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تعديل الملف الشخصي"})]})})]})})]})})]})]})]})}},91788:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,2632,7443,7784,8441,1684,7358],()=>a(28564)),_N_E=e.O()}]);