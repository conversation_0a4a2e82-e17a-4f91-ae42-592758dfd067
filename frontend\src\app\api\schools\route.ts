import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockSchool } from '@/lib/mockData'

// GET - جلب جميع المدارس
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('include_inactive') === 'true'
    const city = searchParams.get('city')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sort_by') || 'created_at'
    const sortOrder = searchParams.get('sort_order') || 'desc'

    // جلب البيانات الوهمية
    let schools = MockDataManager.getSchools()

    // تطبيق الفلاتر
    if (!includeInactive) {
      schools = schools.filter(school => school.is_active)
    }

    if (city) {
      schools = schools.filter(school => 
        school.city?.toLowerCase().includes(city.toLowerCase())
      )
    }

    if (search) {
      const searchLower = search.toLowerCase()
      schools = schools.filter(school => 
        school.name.toLowerCase().includes(searchLower) ||
        school.name_en?.toLowerCase().includes(searchLower) ||
        school.email?.toLowerCase().includes(searchLower) ||
        school.city?.toLowerCase().includes(searchLower)
      )
    }

    // ترتيب النتائج
    schools.sort((a, b) => {
      let aValue: any = a[sortBy as keyof MockSchool]
      let bValue: any = b[sortBy as keyof MockSchool]

      // معالجة القيم الفارغة
      if (aValue === undefined || aValue === null) aValue = ''
      if (bValue === undefined || bValue === null) bValue = ''

      // معالجة التواريخ
      if (sortBy === 'created_at' || sortBy === 'updated_at' || sortBy === 'graduation_date') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // معالجة الأرقام
      if (sortBy === 'student_count') {
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // إحصائيات سريعة
    const stats = {
      total: schools.length,
      active: schools.filter(s => s.is_active).length,
      inactive: schools.filter(s => !s.is_active).length,
      totalStudents: schools.reduce((sum, s) => sum + s.student_count, 0),
      averageStudents: schools.length > 0 ? Math.round(schools.reduce((sum, s) => sum + s.student_count, 0) / schools.length) : 0
    }

    return NextResponse.json({ 
      schools,
      stats,
      total: schools.length
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة مدرسة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      name_en,
      name_fr,
      address,
      city,
      phone,
      email,
      website,
      logo_url,
      graduation_date,
      student_count,
      is_active,
      settings
    } = body

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json(
        { error: 'اسم المدرسة مطلوب' },
        { status: 400 }
      )
    }

    if (student_count !== undefined && (isNaN(student_count) || student_count < 0)) {
      return NextResponse.json(
        { error: 'عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً' },
        { status: 400 }
      )
    }

    // التحقق من صحة البريد الإلكتروني
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // جلب المدارس الحالية
    const schools = MockDataManager.getSchools()

    // التحقق من عدم تكرار البريد الإلكتروني
    if (email) {
      const existingSchool = schools.find(school => school.email === email)
      if (existingSchool) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // إنشاء المدرسة الجديدة
    const newSchool: MockSchool = {
      id: MockDataManager.generateId(),
      name,
      name_en: name_en || undefined,
      name_fr: name_fr || undefined,
      address: address || undefined,
      city: city || undefined,
      phone: phone || undefined,
      email: email || undefined,
      website: website || undefined,
      logo_url: logo_url || undefined,
      graduation_date: graduation_date || undefined,
      student_count: student_count || 0,
      is_active: is_active ?? true,
      settings: settings || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // حفظ المدرسة
    schools.push(newSchool)
    MockDataManager.saveSchools(schools)

    return NextResponse.json({ 
      message: 'تم إضافة المدرسة بنجاح',
      school: newSchool 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
