"use strict";(()=>{var e={};e.id=7413,e.ids=[7413],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45660:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>x,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>u,PUT:()=>p});var o=t(96559),n=t(48088),a=t(37719),d=t(32190),i=t(38561);async function u(e,{params:r}){try{let e=i.CN.getOrders().find(e=>e.id===r.id);if(!e)return d.NextResponse.json({error:"الطلب غير موجود"},{status:404});return d.NextResponse.json({order:e})}catch(e){return console.error("Error fetching order:",e),d.NextResponse.json({error:"خطأ في جلب الطلب"},{status:500})}}async function p(e,{params:r}){try{let{status:t,payment_status:s,tracking_number:o,delivery_date:n,notes:a,shipping_address:u}=await e.json(),p=i.CN.getOrders(),c=p.findIndex(e=>e.id===r.id);if(-1===c)return d.NextResponse.json({error:"الطلب غير موجود"},{status:404});let x={...p[c],...t&&{status:t},...s&&{payment_status:s},...o&&{tracking_number:o},...n&&{delivery_date:n},...a&&{notes:a},...u&&{shipping_address:u},updated_at:new Date().toISOString()};return p[c]=x,i.CN.saveOrders(p),d.NextResponse.json({message:"تم تحديث الطلب بنجاح",order:x})}catch(e){return console.error("Error updating order:",e),d.NextResponse.json({error:"خطأ في تحديث الطلب"},{status:500})}}async function c(e,{params:r}){try{let e=i.CN.getOrders(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return d.NextResponse.json({error:"الطلب غير موجود"},{status:404});let s=e[t];if("delivered"===s.status||"shipped"===s.status)return d.NextResponse.json({error:"لا يمكن حذف طلب تم تسليمه أو شحنه"},{status:400});return e.splice(t,1),i.CN.saveOrders(e),d.NextResponse.json({message:"تم حذف الطلب بنجاح"})}catch(e){return console.error("Error deleting order:",e),d.NextResponse.json({error:"خطأ في حذف الطلب"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/orders/[id]/route",pathname:"/api/orders/[id]",filename:"route",bundlePath:"app/api/orders/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:g}=x;function j(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(45660));module.exports=s})();