(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7623],{17313:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>n});var s=a(95155);a(12115);var r=a(60704),i=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",t),...a})}},52869:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var s=a(95155),r=a(12115),i=a(40283),n=a(37784),l=a(30285),c=a(66695),d=a(26126),o=a(62523),x=a(85057),u=a(59409),m=a(80333),h=a(54165),g=a(17313),p=a(48021),f=a(78749),j=a(92657),v=a(13717),b=a(62525),N=a(38164),y=a(33786),_=a(57434),w=a(35169),k=a(84616),C=a(74783),z=a(56671),A=a(75143),E=a(50402),J=a(78266);function T(e){let{item:t,onEdit:a,onDelete:r,onToggleStatus:i,getTargetIcon:n,childItems:c}=e,{attributes:o,listeners:x,setNodeRef:u,transform:m,transition:h,isDragging:g}=(0,E.gl)({id:t.id}),N={transform:J.Ks.Transform.toString(m),transition:h,opacity:g?.5:1};return(0,s.jsxs)("div",{ref:u,style:N,className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{...o,...x,className:"cursor-grab active:cursor-grabbing",children:(0,s.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[n(t.target_type),(0,s.jsx)("span",{className:"font-medium arabic-text",children:t.title_ar}),!t.is_active&&(0,s.jsx)(d.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>i(t),children:t.is_active?(0,s.jsx)(f.A,{className:"h-4 w-4"}):(0,s.jsx)(j.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>a(t),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>r(t.id),children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})]}),c.map(e=>(0,s.jsxs)("div",{className:"mr-8 flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[n(e.target_type),(0,s.jsx)("span",{className:"arabic-text",children:e.title_ar}),!e.is_active&&(0,s.jsx)(d.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>i(e),children:e.is_active?(0,s.jsx)(f.A,{className:"h-4 w-4"}):(0,s.jsx)(j.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>a(e),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>r(e.id),children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})]},e.id))]})}function S(){let{user:e,profile:t}=(0,i.A)(),[a,d]=(0,r.useState)([]),[p,v]=(0,r.useState)(!0),[b,J]=(0,r.useState)(!1),[S,$]=(0,r.useState)(null),[F,L]=(0,r.useState)({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),O=(0,A.FR)((0,A.MS)(A.AN),(0,A.MS)(A.uN,{coordinateGetter:E.JR}));(0,r.useEffect)(()=>{if(!e||!t||"admin"!==t.role){window.location.href="/dashboard";return}},[e,t]);let P=async()=>{try{v(!0);let e=await fetch("/api/menu-items?include_inactive=true"),t=await e.json();e.ok?d(t.menuItems||[]):z.o.error(t.error||"فشل في جلب عناصر القائمة")}catch(e){console.error("Error fetching menu items:",e),z.o.error("خطأ في الاتصال بالخادم")}finally{v(!1)}};(0,r.useEffect)(()=>{P()},[]);let U=()=>{L({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),$(null)},D=e=>{$(e),L({title_ar:e.title_ar,title_en:e.title_en||"",title_fr:e.title_fr||"",slug:e.slug,icon:e.icon||"",parent_id:e.parent_id||"",target_type:e.target_type,target_value:e.target_value,is_active:e.is_active}),J(!0)},Z=async()=>{try{let e=S?"/api/menu-items/".concat(S.id):"/api/menu-items",t=S?"PUT":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify({...F,parent_id:"none"===F.parent_id?null:F.parent_id||null})}),s=await a.json();a.ok?(z.o.success(s.message),J(!1),U(),P()):z.o.error(s.error||"فشل في حفظ عنصر القائمة")}catch(e){console.error("Error saving menu item:",e),z.o.error("خطأ في الاتصال بالخادم")}},I=async e=>{if(confirm("هل أنت متأكد من حذف هذا العنصر؟"))try{let t=await fetch("/api/menu-items/".concat(e),{method:"DELETE"}),a=await t.json();t.ok?(z.o.success(a.message),P()):z.o.error(a.error||"فشل في حذف عنصر القائمة")}catch(e){console.error("Error deleting menu item:",e),z.o.error("خطأ في الاتصال بالخادم")}},M=async e=>{try{let t=await fetch("/api/menu-items/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,is_active:!e.is_active})}),a=await t.json();t.ok?(z.o.success(a.message),P()):z.o.error(a.error||"فشل في تحديث حالة العنصر")}catch(e){console.error("Error toggling item status:",e),z.o.error("خطأ في الاتصال بالخادم")}},V=async e=>{try{let t=a.map(async t=>t.is_active!==e?(await fetch("/api/menu-items/".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,is_active:e})})).json():null);await Promise.all(t),z.o.success(e?"تم تفعيل جميع عناصر القائمة":"تم إلغاء تفعيل جميع عناصر القائمة"),P()}catch(e){console.error("Error toggling all items:",e),z.o.error("خطأ في تحديث عناصر القائمة")}},W=async e=>{let{active:t,over:s}=e;if(!s||t.id===s.id)return;let r=G.findIndex(e=>e.id===t.id),i=G.findIndex(e=>e.id===s.id);if(-1===r||-1===i)return;let n=(0,E.be)(G,r,i);[...a],d([...n,...a.filter(e=>e.parent_id)]);try{let e=n.map((e,t)=>({id:e.id,order_index:t+1})),t=await fetch("/api/menu-items/reorder",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:e})}),s=await t.json();t.ok?(z.o.success(s.message),P()):(z.o.error(s.error||"فشل في تحديث الترتيب"),d(a))}catch(e){console.error("Error reordering items:",e),z.o.error("خطأ في الاتصال بالخادم"),d(a)}},G=a.filter(e=>!e.parent_id),q=e=>a.filter(t=>t.parent_id===e),B=e=>{switch(e){case"internal":default:return(0,s.jsx)(N.A,{className:"h-4 w-4"});case"external":return(0,s.jsx)(y.A,{className:"h-4 w-4"});case"page":return(0,s.jsx)(_.A,{className:"h-4 w-4"})}};return e&&t&&"admin"===t.role?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,s.jsx)(n.V,{}),(0,s.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)("a",{href:"/dashboard/admin",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة القائمة الرئيسية \uD83D\uDDC2️"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تحكم في عناصر القائمة الرئيسية وترتيبها"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(h.lG,{open:b,onOpenChange:J,children:(0,s.jsx)(h.zM,{asChild:!0,children:(0,s.jsxs)(l.$,{onClick:U,children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"إضافة عنصر جديد"]})})}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>V(!0),className:"text-green-600 hover:text-green-700",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تفعيل الكل"]}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>V(!1),className:"text-red-600 hover:text-red-700",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"إلغاء تفعيل الكل"]})]}),(0,s.jsx)(h.lG,{open:b,onOpenChange:J,children:(0,s.jsxs)(h.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(h.c7,{children:[(0,s.jsx)(h.L3,{className:"arabic-text",children:S?"تحرير عنصر القائمة":"إضافة عنصر قائمة جديد"}),(0,s.jsx)(h.rr,{className:"arabic-text",children:"املأ البيانات أدناه لإنشاء أو تحديث عنصر القائمة"})]}),(0,s.jsxs)(g.tU,{defaultValue:"basic",className:"w-full",children:[(0,s.jsxs)(g.j7,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(g.Xi,{value:"basic",className:"arabic-text",children:"البيانات الأساسية"}),(0,s.jsx)(g.Xi,{value:"translations",className:"arabic-text",children:"الترجمات"})]}),(0,s.jsxs)(g.av,{value:"basic",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"title_ar",className:"arabic-text",children:"العنوان بالعربية *"}),(0,s.jsx)(o.p,{id:"title_ar",value:F.title_ar,onChange:e=>L({...F,title_ar:e.target.value}),placeholder:"مثال: الرئيسية",className:"arabic-text"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,s.jsx)(o.p,{id:"slug",value:F.slug,onChange:e=>L({...F,slug:e.target.value}),placeholder:"مثال: home"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"target_type",className:"arabic-text",children:"نوع الهدف *"}),(0,s.jsxs)(u.l6,{value:F.target_type,onValueChange:e=>L({...F,target_type:e}),children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{})}),(0,s.jsxs)(u.gC,{children:[(0,s.jsx)(u.eb,{value:"internal",children:"رابط داخلي"}),(0,s.jsx)(u.eb,{value:"external",children:"رابط خارجي"}),(0,s.jsx)(u.eb,{value:"page",children:"صفحة ديناميكية"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"target_value",className:"arabic-text",children:"قيمة الهدف *"}),(0,s.jsx)(o.p,{id:"target_value",value:F.target_value,onChange:e=>L({...F,target_value:e.target.value}),placeholder:"internal"===F.target_type?"/catalog":"external"===F.target_type?"https://example.com":"معرف الصفحة"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة (Lucide)"}),(0,s.jsx)(o.p,{id:"icon",value:F.icon,onChange:e=>L({...F,icon:e.target.value}),placeholder:"مثال: Home"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"parent_id",className:"arabic-text",children:"العنصر الأب"}),(0,s.jsxs)(u.l6,{value:F.parent_id,onValueChange:e=>L({...F,parent_id:e}),children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{placeholder:"اختر العنصر الأب (اختياري)"})}),(0,s.jsxs)(u.gC,{children:[(0,s.jsx)(u.eb,{value:"none",children:"بدون عنصر أب"}),G.map(e=>(0,s.jsx)(u.eb,{value:e.id,children:e.title_ar},e.id))]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.d,{id:"is_active",checked:F.is_active,onCheckedChange:e=>L({...F,is_active:e})}),(0,s.jsx)(x.J,{htmlFor:"is_active",className:"arabic-text",children:"مفعل"})]})]}),(0,s.jsxs)(g.av,{value:"translations",className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"title_en",className:"arabic-text",children:"العنوان بالإنجليزية"}),(0,s.jsx)(o.p,{id:"title_en",value:F.title_en,onChange:e=>L({...F,title_en:e.target.value}),placeholder:"Example: Home"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(x.J,{htmlFor:"title_fr",className:"arabic-text",children:"العنوان بالفرنسية"}),(0,s.jsx)(o.p,{id:"title_fr",value:F.title_fr,onChange:e=>L({...F,title_fr:e.target.value}),placeholder:"Exemple: Accueil"})]})]})]}),(0,s.jsxs)(h.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:()=>J(!1),children:"إلغاء"}),(0,s.jsx)(l.$,{onClick:Z,children:S?"تحديث":"إضافة"})]})]})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي العناصر"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.length})]}),(0,s.jsx)(C.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المفعلة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:a.filter(e=>e.is_active).length})]}),(0,s.jsx)(j.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المعطلة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:a.filter(e=>!e.is_active).length})]}),(0,s.jsx)(f.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"القوائم الفرعية"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:a.filter(e=>e.parent_id).length})]}),(0,s.jsx)(w.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{className:"arabic-text",children:"عناصر القائمة الحالية"}),(0,s.jsx)(c.BT,{className:"arabic-text",children:"إدارة وترتيب عناصر القائمة الرئيسية"})]}),(0,s.jsx)(c.Wu,{children:p?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 arabic-text",children:"جاري التحميل..."})]}):0===a.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(C.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 arabic-text",children:"لا توجد عناصر قائمة"})]}):(0,s.jsx)(A.Mp,{sensors:O,collisionDetection:A.fp,onDragEnd:W,children:(0,s.jsx)(E.gB,{items:G.map(e=>e.id),strategy:E._G,children:(0,s.jsx)("div",{className:"space-y-2",children:G.map(e=>(0,s.jsx)(T,{item:e,onEdit:D,onDelete:I,onToggleStatus:M,getTargetIcon:B,childItems:q(e.id)},e.id))})})})})]})]})]}):null}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>x,Es:()=>m,L3:()=>h,c7:()=>u,lG:()=>l,rr:()=>g,zM:()=>c});var s=a(95155);a(12115);var r=a(15452),i=a(54416),n=a(59434);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function x(e){let{className:t,children:a,showCloseButton:l=!0,...c}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(o,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...c,children:[a,l&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},57781:(e,t,a)=>{Promise.resolve().then(a.bind(a,52869))},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>x,eb:()=>m,gC:()=>u,l6:()=>d,yv:()=>o});var s=a(95155);a(12115);var r=a(22918),i=a(66474),n=a(5196),l=a(47863),c=a(59434);function d(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function x(e){let{className:t,size:a="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:t,children:a,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(g,{})]})})}function m(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(95155);a(12115);var r=a(59434);function i(e){let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},80333:(e,t,a)=>{"use strict";a.d(t,{d:()=>n});var s=a(95155);a(12115);var r=a(4884),i=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(95155);a(12115);var r=a(40968),i=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,5486,5148,8698,6874,7889,1475,1672,1056,6671,9438,2632,7443,7784,8441,1684,7358],()=>t(57781)),_N_E=e.O()}]);