"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7443],{40283:(e,t,r)=>{r.d(t,{A:()=>s,AuthProvider:()=>l,g:()=>i});var o=r(95155),a=r(12115),i=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});let n=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[r,i]=(0,a.useState)(null),[l,s]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);(0,a.useEffect)(()=>{m(!0)},[]),(0,a.useEffect)(()=>{u&&(async()=>{try{let e=localStorage.getItem("mockUser"),t=localStorage.getItem("mockProfile");if(e&&t){let r=JSON.parse(e),o=JSON.parse(t);if(r&&o&&r.id&&o.id){let e=localStorage.getItem("sessionTimestamp"),t=Date.now(),a=e?t-parseInt(e):0;e&&a<864e5?(i(r),s(o),console.log("User data loaded from localStorage:",{userData:r,profileData:o})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{d(!1)}})()},[u]),(0,a.useEffect)(()=>{if(!r||!l)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},t=["click","keypress","scroll","mousemove"];return t.forEach(t=>{document.addEventListener(t,e,{passive:!0})}),()=>{t.forEach(t=>{document.removeEventListener(t,e)})}},[r,l]);let g=async(e,t,r)=>(console.log("Sign up:",e,r),{data:{user:{id:"1",email:e}},error:null}),f=async(e,t)=>{console.log("Sign in:",e);let r={id:"1",email:e},o="student";e.includes("admin")?o="admin":e.includes("school")?o="school":e.includes("delivery")&&(o="delivery");let a={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:o,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};i(r),s(a);try{localStorage.setItem("mockUser",JSON.stringify(r)),localStorage.setItem("mockProfile",JSON.stringify(a)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:r,mockProfile:a})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{"admin"===o?window.location.href="/dashboard/admin":"school"===o?window.location.href="/dashboard/school":"delivery"===o?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:r},error:null}},h=async()=>{try{return i(null),s(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},v=async e=>{if(!r)return{data:null,error:"No user logged in"};let t={...l,...e};return s(t),{data:t,error:null}};return(0,o.jsx)(n.Provider,{value:{user:r,profile:l,loading:c,signUp:g,signIn:f,signOut:h,updateProfile:v,hasRole:e=>{if(!l)return!1;let t={admin:4,school:3,delivery:2,student:1};return t[l.role]>=t[e]}},children:t})}function s(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},66424:(e,t,r)=>{r.d(t,{F:()=>n});var o=r(95155);r(12115);var a=r(47655),i=r(59434);function n(e){let{className:t,children:r,...n}=e;return(0,o.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,i.cn)("relative",t),...n,children:[(0,o.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,o.jsx)(l,{}),(0,o.jsx)(a.OK,{})]})}function l(e){let{className:t,orientation:r="vertical",...n}=e;return(0,o.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:(0,i.cn)("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...n,children:(0,o.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},77470:(e,t,r)=>{r.d(t,{By:()=>c,E$:()=>u,NotificationProvider:()=>l,bQ:()=>d,lO:()=>s});var o=r(95155),a=r(12115),i=r(40283);let n=(0,a.createContext)(void 0);function l(e){let{children:t}=e,{user:r}=(0,i.A)(),[l,s]=(0,a.useState)([]);(0,a.useEffect)(()=>{if(r){c();let e=setInterval(()=>{Math.random()>.8&&d()},3e4);return()=>clearInterval(e)}},[r]);let c=()=>{s([{id:"1",type:"order_confirmed",title:"تم تأكيد طلبك",message:"تم تأكيد طلبك #GT-240120-001 بنجاح وسيتم تحضيره قريباً",priority:"high",isRead:!1,createdAt:new Date(Date.now()-72e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الطلب",userId:(null==r?void 0:r.id)||"",metadata:{orderId:"GT-240120-001"}},{id:"2",type:"promotion",title:"عرض خاص - خصم 20%",message:"احصل على خصم 20% على جميع أزياء التخرج لفترة محدودة",priority:"medium",isRead:!1,createdAt:new Date(Date.now()-144e5).toISOString(),expiresAt:new Date(Date.now()+6048e5).toISOString(),actionUrl:"/catalog",actionText:"تسوق الآن",userId:(null==r?void 0:r.id)||"",metadata:{promoCode:"GRAD20"}},{id:"3",type:"order_shipped",title:"تم شحن طلبك",message:"طلبك #GT-240115-002 في طريقه إليك. رقم التتبع: TRK-123456",priority:"high",isRead:!0,createdAt:new Date(Date.now()-864e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الشحنة",userId:(null==r?void 0:r.id)||"",metadata:{orderId:"GT-240115-002",trackingNumber:"TRK-123456"}},{id:"4",type:"review_request",title:"قيم تجربتك معنا",message:"نود معرفة رأيك في المنتجات التي استلمتها مؤخراً",priority:"low",isRead:!1,createdAt:new Date(Date.now()-2592e5).toISOString(),actionUrl:"/reviews",actionText:"اكتب تقييم",userId:(null==r?void 0:r.id)||""}])},d=()=>{let e=["system","promotion","reminder"],t=e[Math.floor(Math.random()*e.length)],r={system:{title:"تحديث النظام",message:"تم تحديث النظام بميزات جديدة"},promotion:{title:"عرض محدود",message:"خصم خاص على المنتجات المختارة"},reminder:{title:"تذكير",message:"لا تنس إكمال طلبك في سلة التسوق"}};u({type:t,title:r[t].title,message:r[t].message,priority:"medium"})},u=e=>{let t={...e,id:Date.now().toString(),createdAt:new Date().toISOString(),userId:(null==r?void 0:r.id)||"",isRead:!1};s(e=>[t,...e]),"granted"===Notification.permission&&new Notification(t.title,{body:t.message,icon:"/favicon.ico",tag:t.id})},m=()=>l.filter(e=>!e.isRead),g=m().length;return(0,a.useEffect)(()=>{"Notification"in window&&"default"===Notification.permission&&Notification.requestPermission()},[]),(0,o.jsx)(n.Provider,{value:{notifications:l,unreadCount:g,addNotification:u,markAsRead:e=>{s(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{s(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{s(t=>t.filter(t=>t.id!==e))},clearAll:()=>{s([])},getNotificationsByType:e=>l.filter(t=>t.type===e),getUnreadNotifications:m},children:t})}function s(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}function c(e){switch(e){case"order_confirmed":case"order_shipped":case"order_delivered":return"\uD83D\uDCE6";case"payment_received":return"\uD83D\uDCB3";case"payment_failed":return"❌";case"promotion":return"\uD83C\uDF89";case"reminder":return"⏰";case"system":return"⚙️";case"message":return"\uD83D\uDCAC";case"review_request":return"⭐";default:return"\uD83D\uDD14"}}function d(e){switch(e){case"urgent":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}}function u(e){let t=new Date,r=new Date(e),o=Math.floor((t.getTime()-r.getTime())/6e4);if(o<1)return"الآن";if(o<60)return"منذ ".concat(o," دقيقة");if(o<1440){let e=Math.floor(o/60);return"منذ ".concat(e," ساعة")}{let e=Math.floor(o/1440);return"منذ ".concat(e," يوم")}}}}]);