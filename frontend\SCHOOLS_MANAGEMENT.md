# نظام إدارة المدارس - Graduation Toqs

## نظرة عامة

تم إنشاء نظام شامل لإدارة المدارس في منصة Graduation Toqs يتيح للمديرين إدارة المدارس المسجلة والشراكات التعليمية بسهولة.

## المميزات الرئيسية

### 1. صفحة إدارة المدارس (`/dashboard/admin/schools`)
- عرض جميع المدارس في جدول منظم
- إحصائيات سريعة (إجمالي المدارس، النشطة، غير النشطة، إجمالي الطلاب، متوسط الطلاب)
- فلترة المدارس حسب:
  - المدينة
  - حالة النشاط (نشطة/غير نشطة)
  - الترتيب (تاريخ الإنشاء، الاسم، عدد الطلاب، المدينة، تاريخ التخرج)
- البحث في أسماء المدارس والبريد الإلكتروني والمدينة
- إجراءات سريعة (تحديث، حذف، تغيير حالة النشاط)

### 2. نموذج إضافة/تحديث المدرسة
نموذج شامل مقسم إلى 3 تبويبات:

#### أ. المعلومات الأساسية
- اسم المدرسة (عربي، إنجليزي، فرنسي)
- العنوان والمدينة
- عدد الطلاب
- تاريخ التخرج
- حالة النشاط

#### ب. معلومات التواصل
- رقم الهاتف
- البريد الإلكتروني
- الموقع الإلكتروني
- رابط الشعار

#### ج. إعدادات التخرج
- مكان حفل التخرج
- نوع الزي المطلوب (رسمي، أكاديمي، عمل، عادي)
- السماح بالتصوير

### 3. إحصائيات تفاعلية
- إجمالي المدارس المسجلة
- عدد المدارس النشطة وغير النشطة
- إجمالي عدد الطلاب في جميع المدارس
- متوسط عدد الطلاب لكل مدرسة

### 4. نظام البحث والفلترة المتقدم
- البحث النصي في جميع حقول المدرسة
- فلترة حسب المدينة
- فلترة حسب حالة النشاط
- ترتيب متعدد المعايير
- ترتيب تصاعدي وتنازلي

## كيفية الاستخدام

### 1. الوصول لصفحة إدارة المدارس
```
/dashboard/admin/schools
```

### 2. إضافة مدرسة جديدة
1. اضغط على زر "إضافة مدرسة جديدة"
2. املأ المعلومات الأساسية (الاسم مطلوب)
3. أضف معلومات التواصل
4. اضبط إعدادات التخرج
5. اضغط "إضافة المدرسة"

### 3. تحديث مدرسة موجودة
1. اضغط على زر التحديث بجانب المدرسة
2. عدل البيانات المطلوبة
3. احفظ التغييرات

### 4. حذف مدرسة
1. اضغط على زر الحذف
2. أكد الحذف

### 5. تغيير حالة النشاط
1. اضغط على شارة الحالة بجانب المدرسة
2. ستتغير الحالة تلقائياً

## الملفات المهمة

```
frontend/src/
├── app/dashboard/admin/schools/page.tsx      # صفحة إدارة المدارس
├── components/admin/SchoolForm.tsx           # نموذج المدرسة
├── app/api/
│   ├── schools/route.ts                      # API المدارس
│   └── schools/[id]/route.ts                # API مدرسة واحدة
├── lib/mockData.ts                           # البيانات الوهمية
└── supabase/schema.sql                       # هيكل قاعدة البيانات
```

## قاعدة البيانات

### جدول المدارس (schools)
```sql
CREATE TABLE schools (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    name_en TEXT,
    name_fr TEXT,
    address TEXT,
    city TEXT,
    phone TEXT,
    email TEXT,
    website TEXT,
    logo_url TEXT,
    graduation_date DATE,
    student_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### البيانات الوهمية
يتضمن النظام 5 مدارس وهمية للتطوير:
- جامعة الإمارات العربية المتحدة
- الجامعة الأمريكية في الشارقة
- جامعة زايد
- كلية الإمارات للتكنولوجيا
- معهد أبوظبي للتعليم التقني

## API Endpoints

### GET /api/schools
جلب جميع المدارس مع إمكانية الفلترة والبحث

**المعاملات:**
- `include_inactive`: تضمين المدارس غير النشطة
- `city`: فلترة حسب المدينة
- `search`: البحث النصي
- `sort_by`: معيار الترتيب
- `sort_order`: اتجاه الترتيب (asc/desc)

### POST /api/schools
إضافة مدرسة جديدة

### GET /api/schools/[id]
جلب مدرسة واحدة

### PUT /api/schools/[id]
تحديث مدرسة

### DELETE /api/schools/[id]
حذف مدرسة

## التحقق من صحة البيانات

### الحقول المطلوبة
- اسم المدرسة (name)

### التحقق من صحة البيانات
- البريد الإلكتروني: تنسيق صحيح
- الموقع الإلكتروني: يبدأ بـ http:// أو https://
- عدد الطلاب: رقم صحيح موجب
- عدم تكرار البريد الإلكتروني

## الأمان والصلاحيات

- جميع عمليات إدارة المدارس محصورة بالمديرين فقط
- التحقق من صلاحيات المستخدم في كل API call
- التحقق من صحة البيانات قبل الحفظ
- حماية من البيانات المكررة

## التطوير المستقبلي

### لتحويل النظام لاستخدام قاعدة بيانات حقيقية:
1. إعداد Supabase بشكل صحيح
2. إنشاء جدول schools باستخدام `supabase/schema.sql`
3. استبدال `MockDataManager` بـ Supabase client
4. تحديث API routes لاستخدام Supabase

### ميزات مقترحة:
- ربط المدارس بالمستخدمين
- إحصائيات مفصلة لكل مدرسة
- تقارير الطلبات حسب المدرسة
- نظام إشعارات للمدارس
- تصدير بيانات المدارس
- رفع شعارات المدارس
- إدارة الأحداث والفعاليات لكل مدرسة

## الاختبار

للاختبار:
1. انتقل إلى `/dashboard/admin/schools`
2. جرب إضافة مدرسة جديدة
3. اختبر البحث والفلترة
4. جرب تحديث وحذف المدارس
5. اختبر تغيير حالة النشاط

## الدعم الفني

في حالة وجود مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من صحة البيانات المدخلة
3. تحقق من صلاحيات المستخدم
4. راجع ملفات API للتأكد من الاستجابات
