"use strict";(()=>{var e={};e.id=761,e.ids=[761],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41380:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var t={};s.r(t),s.d(t,{DELETE:()=>c,GET:()=>u,PUT:()=>p});var o=s(96559),n=s(48088),i=s(37719),a=s(32190),d=s(38561);async function u(e,{params:r}){try{let e=d.CN.getSchools().find(e=>e.id===r.id);if(!e)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});return a.NextResponse.json({school:e})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:r}){try{let{name:s,name_en:t,name_fr:o,address:n,city:i,phone:u,email:p,website:c,logo_url:l,graduation_date:x,student_count:v,is_active:f,settings:h}=await e.json(),j=d.CN.getSchools(),N=j.findIndex(e=>e.id===r.id);if(-1===N)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});if(!s)return a.NextResponse.json({error:"اسم المدرسة مطلوب"},{status:400});if(void 0!==v&&(isNaN(v)||v<0))return a.NextResponse.json({error:"عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً"},{status:400});if(p&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p))return a.NextResponse.json({error:"البريد الإلكتروني غير صحيح"},{status:400});if(p&&j.find(e=>e.email===p&&e.id!==r.id))return a.NextResponse.json({error:"البريد الإلكتروني موجود بالفعل"},{status:400});let g={...j[N],name:s,name_en:t||void 0,name_fr:o||void 0,address:n||void 0,city:i||void 0,phone:u||void 0,email:p||void 0,website:c||void 0,logo_url:l||void 0,graduation_date:x||void 0,student_count:v??j[N].student_count,is_active:f??j[N].is_active,settings:h||j[N].settings,updated_at:new Date().toISOString()};return j[N]=g,d.CN.saveSchools(j),a.NextResponse.json({message:"تم تحديث المدرسة بنجاح",school:g})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e,{params:r}){try{let e=d.CN.getSchools(),s=e.findIndex(e=>e.id===r.id);if(-1===s)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});let t=e.splice(s,1)[0];return d.CN.saveSchools(e),a.NextResponse.json({message:"تم حذف المدرسة بنجاح",school:t})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/schools/[id]/route",pathname:"/api/schools/[id]",filename:"route",bundlePath:"app/api/schools/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:f}=l;function h(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,8554],()=>s(41380));module.exports=t})();