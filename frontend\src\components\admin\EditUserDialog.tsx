"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  User, 
  Mail, 
  Phone, 
  Shield, 
  GraduationCap, 
  School, 
  Truck,
  Eye,
  EyeOff
} from 'lucide-react'

interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'admin' | 'student' | 'school' | 'delivery'
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  last_login?: string
  avatar_url?: string
  school_name?: string
  delivery_company?: string
  student_id?: string
  verified: boolean
}

interface EditUserDialogProps {
  user: User
  onSubmit: (userData: any) => void
  onCancel: () => void
}

interface UserFormData {
  email: string
  full_name: string
  phone: string
  role: 'admin' | 'student' | 'school' | 'delivery'
  status: 'active' | 'inactive' | 'suspended'
  verified: boolean
  // حقول إضافية حسب الدور
  student_id?: string
  school_name?: string
  school_address?: string
  delivery_company?: string
  delivery_license?: string
  notes?: string
  // كلمة مرور جديدة (اختيارية)
  new_password?: string
  confirm_new_password?: string
}

export function EditUserDialog({ user, onSubmit, onCancel }: EditUserDialogProps) {
  const [formData, setFormData] = useState<UserFormData>({
    email: user.email,
    full_name: user.full_name,
    phone: user.phone || '',
    role: user.role,
    status: user.status,
    verified: user.verified,
    student_id: user.student_id || '',
    school_name: user.school_name || '',
    delivery_company: user.delivery_company || '',
    new_password: '',
    confirm_new_password: ''
  })
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    // التحقق من الاسم
    if (!formData.full_name) {
      newErrors.full_name = 'الاسم الكامل مطلوب'
    } else if (formData.full_name.length < 2) {
      newErrors.full_name = 'الاسم يجب أن يكون أكثر من حرفين'
    }

    // التحقق من كلمة المرور الجديدة (إذا تم إدخالها)
    if (formData.new_password) {
      if (formData.new_password.length < 6) {
        newErrors.new_password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
      }
      
      if (formData.new_password !== formData.confirm_new_password) {
        newErrors.confirm_new_password = 'كلمة المرور غير متطابقة'
      }
    }

    // التحقق من الهاتف
    if (formData.phone && !/^\+?[1-9]\d{1,14}$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح'
    }

    // التحقق من الحقول الإضافية حسب الدور
    if (formData.role === 'student' && formData.student_id && formData.student_id.length < 3) {
      newErrors.student_id = 'رقم الطالب يجب أن يكون 3 أحرف على الأقل'
    }

    if (formData.role === 'school' && !formData.school_name) {
      newErrors.school_name = 'اسم المدرسة مطلوب'
    }

    if (formData.role === 'delivery' && !formData.delivery_company) {
      newErrors.delivery_company = 'اسم شركة التوصيل مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // معالجة إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      // إزالة كلمات المرور إذا لم يتم تغييرها
      const { confirm_new_password, ...userData } = formData
      if (!userData.new_password) {
        delete userData.new_password
      }
      
      await onSubmit(userData)
    } catch (error) {
      console.error('Error updating user:', error)
    } finally {
      setLoading(false)
    }
  }

  // تحديث البيانات
  const updateFormData = (field: keyof UserFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="arabic-text">تعديل بيانات المستخدم</DialogTitle>
          <DialogDescription className="arabic-text">
            تعديل بيانات المستخدم: {user.full_name}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* المعلومات الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="full_name" className="arabic-text">الاسم الكامل *</Label>
                  <Input
                    id="full_name"
                    value={formData.full_name}
                    onChange={(e) => updateFormData('full_name', e.target.value)}
                    placeholder="أدخل الاسم الكامل"
                    className={errors.full_name ? 'border-red-500' : ''}
                  />
                  {errors.full_name && (
                    <p className="text-sm text-red-500 arabic-text">{errors.full_name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="arabic-text">البريد الإلكتروني *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 arabic-text">{errors.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="arabic-text">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    placeholder="+971501234567"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500 arabic-text">{errors.phone}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="arabic-text">الدور *</Label>
                  <Select value={formData.role} onValueChange={(value: any) => updateFormData('role', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="student">
                        <div className="flex items-center gap-2">
                          <GraduationCap className="h-4 w-4" />
                          طالب
                        </div>
                      </SelectItem>
                      <SelectItem value="school">
                        <div className="flex items-center gap-2">
                          <School className="h-4 w-4" />
                          مدرسة
                        </div>
                      </SelectItem>
                      <SelectItem value="delivery">
                        <div className="flex items-center gap-2">
                          <Truck className="h-4 w-4" />
                          شركة توصيل
                        </div>
                      </SelectItem>
                      <SelectItem value="admin">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          مدير
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* تغيير كلمة المرور */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">تغيير كلمة المرور</CardTitle>
              <CardDescription className="arabic-text">
                اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="new_password" className="arabic-text">كلمة المرور الجديدة</Label>
                  <div className="relative">
                    <Input
                      id="new_password"
                      type={showNewPassword ? 'text' : 'password'}
                      value={formData.new_password || ''}
                      onChange={(e) => updateFormData('new_password', e.target.value)}
                      placeholder="كلمة المرور الجديدة"
                      className={errors.new_password ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.new_password && (
                    <p className="text-sm text-red-500 arabic-text">{errors.new_password}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirm_new_password" className="arabic-text">تأكيد كلمة المرور الجديدة</Label>
                  <div className="relative">
                    <Input
                      id="confirm_new_password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirm_new_password || ''}
                      onChange={(e) => updateFormData('confirm_new_password', e.target.value)}
                      placeholder="تأكيد كلمة المرور الجديدة"
                      className={errors.confirm_new_password ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.confirm_new_password && (
                    <p className="text-sm text-red-500 arabic-text">{errors.confirm_new_password}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الحقول الإضافية حسب الدور */}
          {formData.role === 'student' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات الطالب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="student_id" className="arabic-text">رقم الطالب</Label>
                  <Input
                    id="student_id"
                    value={formData.student_id || ''}
                    onChange={(e) => updateFormData('student_id', e.target.value)}
                    placeholder="STU2024001"
                    className={errors.student_id ? 'border-red-500' : ''}
                  />
                  {errors.student_id && (
                    <p className="text-sm text-red-500 arabic-text">{errors.student_id}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {formData.role === 'school' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات المدرسة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="school_name" className="arabic-text">اسم المدرسة *</Label>
                  <Input
                    id="school_name"
                    value={formData.school_name || ''}
                    onChange={(e) => updateFormData('school_name', e.target.value)}
                    placeholder="جامعة الإمارات العربية المتحدة"
                    className={errors.school_name ? 'border-red-500' : ''}
                  />
                  {errors.school_name && (
                    <p className="text-sm text-red-500 arabic-text">{errors.school_name}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {formData.role === 'delivery' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات شركة التوصيل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="delivery_company" className="arabic-text">اسم الشركة *</Label>
                  <Input
                    id="delivery_company"
                    value={formData.delivery_company || ''}
                    onChange={(e) => updateFormData('delivery_company', e.target.value)}
                    placeholder="شركة التوصيل السريع"
                    className={errors.delivery_company ? 'border-red-500' : ''}
                  />
                  {errors.delivery_company && (
                    <p className="text-sm text-red-500 arabic-text">{errors.delivery_company}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* الإعدادات */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">إعدادات الحساب</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="arabic-text">حالة الحساب</Label>
                <Select value={formData.status} onValueChange={(value: any) => updateFormData('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                    <SelectItem value="suspended">معلق</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="arabic-text">حساب محقق</Label>
                  <p className="text-sm text-gray-500 arabic-text">
                    تحديد ما إذا كان الحساب محققاً أم لا
                  </p>
                </div>
                <Switch
                  checked={formData.verified}
                  onCheckedChange={(checked) => updateFormData('verified', checked)}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              إلغاء
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'جاري التحديث...' : 'حفظ التغييرات'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
