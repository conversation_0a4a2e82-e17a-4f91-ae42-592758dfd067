"use client"

import { useAuth, UserRole } from '@/contexts/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'
import Link from 'next/link'

export function UserMenu() {
  const { user, profile, signOut } = useAuth()
  const { t } = useTranslation()

  if (!user || !profile) {
    return (
      <Button variant="outline" asChild>
        <a href="/auth">{t('auth.login')}</a>
      </Button>
    )
  }

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Shield className="h-4 w-4" />
      case UserRole.SCHOOL:
        return <School className="h-4 w-4" />
      case UserRole.DELIVERY:
        return <Truck className="h-4 w-4" />
      case UserRole.STUDENT:
        return <GraduationCap className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return 'مدير'
      case UserRole.SCHOOL:
        return 'مدرسة'
      case UserRole.DELIVERY:
        return 'شريك توصيل'
      case UserRole.STUDENT:
        return 'طالب'
      default:
        return 'مستخدم'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleSignOut = async () => {
    await signOut()
    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج
    window.location.href = '/'
  }

  const getDashboardUrl = () => {
    if (!profile) return '/dashboard/student'

    // توجيه كل دور إلى لوحة التحكم المخصصة له
    switch (profile.role) {
      case UserRole.ADMIN:
        return '/dashboard/admin'
      case UserRole.SCHOOL:
        return '/dashboard/school'
      case UserRole.DELIVERY:
        return '/dashboard/delivery'
      case UserRole.STUDENT:
        return '/dashboard/student'
      default:
        return '/dashboard/student'
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <User className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">User menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {profile.full_name}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getRoleIcon(profile.role)}
              <span>{getRoleLabel(profile.role)}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem asChild>
          <Link href="/profile" className="cursor-pointer flex items-center">
            <User className="mr-2 h-4 w-4" />
            <span>{t('navigation.profile')}</span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={getDashboardUrl()} className="cursor-pointer flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            <span>{t('navigation.dashboard')}</span>
          </Link>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          className="cursor-pointer text-red-600 focus:text-red-600"
          onClick={handleSignOut}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{t('auth.logout')}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
