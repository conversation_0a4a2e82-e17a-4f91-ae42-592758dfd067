(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1391],{17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>n});var s=t(95155);t(12115);var r=t(60704),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",a),...t})}},20273:(e,a,t)=>{Promise.resolve().then(t.bind(t,32546))},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>c});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:a,variant:t,asChild:i=!1,...c}=e,d=i?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),a),...c})}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c,r:()=>l});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:a,variant:t,size:i,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:a})),...d})}},32546:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>Z});var s=t(95155),r=t(12115),i=t(30285),n=t(62523),l=t(66695),c=t(26126),d=t(54165),o=t(59409),x=t(35169),u=t(84616),h=t(53896),m=t(17580),g=t(66932),p=t(47924),v=t(85213),b=t(95488),j=t(28883),f=t(4516),N=t(69074),y=t(13717),w=t(62525),_=t(85057),k=t(88539),C=t(80333),z=t(17313),A=t(19420),S=t(381),E=t(54416),F=t(4229);function J(e){let{school:a,onSubmit:t,onCancel:d,isLoading:o=!1}=e,[x,u]=(0,r.useState)({name:"",name_en:"",name_fr:"",address:"",city:"",phone:"",email:"",website:"",logo_url:"",graduation_date:"",student_count:0,is_active:!0,settings:{graduation_ceremony_location:"",dress_code:"formal",photography_allowed:!0}}),[h,g]=(0,r.useState)({});(0,r.useEffect)(()=>{if(a){var e,t,s,r;u({name:a.name||"",name_en:a.name_en||"",name_fr:a.name_fr||"",address:a.address||"",city:a.city||"",phone:a.phone||"",email:a.email||"",website:a.website||"",logo_url:a.logo_url||"",graduation_date:a.graduation_date||"",student_count:a.student_count||0,is_active:a.is_active,settings:{graduation_ceremony_location:(null==(e=a.settings)?void 0:e.graduation_ceremony_location)||"",dress_code:(null==(t=a.settings)?void 0:t.dress_code)||"formal",photography_allowed:null==(r=null==(s=a.settings)?void 0:s.photography_allowed)||r}})}},[a]);let p=(e,a)=>{u(t=>({...t,[e]:a})),h[e]&&g(a=>({...a,[e]:""}))},v=(e,a)=>{u(t=>({...t,settings:{...t.settings,[e]:a}}))},b=()=>{let e={};return x.name.trim()||(e.name="اسم المدرسة مطلوب"),x.student_count<0&&(e.student_count="عدد الطلاب يجب أن يكون رقماً موجباً"),x.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(x.email)&&(e.email="البريد الإلكتروني غير صحيح"),x.website&&!/^https?:\/\/.+/.test(x.website)&&(e.website="الموقع الإلكتروني يجب أن يبدأ بـ http:// أو https://"),g(e),0===Object.keys(e).length},j=async e=>{if(e.preventDefault(),b())try{await t(x)}catch(e){console.error("Error submitting form:",e)}};return(0,s.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,s.jsxs)(z.tU,{defaultValue:"basic",className:"w-full",children:[(0,s.jsxs)(z.j7,{className:"grid w-full grid-cols-3",children:[(0,s.jsx)(z.Xi,{value:"basic",className:"arabic-text",children:"المعلومات الأساسية"}),(0,s.jsx)(z.Xi,{value:"contact",className:"arabic-text",children:"معلومات التواصل"}),(0,s.jsx)(z.Xi,{value:"settings",className:"arabic-text",children:"الإعدادات"})]}),(0,s.jsx)(z.av,{value:"basic",className:"space-y-4",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-5 w-5"}),"المعلومات الأساسية"]})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"name",className:"arabic-text",children:"اسم المدرسة *"}),(0,s.jsx)(n.p,{id:"name",value:x.name,onChange:e=>p("name",e.target.value),placeholder:"جامعة الإمارات العربية المتحدة",className:h.name?"border-red-500":""}),h.name&&(0,s.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:h.name})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"name_en",className:"arabic-text",children:"الاسم بالإنجليزية"}),(0,s.jsx)(n.p,{id:"name_en",value:x.name_en,onChange:e=>p("name_en",e.target.value),placeholder:"United Arab Emirates University"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"name_fr",className:"arabic-text",children:"الاسم بالفرنسية"}),(0,s.jsx)(n.p,{id:"name_fr",value:x.name_fr,onChange:e=>p("name_fr",e.target.value),placeholder:"Universit\xe9 des \xc9mirats Arabes Unis"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"city",className:"arabic-text",children:"المدينة"}),(0,s.jsx)(n.p,{id:"city",value:x.city,onChange:e=>p("city",e.target.value),placeholder:"دبي"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"address",className:"arabic-text",children:"العنوان"}),(0,s.jsx)(k.T,{id:"address",value:x.address,onChange:e=>p("address",e.target.value),placeholder:"شارع الجامعة، المنطقة الأكاديمية",rows:3})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"student_count",className:"arabic-text",children:"عدد الطلاب"}),(0,s.jsx)(n.p,{id:"student_count",type:"number",min:"0",value:x.student_count,onChange:e=>p("student_count",parseInt(e.target.value)||0),placeholder:"1000",className:h.student_count?"border-red-500":""}),h.student_count&&(0,s.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:h.student_count})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"graduation_date",className:"arabic-text",children:"تاريخ التخرج"}),(0,s.jsx)(n.p,{id:"graduation_date",type:"date",value:x.graduation_date,onChange:e=>p("graduation_date",e.target.value)})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(C.d,{id:"is_active",checked:x.is_active,onCheckedChange:e=>p("is_active",e)}),(0,s.jsxs)(_.J,{htmlFor:"is_active",className:"arabic-text",children:["مدرسة نشطة",x.is_active?(0,s.jsx)(c.E,{variant:"default",className:"mr-2",children:"نشطة"}):(0,s.jsx)(c.E,{variant:"secondary",className:"mr-2",children:"غير نشطة"})]})]})]})]})}),(0,s.jsx)(z.av,{value:"contact",className:"space-y-4",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(A.A,{className:"h-5 w-5"}),"معلومات التواصل"]})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,s.jsx)(n.p,{id:"phone",value:x.phone,onChange:e=>p("phone",e.target.value),placeholder:"+971-4-123-4567"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني"}),(0,s.jsx)(n.p,{id:"email",type:"email",value:x.email,onChange:e=>p("email",e.target.value),placeholder:"<EMAIL>",className:h.email?"border-red-500":""}),h.email&&(0,s.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:h.email})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"website",className:"arabic-text",children:"الموقع الإلكتروني"}),(0,s.jsx)(n.p,{id:"website",value:x.website,onChange:e=>p("website",e.target.value),placeholder:"https://www.university.edu",className:h.website?"border-red-500":""}),h.website&&(0,s.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:h.website})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"logo_url",className:"arabic-text",children:"رابط الشعار"}),(0,s.jsx)(n.p,{id:"logo_url",value:x.logo_url,onChange:e=>p("logo_url",e.target.value),placeholder:"https://example.com/logo.png"})]})]})]})]})}),(0,s.jsx)(z.av,{value:"settings",className:"space-y-4",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(S.A,{className:"h-5 w-5"}),"إعدادات التخرج"]})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"ceremony_location",className:"arabic-text",children:"مكان حفل التخرج"}),(0,s.jsx)(n.p,{id:"ceremony_location",value:x.settings.graduation_ceremony_location,onChange:e=>v("graduation_ceremony_location",e.target.value),placeholder:"قاعة الاحتفالات الكبرى"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(_.J,{htmlFor:"dress_code",className:"arabic-text",children:"نوع الزي المطلوب"}),(0,s.jsxs)("select",{id:"dress_code",value:x.settings.dress_code,onChange:e=>v("dress_code",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"formal",children:"رسمي"}),(0,s.jsx)("option",{value:"academic",children:"أكاديمي"}),(0,s.jsx)("option",{value:"business",children:"عمل"}),(0,s.jsx)("option",{value:"casual",children:"عادي"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(C.d,{id:"photography_allowed",checked:x.settings.photography_allowed,onCheckedChange:e=>v("photography_allowed",e)}),(0,s.jsx)(_.J,{htmlFor:"photography_allowed",className:"arabic-text",children:"السماح بالتصوير"})]})]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:d,disabled:o,children:[(0,s.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"إلغاء"]}),(0,s.jsxs)(i.$,{type:"submit",disabled:o,children:[(0,s.jsx)(F.A,{className:"h-4 w-4 mr-2"}),o?"جاري الحفظ...":a?"تحديث المدرسة":"إضافة المدرسة"]})]})]})}function Z(){let[e,a]=(0,r.useState)([]),[t,_]=(0,r.useState)({total:0,active:0,inactive:0,totalStudents:0,averageStudents:0}),[k,C]=(0,r.useState)(!0),[z,A]=(0,r.useState)(""),[S,E]=(0,r.useState)("all"),[F,Z]=(0,r.useState)("all"),[L,U]=(0,r.useState)("created_at"),[W,B]=(0,r.useState)("desc"),[T,$]=(0,r.useState)(!1),[D,P]=(0,r.useState)(null),[V,O]=(0,r.useState)(!1),R=async()=>{try{C(!0);let e=new URLSearchParams({include_inactive:"true",search:z,city:"all"===S?"":S,sort_by:L,sort_order:W}),t=await fetch("/api/schools?".concat(e)),s=await t.json();t.ok?(a(s.schools),_(s.stats)):console.error("Error fetching schools:",s.error)}catch(e){console.error("Error fetching schools:",e)}finally{C(!1)}};(0,r.useEffect)(()=>{R()},[z,S,F,L,W]);let X=e.filter(e=>"active"===F?e.is_active:"inactive"!==F||!e.is_active),q=async e=>{try{O(!0);let a=D?"/api/schools/".concat(D.id):"/api/schools",t=D?"PUT":"POST",s=await fetch(a,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await s.json();s.ok?(await R(),$(!1),P(null)):alert(r.error||"حدث خطأ أثناء حفظ المدرسة")}catch(e){console.error("Error submitting school:",e),alert("حدث خطأ أثناء حفظ المدرسة")}finally{O(!1)}},M=async e=>{if(confirm('هل أنت متأكد من حذف مدرسة "'.concat(e.name,'"؟')))try{let a=await fetch("/api/schools/".concat(e.id),{method:"DELETE"}),t=await a.json();a.ok?await R():alert(t.error||"حدث خطأ أثناء حذف المدرسة")}catch(e){console.error("Error deleting school:",e),alert("حدث خطأ أثناء حذف المدرسة")}},G=async e=>{try{let a=await fetch("/api/schools/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,is_active:!e.is_active})}),t=await a.json();a.ok?await R():alert(t.error||"حدث خطأ أثناء تحديث حالة المدرسة")}catch(e){console.error("Error updating school status:",e),alert("حدث خطأ أثناء تحديث حالة المدرسة")}},I=e=>{P(e),$(!0)},Q=Array.from(new Set(e.map(e=>e.city).filter(Boolean)));return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)("a",{href:"/dashboard/admin",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المدارس \uD83C\uDFEB"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة المدارس المسجلة والشراكات التعليمية"})]}),(0,s.jsxs)(d.lG,{open:T,onOpenChange:$,children:[(0,s.jsx)(d.zM,{asChild:!0,children:(0,s.jsxs)(i.$,{onClick:()=>{P(null)},children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"إضافة مدرسة جديدة"]})}),(0,s.jsxs)(d.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)(d.c7,{children:(0,s.jsx)(d.L3,{className:"arabic-text",children:D?"تحديث المدرسة":"إضافة مدرسة جديدة"})}),(0,s.jsx)(J,{school:D||void 0,onSubmit:q,onCancel:()=>$(!1),isLoading:V})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8",children:[(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المدارس"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:t.total})]}),(0,s.jsx)(h.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المدارس النشطة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:t.active})]}),(0,s.jsx)("div",{className:"h-8 w-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-4 w-4 bg-green-500 rounded-full"})})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المدارس غير النشطة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:t.inactive})]}),(0,s.jsx)("div",{className:"h-8 w-8 bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-4 w-4 bg-red-500 rounded-full"})})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلاب"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:t.totalStudents.toLocaleString()})]}),(0,s.jsx)(m.A,{className:"h-8 w-8 text-purple-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"متوسط الطلاب"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:t.averageStudents.toLocaleString()})]}),(0,s.jsx)("div",{className:"h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-orange-500"})})]})})})]}),(0,s.jsxs)(l.Zp,{className:"mb-6",children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5"}),"البحث والفلترة"]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(n.p,{placeholder:"البحث في المدارس...",value:z,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,s.jsxs)(o.l6,{value:S,onValueChange:E,children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"جميع المدن"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"all",children:"جميع المدن"}),Q.map(e=>(0,s.jsx)(o.eb,{value:e,children:e},e))]})]}),(0,s.jsxs)(o.l6,{value:F,onValueChange:Z,children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"جميع الحالات"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"all",children:"جميع الحالات"}),(0,s.jsx)(o.eb,{value:"active",children:"نشطة"}),(0,s.jsx)(o.eb,{value:"inactive",children:"غير نشطة"})]})]}),(0,s.jsxs)(o.l6,{value:L,onValueChange:U,children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"ترتيب حسب"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"created_at",children:"تاريخ الإنشاء"}),(0,s.jsx)(o.eb,{value:"name",children:"الاسم"}),(0,s.jsx)(o.eb,{value:"student_count",children:"عدد الطلاب"}),(0,s.jsx)(o.eb,{value:"city",children:"المدينة"}),(0,s.jsx)(o.eb,{value:"graduation_date",children:"تاريخ التخرج"})]})]}),(0,s.jsxs)(i.$,{variant:"outline",onClick:()=>B("asc"===W?"desc":"asc"),className:"flex items-center gap-2",children:["asc"===W?(0,s.jsx)(v.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"}),"asc"===W?"تصاعدي":"تنازلي"]})]})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"arabic-text",children:["قائمة المدارس (",X.length,")"]})}),(0,s.jsx)(l.Wu,{children:k?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400 arabic-text",children:"جاري التحميل..."})]}):0===X.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"لا توجد مدارس مطابقة للبحث"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"المدرسة"}),(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"المدينة"}),(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"عدد الطلاب"}),(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"تاريخ التخرج"}),(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"الحالة"}),(0,s.jsx)("th",{className:"text-right p-4 arabic-text",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{children:X.map(e=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text",children:e.name}),e.name_en&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.name_en}),e.email&&(0,s.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 text-gray-400"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.email})]})]})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-gray-900 dark:text-white arabic-text",children:e.city||"غير محدد"})]})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-gray-900 dark:text-white",children:e.student_count.toLocaleString()})]})}),(0,s.jsx)("td",{className:"p-4",children:e.graduation_date?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-gray-900 dark:text-white",children:new Date(e.graduation_date).toLocaleDateString("ar-AE")})]}):(0,s.jsx)("span",{className:"text-gray-500 arabic-text",children:"غير محدد"})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsx)(c.E,{variant:e.is_active?"default":"secondary",className:"cursor-pointer",onClick:()=>G(e),children:e.is_active?"نشطة":"غير نشطة"})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>I(e),children:(0,s.jsx)(y.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>M(e),className:"text-red-600 hover:text-red-700",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]})]})})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>x,Es:()=>h,L3:()=>m,c7:()=>u,lG:()=>l,rr:()=>g,zM:()=>c});var s=t(95155);t(12115);var r=t(15452),i=t(54416),n=t(59434);function l(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function d(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function o(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function x(e){let{className:a,children:t,showCloseButton:l=!0,...c}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(o,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...c,children:[t,l&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function m(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",a),...t})}},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>x,eb:()=>h,gC:()=>u,l6:()=>d,yv:()=>o});var s=t(95155);t(12115);var r=t(22918),i=t(66474),n=t(5196),l=t(47863),c=t(59434);function d(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function o(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function x(e){let{className:a,size:t="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:a,children:t,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(m,{}),(0,s.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(g,{})]})})}function h(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function m(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(52596),r=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,type:t,...i}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},80333:(e,a,t)=>{"use strict";t.d(a,{d:()=>n});var s=t(95155);t(12115);var r=t(4884),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var s=t(95155);t(12115);var r=t(40968),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88539:(e,a,t)=>{"use strict";t.d(a,{T:()=>i});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,1672,1056,1172,8441,1684,7358],()=>a(20273)),_N_E=e.O()}]);