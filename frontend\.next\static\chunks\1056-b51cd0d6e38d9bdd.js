"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1056],{4884:(e,t,r)=>{r.d(t,{bL:()=>D,zi:()=>j});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),s=r(5845),i=r(45503),u=r(11275),c=r(63655),d=r(95155),p="Switch",[f,g]=(0,l.A)(p),[m,v]=f(p),h=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:i,defaultChecked:u,required:f,disabled:g,value:v="on",onCheckedChange:h,form:b,...y}=e,[D,j]=n.useState(null),R=(0,a.s)(t,e=>j(e)),C=n.useRef(!1),I=!D||b||!!D.closest("form"),[k,E]=(0,s.i)({prop:i,defaultProp:null!=u&&u,onChange:h,caller:p});return(0,d.jsxs)(m,{scope:r,checked:k,disabled:g,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":k,"aria-required":f,"data-state":x(k),"data-disabled":g?"":void 0,disabled:g,value:v,...y,ref:R,onClick:(0,o.m)(e.onClick,e=>{E(e=>!e),I&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),I&&(0,d.jsx)(w,{control:D,bubbles:!C.current,name:l,value:v,checked:k,required:f,disabled:g,form:b,style:{transform:"translateX(-100%)"}})]})});h.displayName=p;var b="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(b,r);return(0,d.jsx)(c.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});y.displayName=b;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:l,bubbles:s=!0,...c}=e,p=n.useRef(null),f=(0,a.s)(p,t),g=(0,i.Z)(l),m=(0,u.X)(o);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(g!==l&&t){let r=new Event("click",{bubbles:s});t.call(e,l),e.dispatchEvent(r)}},[g,l,s]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:f,style:{...c.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var D=h,j=y},15452:(e,t,r)=>{r.d(t,{G$:()=>z,Hs:()=>x,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>$,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),s=r(61285),i=r(5845),u=r(19178),c=r(25519),d=r(34378),p=r(28905),f=r(63655),g=r(92293),m=r(93795),v=r(38168),h=r(99708),b=r(95155),y="Dialog",[w,x]=(0,l.A)(y),[D,j]=w(y),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[p,f]=(0,i.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:y});return(0,b.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};R.displayName=y;var C="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(C,r),s=(0,a.s)(t,l.triggerRef);return(0,b.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...n,ref:s,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});I.displayName=C;var k="DialogPortal",[E,N]=w(k,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=j(k,t);return(0,b.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,b.jsx)(p.C,{present:r||l.open,children:(0,b.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=k;var _="DialogOverlay",P=n.forwardRef((e,t)=>{let r=N(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(_,e.__scopeDialog);return a.modal?(0,b.jsx)(p.C,{present:n||a.open,children:(0,b.jsx)(A,{...o,ref:t})}):null});P.displayName=_;var F=(0,h.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(_,r);return(0,b.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(f.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",M=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(G,e.__scopeDialog);return(0,b.jsx)(p.C,{present:n||a.open,children:a.modal?(0,b.jsx)(S,{...o,ref:t}):(0,b.jsx)(T,{...o,ref:t})})});M.displayName=G;var S=n.forwardRef((e,t)=>{let r=j(G,e.__scopeDialog),l=n.useRef(null),s=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,b.jsx)(B,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=j(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,b.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,d=j(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,b.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...i,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(K,{titleId:d.titleId}),(0,b.jsx)(Y,{contentRef:p,descriptionId:d.descriptionId})]})]})}),L="DialogTitle",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(L,r);return(0,b.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});q.displayName=L;var W="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(W,r);return(0,b.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});Z.displayName=W;var H="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(H,r);return(0,b.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=H;var X="DialogTitleWarning",[z,J]=(0,l.q)(X,{contentName:G,titleName:L,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,r=J(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},$=R,Q=I,ee=O,et=P,er=M,en=q,eo=Z,ea=U},40968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(12115),o=r(63655),a=r(95155),l=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l}}]);