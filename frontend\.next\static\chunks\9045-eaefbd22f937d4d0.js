"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9045],{1243:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5623:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},9446:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},13717:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},17649:(e,a,t)=>{t.d(a,{UC:()=>O,VY:()=>V,ZD:()=>F,ZL:()=>L,bL:()=>R,hE:()=>I,hJ:()=>E,l9:()=>C,rc:()=>_});var r=t(12115),l=t(46081),i=t(6101),d=t(15452),n=t(85185),c=t(99708),s=t(95155),o="AlertDialog",[y,p]=(0,l.A)(o,[d.Hs]),h=(0,d.Hs)(),u=e=>{let{__scopeAlertDialog:a,...t}=e,r=h(a);return(0,s.jsx)(d.bL,{...r,...t,modal:!0})};u.displayName=o;var k=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,l=h(t);return(0,s.jsx)(d.l9,{...l,...r,ref:a})});k.displayName="AlertDialogTrigger";var A=e=>{let{__scopeAlertDialog:a,...t}=e,r=h(a);return(0,s.jsx)(d.ZL,{...r,...t})};A.displayName="AlertDialogPortal";var f=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,l=h(t);return(0,s.jsx)(d.hJ,{...l,...r,ref:a})});f.displayName="AlertDialogOverlay";var v="AlertDialogContent",[m,x]=y(v),g=(0,c.Dc)("AlertDialogContent"),M=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,children:l,...c}=e,o=h(t),y=r.useRef(null),p=(0,i.s)(a,y),u=r.useRef(null);return(0,s.jsx)(d.G$,{contentName:v,titleName:b,docsSlug:"alert-dialog",children:(0,s.jsx)(m,{scope:t,cancelRef:u,children:(0,s.jsxs)(d.UC,{role:"alertdialog",...o,...c,ref:p,onOpenAutoFocus:(0,n.m)(c.onOpenAutoFocus,e=>{var a;e.preventDefault(),null==(a=u.current)||a.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(g,{children:l}),(0,s.jsx)(z,{contentRef:y})]})})})});M.displayName=v;var b="AlertDialogTitle",w=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,l=h(t);return(0,s.jsx)(d.hE,{...l,...r,ref:a})});w.displayName=b;var j="AlertDialogDescription",D=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,l=h(t);return(0,s.jsx)(d.VY,{...l,...r,ref:a})});D.displayName=j;var N=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,l=h(t);return(0,s.jsx)(d.bm,{...l,...r,ref:a})});N.displayName="AlertDialogAction";var q="AlertDialogCancel",H=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,{cancelRef:l}=x(q,t),n=h(t),c=(0,i.s)(a,l);return(0,s.jsx)(d.bm,{...n,...r,ref:c})});H.displayName=q;var z=e=>{let{contentRef:a}=e,t="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=a.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(t)},[t,a]),null},R=u,C=k,L=A,E=f,O=M,_=N,F=H,I=w,V=D},29869:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},35169:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40646:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53904:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},55670:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},78749:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);