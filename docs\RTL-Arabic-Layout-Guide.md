# دليل تخطيط المحتوى العربي (RTL Layout Guide)

## نظرة عامة
هذا الدليل يحتوي على الصيغ والأساليب المستخدمة لضمان العرض الصحيح للمحتوى العربي من اليمين إلى اليسار (RTL) في منصة Toqs.

## 1. إعدادات CSS الأساسية

### تحسين عرض النص العربي
```css
/* تحسين عرض النص العربي */
html[dir="rtl"] {
  font-feature-settings: "liga" 1, "kern" 1;
}

/* تحسين المسافات للنص العربي */
.arabic-text {
  line-height: 1.8;
  letter-spacing: 0.02em;
}
```

### تحسين Grid Layout للـ RTL
```css
/* تحسين Grid Layout للـ RTL */
html[dir="rtl"] .grid {
  direction: rtl;
}

/* تحسين Flexbox للـ RTL */
html[dir="rtl"] .flex {
  direction: rtl;
}
```

## 2. فئات CSS المخصصة للمنتجات والفئات

### فئات الشبكة المخصصة
```css
/* تحسين عرض البطاقات والمنتجات */
html[dir="rtl"] .product-grid,
html[dir="rtl"] .category-grid {
  direction: rtl;
}

/* تحسين عرض البطاقات في الشبكة */
html[dir="rtl"] .product-grid > *,
html[dir="rtl"] .category-grid > * {
  direction: ltr; /* المحتوى الداخلي للبطاقات يبقى LTR */
}

/* تحسين عرض النصوص العربية في البطاقات */
html[dir="rtl"] .product-grid .arabic-text,
html[dir="rtl"] .category-grid .arabic-text {
  direction: rtl;
  text-align: right;
}
```

## 3. تحسين عرض الأسعار والأرقام

### الأسعار والأرقام
```css
/* تحسين عرض الصور والأيقونات */
html[dir="rtl"] .product-grid img,
html[dir="rtl"] .category-grid img {
  direction: ltr;
}

/* تحسين عرض الأسعار والأرقام */
html[dir="rtl"] .price,
html[dir="rtl"] .number {
  direction: ltr;
  text-align: left;
}
```

## 4. تحسين الجداول

### إعدادات الجداول للـ RTL
```css
/* تحسين عرض الجداول */
html[dir="rtl"] table {
  direction: rtl;
}

/* تحسين عرض الجداول في RTL */
html[dir="rtl"] table th,
html[dir="rtl"] table td {
  text-align: right;
}

/* تحسين عرض الأرقام والأسعار في الجداول */
html[dir="rtl"] table .price,
html[dir="rtl"] table .number,
html[dir="rtl"] table .rating {
  text-align: left;
  direction: ltr;
}

/* تحسين عرض الأزرار في الجداول */
html[dir="rtl"] table .actions {
  direction: ltr;
}
```

## 5. تطبيق الفئات في HTML

### مثال على استخدام فئات المنتجات
```jsx
{/* Products Grid */}
<div className="product-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  {products.map((product) => (
    <div key={product.id} className="card">
      <h3 className="arabic-text">{product.name}</h3>
      <p className="price">{product.price} درهم</p>
    </div>
  ))}
</div>
```

### مثال على استخدام فئات الفئات
```jsx
{/* Categories Grid */}
<div className="category-grid grid grid-cols-1 md:grid-cols-2 gap-4">
  {categories.map((category) => (
    <div key={category.id} className="card">
      <h3 className="arabic-text">{category.name}</h3>
      <p className="number">{category.products_count} منتج</p>
    </div>
  ))}
</div>
```

### مثال على الجداول
```jsx
<table>
  <thead>
    <tr>
      <th className="arabic-text">اسم المنتج</th>
      <th className="arabic-text">السعر</th>
      <th className="arabic-text">التقييم</th>
      <th className="arabic-text">الإجراءات</th>
    </tr>
  </thead>
  <tbody>
    {products.map((product) => (
      <tr key={product.id}>
        <td className="arabic-text">{product.name}</td>
        <td className="price">{product.price} درهم</td>
        <td className="rating">{product.rating}</td>
        <td className="actions">
          <button>تعديل</button>
          <button>حذف</button>
        </td>
      </tr>
    ))}
  </tbody>
</table>
```

## 6. إعدادات إضافية

### تحسين الأزرار والعناصر التفاعلية
```css
/* تحسين عرض الأزرار والعناصر التفاعلية */
html[dir="rtl"] .btn-group {
  direction: rtl;
}
```

## 7. قائمة الفئات المهمة

### الفئات الأساسية للاستخدام:
- `.product-grid` - للشبكات التي تحتوي على منتجات
- `.category-grid` - للشبكات التي تحتوي على فئات
- `.arabic-text` - للنصوص العربية
- `.price` - للأسعار والمبالغ المالية
- `.number` - للأرقام والإحصائيات
- `.rating` - للتقييمات
- `.actions` - لأزرار الإجراءات

## 8. ملاحظات مهمة

1. **اتجاه الشبكة**: استخدم `product-grid` أو `category-grid` للشبكات الرئيسية
2. **النصوص العربية**: أضف `arabic-text` لجميع النصوص العربية
3. **الأسعار والأرقام**: استخدم `price` و `number` للحفاظ على الاتجاه الصحيح
4. **الجداول**: الجداول تحتاج إلى فئات خاصة للأسعار والإجراءات
5. **التناسق**: تأكد من تطبيق الفئات بشكل متسق في جميع أنحاء التطبيق

## 9. اختبار RTL

### للتأكد من العمل الصحيح:
1. تحقق من اتجاه عرض المنتجات (يمين إلى يسار)
2. تأكد من محاذاة النصوص العربية إلى اليمين
3. تحقق من عرض الأسعار والأرقام بالاتجاه الصحيح
4. اختبر الجداول والأزرار

---

**تاريخ الإنشاء**: 2025-01-10  
**آخر تحديث**: 2025-01-10  
**الإصدار**: 1.0
