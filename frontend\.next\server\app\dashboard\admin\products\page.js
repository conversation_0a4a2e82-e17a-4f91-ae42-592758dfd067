(()=>{var e={};e.id=5006,e.ids=[5006],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,s)=>{"use strict";s.d(a,{A0:()=>c,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>x,nd:()=>o});var t=s(60687),r=s(43210),l=s(4780);let i=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",e),...a})}));i.displayName="Table";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",e),...a}));c.displayName="TableHeader";let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...a}));n.displayName="TableBody",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tfoot",{ref:s,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let d=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));d.displayName="TableRow";let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));o.displayName="TableHead";let x=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));x.displayName="TableCell",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15116:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=s(65239),r=s(48088),l=s(88170),i=s.n(l),c=s(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);s.d(a,n);let d={children:["",{children:["dashboard",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57802)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/products/page",pathname:"/dashboard/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19080:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46657:(e,a,s)=>{"use strict";s.d(a,{k:()=>y});var t=s(60687),r=s(43210),l=s(11273),i=s(14163),c="Progress",[n,d]=(0,l.A)(c),[o,x]=n(c),m=r.forwardRef((e,a)=>{var s,r;let{__scopeProgress:l,value:c=null,max:n,getValueLabel:d=u,...x}=e;(n||0===n)&&!b(n)&&console.error((s=`${n}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=b(n)?n:100;null===c||f(c,m)||console.error((r=`${c}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=f(c,m)?c:null,p=g(h)?d(h,m):void 0;return(0,t.jsx)(o,{scope:l,value:h,max:m,children:(0,t.jsx)(i.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(h)?h:void 0,"aria-valuetext":p,role:"progressbar","data-state":j(h,m),"data-value":h??void 0,"data-max":m,...x,ref:a})})});m.displayName=c;var h="ProgressIndicator",p=r.forwardRef((e,a)=>{let{__scopeProgress:s,...r}=e,l=x(h,s);return(0,t.jsx)(i.sG.div,{"data-state":j(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:a})});function u(e,a){return`${Math.round(e/a*100)}%`}function j(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function g(e){return"number"==typeof e}function b(e){return g(e)&&!isNaN(e)&&e>0}function f(e,a){return g(e)&&!isNaN(e)&&e<=a&&e>=0}p.displayName=h;var v=s(4780);function y({className:e,value:a,...s}){return(0,t.jsx)(m,{"data-slot":"progress",className:(0,v.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,t.jsx)(p,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})})}},47763:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>ee});var t=s(60687),r=s(43210),l=s(63213),i=s(32884),c=s(44493),n=s(29523),d=s(96834),o=s(89667),x=s(80013),m=s(15079),h=s(63503),p=s(85763),u=s(6211),j=s(21342),g=s(34729),b=s(54987),f=s(19080),v=s(98971),y=s(62688);let N=(0,y.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var w=s(64398),k=s(93613),C=s(96474),_=s(11860),A=s(13861),T=s(8819),$=s(46657),S=s(91821);let q=(0,y.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var E=s(16023);let z=(0,y.A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);var P=s(88233);let F=(0,y.A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);var D=s(13964);function R({images:e,onImagesChange:a,maxImages:s=10,maxSize:l=5242880,acceptedTypes:i=["image/jpeg","image/png","image/webp"],className:o=""}){let[x,m]=(0,r.useState)(!1),[h,p]=(0,r.useState)({}),[u,j]=(0,r.useState)(!0),[g,b]=(0,r.useState)([]),f=(0,r.useMemo)(()=>Array.isArray(e)?e:[],[e]),v=(0,r.useCallback)(async()=>{try{let e=(await fetch("/api/health",{method:"HEAD"})).ok;return j(e),e}catch{return j(!1),!1}},[]),y=(0,r.useCallback)(e=>i.includes(e.type)?e.size>l?`حجم الملف كبير جداً. الحد الأقصى ${(l/1024/1024).toFixed(1)} ميجابايت`:null:"نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP",[i,l]),N=(0,r.useCallback)((e,a)=>new Promise((s,t)=>{let r=new FileReader;r.onload=()=>{try{let e=r.result;localStorage.setItem(`fallback_image_${a}`,e),s(e)}catch(e){t(e)}},r.onerror=t,r.readAsDataURL(e)}),[]),w=(0,r.useCallback)(async e=>new Promise(a=>{let s=document.createElement("canvas"),t=s.getContext("2d"),r=new Image;r.onload=()=>{let{width:l,height:i}=r;l>i?l>1200&&(i=1200*i/l,l=1200):i>1200&&(l=1200*l/i,i=1200),s.width=l,s.height=i,t?.drawImage(r,0,0,l,i),s.toBlob(s=>{s?a(new File([s],e.name,{type:"image/jpeg",lastModified:Date.now()})):a(e)},"image/jpeg",.8)},r.src=URL.createObjectURL(e)}),[]),C=(0,r.useCallback)(async(e,a,s=2)=>{let t=await w(e);for(let r=1;r<=s;r++)try{p(e=>({...e,[a]:(r-1)*40}));let e=new FormData;e.append("files",t),e.append("folder","products");let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error(`HTTP ${s.status}`);let l=await s.json();if(p(e=>({...e,[a]:100})),l.uploadedFiles&&l.uploadedFiles.length>0)return l.uploadedFiles[0].url;throw Error("لم يتم إرجاع رابط الصورة")}catch(t){if(console.error(`Upload attempt ${r} failed:`,t),r===s){let s=await N(e,a);return p(e=>({...e,[a]:100})),s}await new Promise(e=>setTimeout(e,1e3*r))}throw Error("فشل في رفع الصورة")},[w,N]),A=(0,r.useCallback)(async t=>{let r=[],l=(Array.isArray(e)?e:[]).length,i=[];await v();for(let e=0;e<t.length&&l+r.length<s;e++){let a=t[e],s=y(a),l=`${Date.now()}-${e}`;if(s){i.push(`${a.name}: ${s}`);continue}try{let e=await new Promise((e,s)=>{let t=new FileReader;t.onload=a=>e(a.target?.result),t.onerror=s,t.readAsDataURL(a)}),s={file:a,preview:e,id:l,uploading:!0,uploaded:!1};r.push(s)}catch(e){console.error("Preview creation failed:",e),i.push(`${a.name}: فشل في إنشاء المعاينة`)}}if(r.length>0){a([...e,...r]);let s=r.map(async e=>{try{let s=await C(e.file,e.id);a(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!0,fallbackUrl:s}:a))}catch(s){console.error("Upload failed:",s),i.push(`${e.file.name}: فشل في الرفع`),a(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!1,error:"فشل في الرفع"}:a))}});await Promise.allSettled(s)}i.length>0&&(b(i),setTimeout(()=>b([]),5e3))},[e,s,a,v,y,C]),T=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?m(!0):"dragleave"===e.type&&m(!1)},[]),R=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),m(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&A(e.dataTransfer.files)},[A]),U=e=>{a(f.filter(a=>a.id!==e))},M=(e,s)=>{let t=[...f],[r]=t.splice(e,1);t.splice(s,0,r),a(t)};return(0,t.jsxs)("div",{className:`space-y-4 ${o}`,children:[!u&&(0,t.jsxs)(S.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(q,{className:"h-4 w-4"}),(0,t.jsx)(S.TN,{className:"arabic-text",children:"لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية."})]}),g.length>0&&(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)(S.TN,{children:(0,t.jsx)("div",{className:"space-y-1",children:g.map((e,a)=>(0,t.jsx)("div",{className:"text-sm arabic-text",children:e},a))})})]}),(0,t.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${x?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400"} ${e.length>=s?"opacity-50 pointer-events-none":""}`,onDragEnter:T,onDragLeave:T,onDragOver:T,onDrop:R,children:[(0,t.jsx)(E.A,{className:`h-12 w-12 mx-auto mb-4 ${x?"text-blue-500":"text-gray-400"}`}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium arabic-text",children:x?"أفلت الصور هنا":"اسحب الصور هنا أو"}),f.length<s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("input",{ref:e=>{e&&(window.fileInput=e)},type:"file",multiple:!0,accept:i.join(","),className:"hidden",onChange:e=>{e.target.files&&e.target.files.length>0&&(A(e.target.files),e.target.value="")},disabled:f.length>=s}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",disabled:f.length>=s,onClick:()=>{let e=window.fileInput;e&&e.click()},children:[(0,t.jsx)(z,{className:"h-4 w-4 mr-2"}),"اختر الصور"]})]})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-1",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500 arabic-text",children:["يمكنك رفع حتى ",s," صور بحجم أقصى ",(l/1024/1024).toFixed(1)," ميجابايت لكل صورة"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["الصيغ المدعومة: ",i.map(e=>e.split("/")[1].toUpperCase()).join(", ")]})]}),f.length>=s&&(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2 text-orange-600",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الوصول للحد الأقصى من الصور"})]})]}),f.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium arabic-text",children:["الصور المرفوعة (",f.length,"/",s,")"]}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>a([]),className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"حذف الكل"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:f.map((a,s)=>(0,t.jsx)(c.Zp,{className:"relative group overflow-hidden",children:(0,t.jsx)(c.Wu,{className:"p-0",children:a.error?(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20",children:(0,t.jsxs)("div",{className:"text-center p-4",children:[(0,t.jsx)(k.A,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-xs text-red-600 arabic-text",children:a.error})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("img",{src:a.preview,alt:`صورة ${s+1}`,className:"w-full aspect-square object-cover"}),a.uploading&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(F,{className:"h-5 w-5 animate-spin text-blue-500"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"جاري الرفع..."})]}),(0,t.jsx)($.k,{value:h[a.id]||0,className:"h-3 mb-2"}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-600 dark:text-gray-400",children:[h[a.id]||0,"%"]})]})}),a.uploaded&&(0,t.jsx)("div",{className:"absolute top-2 left-2",children:(0,t.jsxs)(d.E,{className:a.fallbackUrl?.startsWith("data:")?"bg-orange-600":"bg-green-600",children:[(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),a.fallbackUrl?.startsWith("data:")?"محفوظ محلياً":"تم الرفع"]})}),!u&&a.uploaded&&a.fallbackUrl?.startsWith("data:")&&(0,t.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white/90",children:[(0,t.jsx)(q,{className:"h-3 w-3 mr-1"}),"غير متصل"]})}),(0,t.jsx)("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsx)("div",{className:"flex gap-1",children:(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"destructive",onClick:()=>U(a.id),className:"h-8 w-8 p-0",disabled:a.uploading,children:(0,t.jsx)(_.A,{className:"h-3 w-3"})})})}),0===s&&(0,t.jsx)(d.E,{className:"absolute bottom-2 left-2 bg-blue-600",children:"الصورة الرئيسية"}),(0,t.jsx)("div",{className:"absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[s>0&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>M(s,s-1),className:"h-6 w-6 p-0",children:"←"}),s<e.length-1&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>M(s,s+1),className:"h-6 w-6 p-0",children:"→"})]})})]})})},a.id))})]})]})}let U=["أسود","أزرق داكن","بورجوندي","ذهبي","فضي","أبيض","أحمر","أخضر","بنفسجي","وردي","برتقالي","بني"],M=["XS","S","M","L","XL","XXL","XXXL","واحد"];function G({onSubmit:e,onCancel:a,initialData:s={},isEditing:l=!1}){let[i,h]=(0,r.useState)({name:s.name||"",description:s.description||"",category:s.category||"",price:s.price||0,rental_price:s.rental_price||0,colors:s.colors||[],sizes:s.sizes||[],images:s.images||[],stock_quantity:s.stock_quantity||0,is_available:s.is_available??!0,features:s.features||[],specifications:s.specifications||{}}),[u,j]=(0,r.useState)([]),[y,$]=(0,r.useState)(!0),[S,q]=(0,r.useState)(""),[E,z]=(0,r.useState)(""),[P,F]=(0,r.useState)(""),[D,G]=(0,r.useState)(""),[L,I]=(0,r.useState)(""),[B,Z]=(0,r.useState)({}),O=()=>{let e={};return i.name.trim()||(e.name="اسم المنتج مطلوب"),i.description.trim()||(e.description="وصف المنتج مطلوب"),i.category||(e.category="فئة المنتج مطلوبة"),i.price<=0&&(e.price="السعر يجب أن يكون أكبر من صفر"),0===i.colors.length&&(e.colors="يجب إضافة لون واحد على الأقل"),0===i.sizes.length&&(e.sizes="يجب إضافة مقاس واحد على الأقل"),i.stock_quantity<0&&(e.stock_quantity="كمية المخزون لا يمكن أن تكون سالبة"),Z(e),0===Object.keys(e).length},V=e=>{h(a=>({...a,colors:a.colors.filter(a=>a!==e)}))},X=e=>{h(a=>({...a,sizes:a.sizes.filter(a=>a!==e)}))},J=e=>{h(a=>({...a,features:a.features.filter(a=>a!==e)}))},W=e=>{h(a=>{let s={...a.specifications};return delete s[e],{...a,specifications:s}})};return(0,t.jsxs)("form",{onSubmit:a=>{a.preventDefault(),O()&&e(i)},className:"space-y-6",children:[(0,t.jsxs)(p.tU,{defaultValue:"basic",className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(p.Xi,{value:"basic",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"المعلومات الأساسية"]}),(0,t.jsxs)(p.Xi,{value:"details",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"التفاصيل والألوان"]}),(0,t.jsxs)(p.Xi,{value:"images",className:"arabic-text",children:[(0,t.jsx)(N,{className:"h-4 w-4 mr-2"}),"الصور"]}),(0,t.jsxs)(p.Xi,{value:"features",className:"arabic-text",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"المميزات والمواصفات"]})]}),(0,t.jsxs)(p.av,{value:"basic",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"name",className:"arabic-text",children:"اسم المنتج *"}),(0,t.jsx)(o.p,{id:"name",value:i.name,onChange:e=>h(a=>({...a,name:e.target.value})),placeholder:"أدخل اسم المنتج",className:"arabic-text"}),B.name&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.name]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"category",className:"arabic-text",children:"فئة المنتج *"}),(0,t.jsxs)(m.l6,{value:i.category,onValueChange:e=>h(a=>({...a,category:e})),disabled:y,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:y?"جاري تحميل الفئات...":"اختر فئة المنتج"})}),(0,t.jsx)(m.gC,{children:u.map(e=>(0,t.jsx)(m.eb,{value:e.slug,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{className:"arabic-text",children:e.name_ar})]})},e.slug))})]}),B.category&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.category]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"description",className:"arabic-text",children:"وصف المنتج *"}),(0,t.jsx)(g.T,{id:"description",value:i.description,onChange:e=>h(a=>({...a,description:e.target.value})),placeholder:"أدخل وصف مفصل للمنتج",className:"arabic-text min-h-[100px]"}),B.description&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.description]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"price",className:"arabic-text",children:"السعر (درهم) *"}),(0,t.jsx)(o.p,{id:"price",type:"number",min:"0",step:"0.01",value:i.price,onChange:e=>h(a=>({...a,price:parseFloat(e.target.value)||0})),placeholder:"0.00"}),B.price&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.price]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"rental_price",className:"arabic-text",children:"سعر الإيجار (درهم)"}),(0,t.jsx)(o.p,{id:"rental_price",type:"number",min:"0",step:"0.01",value:i.rental_price,onChange:e=>h(a=>({...a,rental_price:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"stock_quantity",className:"arabic-text",children:"كمية المخزون *"}),(0,t.jsx)(o.p,{id:"stock_quantity",type:"number",min:"0",value:i.stock_quantity,onChange:e=>h(a=>({...a,stock_quantity:parseInt(e.target.value)||0})),placeholder:"0"}),B.stock_quantity&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.stock_quantity]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.d,{id:"is_available",checked:i.is_available,onCheckedChange:e=>h(a=>({...a,is_available:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_available",className:"arabic-text",children:"متاح للبيع"})]})]}),(0,t.jsxs)(p.av,{value:"details",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"الألوان المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف الألوان المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:S,onValueChange:q,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر لون"})}),(0,t.jsx)(m.gC,{children:U.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:S,onChange:e=>q(e.target.value),placeholder:"أو أدخل لون مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{S.trim()&&!i.colors.includes(S.trim())&&(h(e=>({...e,colors:[...e.colors,S.trim()]})),q(""))},size:"sm",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:i.colors.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>V(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(_.A,{className:"h-3 w-3"})})]},a))}),B.colors&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.colors]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"المقاسات المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المقاسات المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:E,onValueChange:z,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر مقاس"})}),(0,t.jsx)(m.gC,{children:M.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:E,onChange:e=>z(e.target.value),placeholder:"أو أدخل مقاس مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{E.trim()&&!i.sizes.includes(E.trim())&&(h(e=>({...e,sizes:[...e.sizes,E.trim()]})),z(""))},size:"sm",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:i.sizes.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>X(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(_.A,{className:"h-3 w-3"})})]},a))}),B.sizes&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),B.sizes]})]})]})]}),(0,t.jsx)(p.av,{value:"images",className:"space-y-6 mt-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"صور المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)(R,{images:i.images,onImagesChange:e=>h(a=>({...a,images:e})),maxImages:8,maxSize:5242880,acceptedTypes:["image/jpeg","image/png","image/webp"]})})]})}),(0,t.jsxs)(p.av,{value:"features",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مميزات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المميزات الرئيسية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:P,onChange:e=>F(e.target.value),placeholder:"أدخل ميزة جديدة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{P.trim()&&!i.features.includes(P.trim())&&(h(e=>({...e,features:[...e.features,P.trim()]})),F(""))},size:"sm",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"space-y-2",children:i.features.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:e}),(0,t.jsx)("button",{type:"button",onClick:()=>J(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})})]},a))}),0===i.features.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مميزات بعد"})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مواصفات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المواصفات التقنية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(o.p,{value:D,onChange:e=>G(e.target.value),placeholder:"اسم المواصفة (مثل: المادة)",className:"arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:L,onChange:e=>I(e.target.value),placeholder:"قيمة المواصفة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{D.trim()&&L.trim()&&(h(e=>({...e,specifications:{...e.specifications,[D.trim()]:L.trim()}})),G(""),I(""))},size:"sm",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:Object.entries(i.specifications).map(([e,a])=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsxs)("span",{className:"font-medium",children:[e,":"]})," ",a]}),(0,t.jsx)("button",{type:"button",onClick:()=>W(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})})]},e))}),0===Object.keys(i.specifications).length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مواصفات بعد"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:a,children:"إلغاء"}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"معاينة"]}),(0,t.jsxs)(n.$,{type:"submit",children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),l?"تحديث المنتج":"إضافة المنتج"]})]})]})}function L({product:e,open:a,onOpenChange:s,onSave:l}){let[i,c]=(0,r.useState)(!1),n=async a=>{try{c(!0),await l({...a,id:e?.id}),s(!1)}catch(e){console.error("Error updating product:",e)}finally{c(!1)}};return e?(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(h.c7,{children:(0,t.jsxs)(h.L3,{className:"arabic-text",children:["تعديل المنتج: ",e.name]})}),(0,t.jsx)(G,{initialData:{name:e.name,description:e.description,category:e.category,price:e.price,rental_price:e.rental_price,colors:e.colors,sizes:e.sizes,images:e.images.map((e,a)=>({id:`existing-${a}`,file:new File([],"existing-image"),preview:e,uploaded:!0,fallbackUrl:e})),stock_quantity:e.stock_quantity,is_available:e.is_available,features:e.features||[],specifications:e.specifications||{}},onSubmit:n,onCancel:()=>{s(!1)},isEditing:!0})]})}):null}var I=s(75373),B=s(62694),Z=s(85814),O=s.n(Z),V=s(28559),X=s(31158),J=s(43649),W=s(25541),H=s(99270),K=s(63143),Q=s(93661);let Y={gown:"ثوب التخرج",cap:"قبعة التخرج",tassel:"الشرابة",stole:"الوشاح",hood:"القلنسوة"};function ee(){let{user:e,profile:a}=(0,l.A)(),s=(0,B.dj)(),[b,v]=(0,r.useState)([]),[y,N]=(0,r.useState)([]),[k,_]=(0,r.useState)(""),[T,$]=(0,r.useState)("all"),[S,q]=(0,r.useState)("all"),[E,z]=(0,r.useState)("created_at"),[F,D]=(0,r.useState)("desc"),[R,U]=(0,r.useState)(!0),[M,Z]=(0,r.useState)(!1),[ee,ea]=(0,r.useState)(null),[es,et]=(0,r.useState)(!1),[er,el]=(0,r.useState)({open:!1,productId:"",productName:""}),[ei,ec]=(0,r.useState)([]),[en,ed]=(0,r.useState)(!1),[eo,ex]=(0,r.useState)(null),[em,eh]=(0,r.useState)({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),[ep,eu]=(0,r.useState)("products"),ej=async()=>{try{U(!0);let e=await fetch("/api/products");if(!e.ok)throw Error("فشل في جلب المنتجات");let a=await e.json();v(a.products||[]),N(a.products||[])}catch(e){console.error("Error fetching products:",e),s.error("فشل في جلب المنتجات")}finally{U(!1)}},eg=(e,a)=>{el({open:!0,productId:e,productName:a})},eb=async()=>{try{let e=await fetch(`/api/products/${er.productId}`,{method:"DELETE"});if(!e.ok){let a=await e.json();throw Error(a.error||"فشل في حذف المنتج")}await ej(),s.success("تم حذف المنتج بنجاح!")}catch(e){console.error("Error deleting product:",e),s.error(e instanceof Error?e.message:"فشل في حذف المنتج")}},ef=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch(`/api/products/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_available:!a.is_available})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة المنتج")}await ej(),s.success(`تم ${!a.is_available?"تفعيل":"إلغاء تفعيل"} المنتج بنجاح`)}catch(e){console.error("Error toggling availability:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة المنتج")}},ev=async e=>{try{U(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في إضافة المنتج")}await t.json(),await ej(),Z(!1),s.success("تم إضافة المنتج بنجاح!")}catch(e){console.error("Error adding product:",e),s.error(e instanceof Error?e.message:"فشل في إضافة المنتج")}finally{U(!1)}},ey=e=>{ea(e),et(!0)},eN=async e=>{try{U(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch(`/api/products/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث المنتج")}await ej(),s.success("تم تحديث المنتج بنجاح!")}catch(e){console.error("Error updating product:",e),s.error(e instanceof Error?e.message:"فشل في تحديث المنتج")}finally{U(!1)}},ew=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();ec(a.categories||[])}}catch(e){console.error("Error fetching categories:",e)}},ek=async()=>{try{let e=eo?`/api/categories/${eo.id}`:"/api/categories",a=eo?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(em)});if(t.ok)await ew(),eC(),s.success(eo?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!");else{let e=await t.json();s.error(e.error||"فشل في حفظ الفئة")}}catch(e){console.error("Error saving category:",e),s.error("فشل في حفظ الفئة")}},eC=()=>{eh({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),ex(null),ed(!1)},e_=e=>{eh({name_ar:e.name_ar,slug:e.slug,description:e.description||"",icon:e.icon||"",is_active:e.is_active,order_index:e.order_index}),ex(e),ed(!0)},eA=async e=>{try{let a=await fetch(`/api/categories/${e}`,{method:"DELETE"});if(a.ok)await ew(),s.success("تم حذف الفئة بنجاح!");else{let e=await a.json();s.error(e.error||"فشل في حذف الفئة")}}catch(e){console.error("Error deleting category:",e),s.error("فشل في حذف الفئة")}};return(console.log("User:",e),console.log("Profile:",a),console.log("Profile role:",a?.role),e&&a?.role==="admin")?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(O(),{href:"/dashboard/admin",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المنتجات والفئات \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة منتجات وفئات المنصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),"products"===ep&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>Z(!0),children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]}),"categories"===ep&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>{eC(),ed(!0)},children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})]})]})]}),(0,t.jsxs)(p.tU,{value:ep,onValueChange:eu,className:"space-y-6",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(p.Xi,{value:"products",className:"arabic-text",children:"المنتجات"}),(0,t.jsx)(p.Xi,{value:"categories",className:"arabic-text",children:"الفئات"})]}),(0,t.jsxs)(p.av,{value:"products",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المنتجات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.length})]}),(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المتاحة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_available).length})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"مخزون منخفض"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.stock_quantity<20).length})]}),(0,t.jsx)(J.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"متوسط التقييم"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(b.reduce((e,a)=>e+(a.rating||0),0)/b.length).toFixed(1)})]}),(0,t.jsx)(W.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsx)(c.Zp,{className:"mb-6",children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(H.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"البحث في المنتجات...",value:k,onChange:e=>_(e.target.value),className:"pl-10 arabic-text"})]}),(0,t.jsxs)(m.l6,{value:T,onValueChange:$,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع الفئات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع الفئات"}),Object.entries(Y).map(([e,a])=>(0,t.jsx)(m.eb,{value:e,children:a},e))]})]}),(0,t.jsxs)(m.l6,{value:S,onValueChange:q,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع المنتجات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(m.eb,{value:"available",children:"متاح"}),(0,t.jsx)(m.eb,{value:"unavailable",children:"غير متاح"})]})]}),(0,t.jsxs)(m.l6,{value:E,onValueChange:z,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"ترتيب حسب"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"created_at",children:"تاريخ الإنشاء"}),(0,t.jsx)(m.eb,{value:"name",children:"الاسم"}),(0,t.jsx)(m.eb,{value:"price",children:"السعر"}),(0,t.jsx)(m.eb,{value:"stock_quantity",children:"المخزون"}),(0,t.jsx)(m.eb,{value:"rating",children:"التقييم"})]})]}),(0,t.jsxs)(m.l6,{value:F,onValueChange:e=>D(e),children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"desc",children:"تنازلي"}),(0,t.jsx)(m.eb,{value:"asc",children:"تصاعدي"})]})]})]})})}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["قائمة المنتجات (",y.length,")"]})}),(0,t.jsx)(c.Wu,{children:R?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-500 mt-2 arabic-text",children:"جاري التحميل..."})]}):0===y.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(f.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد منتجات"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المنتج"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الفئة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"السعر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المخزون"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"التقييم"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الحالة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)("tbody",{children:y.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:e.images[0]||"/api/placeholder/80/80",alt:e.name,className:"w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700"}),e.images.length>1&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:e.images.length})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text line-clamp-1",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500 arabic-text line-clamp-2 mt-1",children:e.description})]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:Y[e.category]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"price font-medium text-gray-900 dark:text-white",children:[e.price," درهم"]}),e.rental_price&&(0,t.jsxs)("p",{className:"price text-gray-500",children:["إيجار: ",e.rental_price," درهم"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:`font-medium ${e.stock_quantity<20?"text-red-600":e.stock_quantity<50?"text-orange-600":"text-green-600"}`,children:e.stock_quantity}),e.stock_quantity<20&&(0,t.jsx)(J.A,{className:"h-4 w-4 text-red-600"})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"rating flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"number text-sm font-medium",children:e.rating?.toFixed(1)||"N/A"}),(0,t.jsxs)("span",{className:"number text-xs text-gray-500",children:["(",e.reviews_count||0,")"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_available?"default":"secondary",className:"arabic-text",children:e.is_available?"متاح":"غير متاح"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"actions flex items-center gap-2",children:[(0,t.jsx)(n.$,{size:"sm",variant:"outline",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>ey(e),children:(0,t.jsx)(K.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>ef(e.id),children:e.is_available?"\uD83D\uDD12":"\uD83D\uDD13"}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eg(e.id,e.name),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]})]}),(0,t.jsx)(p.av,{value:"categories",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إدارة الفئات"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nd,{className:"arabic-text",children:"الاسم"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الحالة"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الترتيب"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)(u.BF,{children:ei.map(e=>(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 arabic-text",children:e.description})]})]})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,t.jsx)(u.nA,{children:e.order_index}),(0,t.jsx)(u.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,t.jsx)(u.nA,{children:(0,t.jsxs)(j.rI,{children:[(0,t.jsx)(j.ty,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(j.SQ,{align:"end",children:[(0,t.jsxs)(j._2,{onClick:()=>e_(e),children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsxs)(j._2,{onClick:()=>eA(e.id),className:"text-red-600",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})})]})]}),(0,t.jsx)(h.lG,{open:en,onOpenChange:ed,children:(0,t.jsxs)(h.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:eo?"تعديل الفئة":"إضافة فئة جديدة"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:eo?"تعديل بيانات الفئة":"أدخل بيانات الفئة الجديدة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-name",className:"arabic-text",children:"اسم الفئة (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-name",value:em.name_ar,onChange:e=>eh(a=>({...a,name_ar:e.target.value})),placeholder:"أدخل اسم الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-slug",className:"arabic-text",children:"الرابط المختصر (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-slug",value:em.slug,onChange:e=>eh(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-description",className:"arabic-text",children:"الوصف"}),(0,t.jsx)(g.T,{id:"category-description",value:em.description,onChange:e=>eh(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-icon",className:"arabic-text",children:"الأيقونة"}),(0,t.jsx)(o.p,{id:"category-icon",value:em.icon,onChange:e=>eh(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDFF7️"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"category-active",checked:em.is_active,onChange:e=>eh(a=>({...a,is_active:e.target.checked}))}),(0,t.jsx)(x.J,{htmlFor:"category-active",className:"arabic-text",children:"فئة نشطة"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:eC,children:"إلغاء"}),(0,t.jsx)(n.$,{onClick:ek,children:eo?"تحديث":"إضافة"})]})]})]})}),(0,t.jsx)(h.lG,{open:M,onOpenChange:Z,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:"إضافة منتج جديد"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:"أدخل تفاصيل المنتج الجديد"})]}),(0,t.jsx)(G,{onSubmit:ev,onCancel:()=>Z(!1)})]})}),(0,t.jsx)(L,{product:ee,open:es,onOpenChange:et,onSave:eN}),(0,t.jsx)(I.T,{open:er.open,onOpenChange:e=>el(a=>({...a,open:e})),title:"تأكيد حذف المنتج",description:`هل أنت متأكد من حذف المنتج "${er.productName}"؟ هذا الإجراء لا يمكن التراجع عنه.`,confirmText:"حذف",cancelText:"إلغاء",variant:"destructive",onConfirm:eb}),(0,t.jsx)(B.N9,{toasts:s.toasts,onRemove:s.removeToast})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 arabic-text",children:"غير مصرح لك بالوصول"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2 arabic-text",children:"هذه الصفحة مخصصة للمديرين فقط"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["User: ",e?"موجود":"غير موجود"]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Role: ",a?.role||"غير محدد"]})]})})}},48006:(e,a,s)=>{Promise.resolve().then(s.bind(s,47763))},55146:(e,a,s)=>{"use strict";s.d(a,{B8:()=>$,UC:()=>q,bL:()=>T,l9:()=>S});var t=s(43210),r=s(70569),l=s(11273),i=s(72942),c=s(46059),n=s(14163),d=s(43),o=s(65551),x=s(96963),m=s(60687),h="Tabs",[p,u]=(0,l.A)(h,[i.RG]),j=(0,i.RG)(),[g,b]=p(h),f=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,onValueChange:r,defaultValue:l,orientation:i="horizontal",dir:c,activationMode:p="automatic",...u}=e,j=(0,d.jH)(c),[b,f]=(0,o.i)({prop:t,onChange:r,defaultProp:l??"",caller:h});return(0,m.jsx)(g,{scope:s,baseId:(0,x.B)(),value:b,onValueChange:f,orientation:i,dir:j,activationMode:p,children:(0,m.jsx)(n.sG.div,{dir:j,"data-orientation":i,...u,ref:a})})});f.displayName=h;var v="TabsList",y=t.forwardRef((e,a)=>{let{__scopeTabs:s,loop:t=!0,...r}=e,l=b(v,s),c=j(s);return(0,m.jsx)(i.bL,{asChild:!0,...c,orientation:l.orientation,dir:l.dir,loop:t,children:(0,m.jsx)(n.sG.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:a})})});y.displayName=v;var N="TabsTrigger",w=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,disabled:l=!1,...c}=e,d=b(N,s),o=j(s),x=_(d.baseId,t),h=A(d.baseId,t),p=t===d.value;return(0,m.jsx)(i.q7,{asChild:!0,...o,focusable:!l,active:p,children:(0,m.jsx)(n.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:x,...c,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||l||!e||d.onValueChange(t)})})})});w.displayName=N;var k="TabsContent",C=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,forceMount:l,children:i,...d}=e,o=b(k,s),x=_(o.baseId,r),h=A(o.baseId,r),p=r===o.value,u=t.useRef(p);return t.useEffect(()=>{let e=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(c.C,{present:l||p,children:({present:s})=>(0,m.jsx)(n.sG.div,{"data-state":p?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":x,hidden:!s,id:h,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:u.current?"0s":void 0},children:s&&i})})});function _(e,a){return`${e}-trigger-${a}`}function A(e,a){return`${e}-content-${a}`}C.displayName=k;var T=f,$=y,S=w,q=C},57802:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},79550:(e,a,s)=>{Promise.resolve().then(s.bind(s,57802))},79551:e=>{"use strict";e.exports=require("url")},85763:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>i});var t=s(60687);s(43210);var r=s(55146),l=s(4780);function i({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...a})}function c({className:e,...a}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...a})}function n({className:e,...a}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function d({className:e,...a}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...a})}},91821:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>c,TN:()=>n});var t=s(60687);s(43210);var r=s(24224),l=s(4780);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function c({className:e,variant:a,...s}){return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:a}),e),...s})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...a})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,8773,1345,3044,4617,5336,2884,7068],()=>s(15116));module.exports=t})();