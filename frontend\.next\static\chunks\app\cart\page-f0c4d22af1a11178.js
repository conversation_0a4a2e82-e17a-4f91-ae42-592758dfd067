(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{27809:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},35169:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},43332:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},60580:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>k});var t=s(95155),r=s(12115),i=s(40283),l=s(37784),c=s(66695),n=s(30285),d=s(26126),x=s(62523),o=s(22346),m=s(27809),h=s(62525),u=s(19946);let p=(0,u.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var g=s(84616),j=s(43332);let b=(0,u.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var y=s(29799),f=s(75525),N=s(35169),v=s(92138);function k(){let{user:e,profile:a}=(0,i.A)(),[s,u]=(0,r.useState)([]),[k,w]=(0,r.useState)(""),[A,C]=(0,r.useState)(null),[q,z]=(0,r.useState)(!0);(0,r.useEffect)(()=>{u([{id:"1",name:"زي التخرج الكلاسيكي",description:"زي تخرج أنيق مصنوع من أجود الخامات",image:"/api/placeholder/150/150",price:299.99,originalPrice:349.99,quantity:1,size:"L",color:"أسود",category:"gown",inStock:!0,customizations:{embroidery:"اسم الطالب",specialRequests:"تطريز ذهبي"}},{id:"2",name:"قبعة التخرج المميزة",description:"قبعة تخرج مع شرابة ذهبية",image:"/api/placeholder/150/150",price:89.99,quantity:1,color:"أسود",category:"cap",inStock:!0},{id:"3",name:"وشاح التخرج الفاخر",description:"وشاح مطرز بتصميم مميز",image:"/api/placeholder/150/150",price:149.99,quantity:1,color:"ذهبي",category:"accessories",inStock:!1}]),z(!1)},[]);let E=(e,a)=>{a<1||u(s=>s.map(s=>s.id===e?{...s,quantity:a}:s))},M=e=>{u(a=>a.filter(a=>a.id!==e))},S=s.reduce((e,a)=>e+a.price*a.quantity,0),_=S>500?0:25,D=A?"percentage"===A.type?S*A.discount/100:A.discount:0,F=(S-D)*.05,Z=S-D+_+F;return q?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})})]}):0===s.length?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)(m.A,{className:"h-24 w-24 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"سلة التسوق فارغة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 arabic-text",children:"لم تقم بإضافة أي منتجات إلى سلة التسوق بعد"}),(0,t.jsx)(n.$,{asChild:!0,children:(0,t.jsx)("a",{href:"/catalog",className:"arabic-text",children:"تصفح المنتجات"})})]})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"سلة التسوق \uD83D\uDED2"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"راجع منتجاتك واكمل عملية الشراء"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[s.map(e=>(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0",children:(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]}),(0,t.jsx)("button",{onClick:()=>M(e.id),className:"text-red-500 hover:text-red-700 p-1",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex gap-4 mb-3",children:[e.size&&(0,t.jsxs)(d.E,{variant:"outline",className:"arabic-text",children:["المقاس: ",e.size]}),e.color&&(0,t.jsxs)(d.E,{variant:"outline",className:"arabic-text",children:["اللون: ",e.color]}),!e.inStock&&(0,t.jsx)(d.E,{variant:"destructive",className:"arabic-text",children:"غير متوفر"})]}),e.customizations&&(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 arabic-text",children:"التخصيصات:"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:Object.entries(e.customizations).map(e=>{let[a,s]=e;return(0,t.jsxs)("span",{className:"block",children:[a,": ",s]},a)})})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("button",{onClick:()=>E(e.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,t.jsx)(p,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,t.jsx)("button",{onClick:()=>E(e.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"text-right",children:[e.originalPrice&&(0,t.jsxs)("p",{className:"text-sm text-gray-500 line-through",children:[e.originalPrice," درهم"]}),(0,t.jsxs)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:[(e.price*e.quantity).toFixed(2)," درهم"]})]})]})]})]})})},e.id)),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"كود الخصم"]})}),(0,t.jsx)(c.Wu,{children:A?(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(b,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"font-medium text-green-800 dark:text-green-200 arabic-text",children:A.code}),(0,t.jsx)(d.E,{variant:"secondary",children:"percentage"===A.type?"".concat(A.discount,"%"):"".concat(A.discount," درهم")})]}),(0,t.jsx)("button",{onClick:()=>{C(null)},className:"text-red-500 hover:text-red-700",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}):(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.p,{placeholder:"أدخل كود الخصم",value:k,onChange:e=>w(e.target.value),className:"arabic-text"}),(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{let e=[{code:"GRAD2024",discount:15,type:"percentage",minAmount:200},{code:"WELCOME50",discount:50,type:"fixed",minAmount:100}].find(e=>e.code.toLowerCase()===k.toLowerCase());e&&S>=(e.minAmount||0)?(C(e),w("")):alert("كود الخصم غير صالح أو لا يحقق الحد الأدنى للمبلغ")},disabled:!k.trim(),children:"تطبيق"})]})})]})]}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(c.Zp,{className:"sticky top-24",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,t.jsxs)("span",{children:[S.toFixed(2)," درهم"]})]}),D>0&&(0,t.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الخصم:"}),(0,t.jsxs)("span",{children:["-",D.toFixed(2)," درهم"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,t.jsx)("span",{children:0===_?(0,t.jsx)("span",{className:"text-green-600 arabic-text",children:"مجاني"}):"".concat(_," درهم")})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الضريبة (5%):"}),(0,t.jsxs)("span",{children:[F.toFixed(2)," درهم"]})]}),(0,t.jsx)(o.w,{}),(0,t.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{children:[Z.toFixed(2)," درهم"]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"معلومات الشحن"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:0===_?"شحن مجاني للطلبات أكثر من 500 درهم":"التوصيل خلال 2-3 أيام عمل"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"arabic-text",children:"دفع آمن ومضمون"})]}),(0,t.jsx)(n.$,{className:"w-full",size:"lg",asChild:!0,children:(0,t.jsxs)("a",{href:"/checkout",className:"arabic-text",children:["متابعة للدفع",(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"})]})}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full arabic-text",asChild:!0,children:(0,t.jsxs)("a",{href:"/catalog",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 ml-2"}),"متابعة التسوق"]})})]})]})})]})]})]})}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>i});var t=s(95155);s(12115);var r=s(59434);function i(e){let{className:a,type:s,...i}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},84616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92138:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},99079:(e,a,s)=>{Promise.resolve().then(s.bind(s,60580))}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,2632,7443,7784,8441,1684,7358],()=>a(99079)),_N_E=e.O()}]);