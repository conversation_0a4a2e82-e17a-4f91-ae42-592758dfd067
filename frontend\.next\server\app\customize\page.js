(()=>{var e={};e.id=462,e.ids=[462],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11442:(e,a,s)=>{Promise.resolve().then(s.bind(s,78127))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33872:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(60687);s(43210);var r=s(4780);function l({className:e,...a}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},40945:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("check-check",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},47342:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48340:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48394:(e,a,s)=>{Promise.resolve().then(s.bind(s,60588))},54987:(e,a,s)=>{"use strict";s.d(a,{d:()=>i});var t=s(60687);s(43210);var r=s(90270),l=s(4780);function i({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},56085:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},60588:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\customize\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx","default")},62369:(e,a,s)=>{"use strict";s.d(a,{b:()=>n});var t=s(43210),r=s(14163),l=s(60687),i="horizontal",c=["horizontal","vertical"],o=t.forwardRef((e,a)=>{var s;let{decorative:t,orientation:o=i,...n}=e,d=(s=o,c.includes(s))?o:i;return(0,l.jsx)(r.sG.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...n,ref:a})});o.displayName="Separator";var n=o},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>x,Es:()=>m,L3:()=>u,c7:()=>h,lG:()=>c,rr:()=>p,zM:()=>o});var t=s(60687);s(43210);var r=s(26134),l=s(11860),i=s(4780);function c({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function n({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function x({className:e,children:a,showCloseButton:s=!0,...c}){return(0,t.jsxs)(n,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...c,children:[a,s&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function m({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function u({className:e,...a}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...a})}},71057:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},78127:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>I});var t=s(60687),r=s(43210),l=s(8520),i=s(32884),c=s(29523),o=s(44493),n=s(96834),d=s(62688);let x=(0,d.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),h=(0,d.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),m=(0,d.A)("move-3d",[["path",{d:"M5 3v16h16",key:"1mqmf9"}],["path",{d:"m5 19 6-6",key:"jh6hbb"}],["path",{d:"m2 6 3-3 3 3",key:"tkyvxa"}],["path",{d:"m18 16 3 3-3 3",key:"1d4glt"}]]);var u=s(13943),p=s(31158),g=s(81620);function b({configuration:e,className:a=""}){let[s,l]=(0,r.useState)(0),[i,d]=(0,r.useState)(1),[b,v]=(0,r.useState)(!1),j={black:"#000000",navy:"#1e3a8a",burgundy:"#7c2d12",forest:"#166534",purple:"#7c3aed",gray:"#4b5563",gold:"#fbbf24",silver:"#e5e7eb",white:"#ffffff",blue:"#3b82f6",red:"#ef4444"};return(0,t.jsx)(o.Zp,{className:`overflow-hidden ${a}`,children:(0,t.jsxs)(o.Wu,{className:"p-0",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(n.E,{variant:"outline",className:"arabic-text",children:"معاينة مباشرة"})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{d(e=>Math.max(e-.2,.5))},children:(0,t.jsx)(x,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{d(e=>Math.min(e+.2,2))},children:(0,t.jsx)(h,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{v(!0),l(e=>e+90),setTimeout(()=>v(!1),500)},children:(0,t.jsx)(m,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{l(0),d(1)},children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"relative aspect-square bg-gradient-to-br from-gray-100 via-white to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-800 overflow-hidden",children:[(0,t.jsx)("div",{className:`absolute inset-0 flex items-center justify-center transition-transform duration-500 ${b?"animate-pulse":""}`,style:{transform:`rotate(${s}deg) scale(${i})`,transformOrigin:"center"},children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"w-32 h-40 rounded-t-full relative",style:{backgroundColor:j[e.gown.color]||"#000000",opacity:"luxury"===e.gown.fabric?.9:.8},children:[(0,t.jsx)("div",{className:"absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-white/20 to-transparent rounded-t-full"}),(0,t.jsx)("div",{className:"absolute -left-6 top-4 w-12 h-16 rounded-full transform -rotate-12",style:{backgroundColor:j[e.gown.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -right-6 top-4 w-12 h-16 rounded-full transform rotate-12",style:{backgroundColor:j[e.gown.color]||"#000000"}}),e.accessories.hood&&(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 w-20 h-12 rounded-t-full border-2",style:{backgroundColor:j[e.gown.color]||"#000000",borderColor:j[e.cap.color]||"#000000"}})]}),(0,t.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,t.jsx)("div",{className:"w-16 h-4 rounded-full",style:{backgroundColor:j[e.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-20 h-20 border-4 border-gray-300",style:{backgroundColor:j[e.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-1 h-8 transform rotate-12",style:{backgroundColor:j[e.cap.tassel.color]||"#fbbf24"},children:(0,t.jsx)("div",{className:"absolute bottom-0 w-3 h-3 rounded-full",style:{backgroundColor:j[e.cap.tassel.color]||"#fbbf24"}})})]}),e.stole.enabled&&(0,t.jsx)("div",{className:"absolute top-8 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-32 rounded-full opacity-90",style:{backgroundColor:j[e.stole.color]||"#fbbf24"},children:e.stole.embroidery&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-yellow-200/50 to-transparent rounded-full"})})}),e.accessories.sash&&(0,t.jsx)("div",{className:"absolute top-12 left-0 w-full h-4 transform -rotate-12 opacity-80",style:{backgroundColor:"#ef4444"}}),e.accessories.medal&&(0,t.jsx)("div",{className:"absolute top-16 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-6 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 border-2 border-yellow-300"})})]})}),(0,t.jsx)("div",{className:"absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-pulse"}),(0,t.jsx)("div",{className:"absolute bottom-8 left-8 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce delay-300"}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse delay-500"})]}),(0,t.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-900 border-t",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الثوب:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:j[e.gown.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===e.gown.color?"أسود":"navy"===e.gown.color?"أزرق داكن":"burgundy"===e.gown.color?"بورجوندي":e.gown.color})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"القبعة:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:j[e.cap.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===e.cap.color?"أسود":"navy"===e.cap.color?"أزرق داكن":e.cap.color})]})]}),e.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الوشاح:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:j[e.stole.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"gold"===e.stole.color?"ذهبي":"silver"===e.stole.color?"فضي":e.stole.color}),e.stole.embroidery&&(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs",children:"مطرز"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تحميل"})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"مشاركة"})]})]})]})]})})}var v=s(89667),j=s(80013),y=s(85763),f=s(64398),N=s(98971),k=s(99270),w=s(13964),C=s(67760);let A={classic:{name:"كلاسيكي",icon:"\uD83C\uDFA9",description:"الألوان التقليدية الأنيقة"},modern:{name:"عصري",icon:"✨",description:"ألوان معاصرة وجريئة"},premium:{name:"فاخر",icon:"\uD83D\uDC8E",description:"ألوان راقية ومميزة"}};function M({title:e,selectedColor:a,onColorChange:s,colors:l,showCategories:i=!0,showSearch:d=!1,allowCustom:x=!1,className:h=""}){let[m,u]=(0,r.useState)(""),[p,g]=(0,r.useState)("all"),[b,M]=(0,r.useState)("#000000"),[z,q]=(0,r.useState)([]),$=l.filter(e=>{let a=e.name.toLowerCase().includes(m.toLowerCase()),s="all"===p||e.category===p;return a&&s}),S=e=>{q(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},P=(e=0)=>Array.from({length:5},(a,s)=>(0,t.jsx)(f.A,{className:`h-3 w-3 ${s<e?"text-yellow-400 fill-current":"text-gray-300"}`},s));return(0,t.jsxs)(o.Zp,{className:h,children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),e]}),d&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(v.p,{placeholder:"ابحث عن لون...",value:m,onChange:e=>u(e.target.value),className:"pl-10 arabic-text"})]})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[i&&(0,t.jsx)(y.tU,{value:p,onValueChange:g,children:(0,t.jsxs)(y.j7,{className:"category-grid grid w-full grid-cols-4",children:[(0,t.jsx)(y.Xi,{value:"all",className:"arabic-text",children:"الكل"}),Object.entries(A).map(([e,a])=>(0,t.jsxs)(y.Xi,{value:e,className:"arabic-text",children:[(0,t.jsx)("span",{className:"mr-1",children:a.icon}),a.name]},e))]})}),(0,t.jsx)("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3",children:$.map(e=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{onClick:()=>s(e.value),className:`relative w-full aspect-square rounded-lg border-3 transition-all duration-200 hover:scale-105 hover:shadow-lg ${a===e.value?"border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,style:{backgroundColor:e.hex},children:[a===e.value&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-900 rounded-full p-1",children:(0,t.jsx)(w.A,{className:"h-4 w-4 text-blue-600"})})}),e.isNew&&(0,t.jsx)(n.E,{className:"absolute -top-2 -right-2 bg-green-500 text-xs px-1 py-0",children:"جديد"})]}),(0,t.jsx)("div",{onClick:a=>{a.stopPropagation(),S(e.value)},className:"absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10",role:"button",tabIndex:0,onKeyDown:a=>{("Enter"===a.key||" "===a.key)&&(a.preventDefault(),a.stopPropagation(),S(e.value))},children:(0,t.jsx)(C.A,{className:`h-3 w-3 ${z.includes(e.value)?"text-red-500 fill-current":"text-white drop-shadow-lg"}`})}),(0,t.jsxs)("div",{className:"mt-2 text-center",children:[(0,t.jsx)("div",{className:"text-xs font-medium arabic-text truncate",children:e.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500 uppercase",children:e.hex}),e.popularity&&(0,t.jsx)("div",{className:"flex justify-center gap-0.5 mt-1",children:P(e.popularity)})]})]},e.value))}),x&&(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(j.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"لون مخصص"}),(0,t.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,t.jsx)("input",{type:"color",value:b,onChange:e=>M(e.target.value),className:"w-12 h-12 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(v.p,{value:b,onChange:e=>M(e.target.value),placeholder:"#000000",className:"font-mono"})}),(0,t.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>s(b),className:"arabic-text",children:"تطبيق"})]})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(j.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"تركيبات شائعة"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("button",{onClick:()=>s("black"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-black border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-yellow-400 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"كلاسيكي"})]}),(0,t.jsxs)("button",{onClick:()=>s("navy"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-blue-900 border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-300 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"أنيق"})]})]})]}),a&&(0,t.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full border-2 border-white shadow-sm",style:{backgroundColor:l.find(e=>e.value===a)?.hex}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:l.find(e=>e.value===a)?.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:l.find(e=>e.value===a)?.hex})]})]})})]})]})}var z=s(34729),q=s(63503);let $=(0,d.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),S=(0,d.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),P=(0,d.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);var G=s(33872),L=s(8819);let _=(0,d.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),J=(0,d.A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]),V=(0,d.A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);function D({designData:e,designName:a="تصميمي المخصص",onSave:s,onShare:l,className:i=""}){let[n,d]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),[m,u]=(0,r.useState)(a),[b,y]=(0,r.useState)(""),[f,N]=(0,r.useState)(!1),[k]=(0,r.useState)(`https://graduation-toqs.com/design/${Date.now()}`),w=async()=>{if(m.trim()){N(!0);try{await s?.(m,b),d(!1)}catch(e){}finally{N(!1)}}},A=e=>{l?.(e);let a=`شاهد تصميم زي التخرج المخصص الخاص بي على Graduation Toqs!`;switch(e){case"facebook":window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(k)}`,"_blank");break;case"twitter":window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(a)}&url=${encodeURIComponent(k)}`,"_blank");break;case"whatsapp":window.open(`https://wa.me/?text=${encodeURIComponent(a+" "+k)}`,"_blank");break;case"copy":navigator.clipboard.writeText(k)}},M=e=>{console.log(`Downloading design as ${e}`)},D=[{id:"facebook",name:"Facebook",icon:(0,t.jsx)($,{className:"h-5 w-5"}),color:"bg-blue-600 hover:bg-blue-700"},{id:"twitter",name:"Twitter",icon:(0,t.jsx)(S,{className:"h-5 w-5"}),color:"bg-sky-500 hover:bg-sky-600"},{id:"instagram",name:"Instagram",icon:(0,t.jsx)(P,{className:"h-5 w-5"}),color:"bg-pink-600 hover:bg-pink-700"},{id:"whatsapp",name:"WhatsApp",icon:(0,t.jsx)(G.A,{className:"h-5 w-5"}),color:"bg-green-600 hover:bg-green-700"}];return(0,t.jsxs)("div",{className:`space-y-4 ${i}`,children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)(q.lG,{open:n,onOpenChange:d,children:[(0,t.jsx)(q.zM,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"حفظ التصميم"]})}),(0,t.jsxs)(q.Cf,{children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsx)(q.L3,{className:"arabic-text",children:"حفظ التصميم"}),(0,t.jsx)(q.rr,{className:"arabic-text",children:"احفظ تصميمك المخصص لتتمكن من الوصول إليه لاحقاً"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{htmlFor:"design-title",className:"arabic-text",children:"اسم التصميم"}),(0,t.jsx)(v.p,{id:"design-title",value:m,onChange:e=>u(e.target.value),placeholder:"أدخل اسم التصميم",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{htmlFor:"design-description",className:"arabic-text",children:"وصف التصميم (اختياري)"}),(0,t.jsx)(z.T,{id:"design-description",value:b,onChange:e=>y(e.target.value),placeholder:"أضف وصفاً لتصميمك...",className:"arabic-text",rows:3})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(c.$,{onClick:w,disabled:!m.trim()||f,className:"flex-1 arabic-text",children:f?"جاري الحفظ...":"حفظ"}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>d(!1),className:"arabic-text",children:"إلغاء"})]})]})]})]}),(0,t.jsxs)(q.lG,{open:x,onOpenChange:h,children:[(0,t.jsx)(q.zM,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"مشاركة"]})}),(0,t.jsxs)(q.Cf,{children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsx)(q.L3,{className:"arabic-text",children:"مشاركة التصميم"}),(0,t.jsx)(q.rr,{className:"arabic-text",children:"شارك تصميمك المميز مع الأصدقاء والعائلة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"رابط التصميم"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(v.p,{value:k,readOnly:!0,className:"flex-1"}),(0,t.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>A("copy"),children:(0,t.jsx)(_,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text mb-3 block",children:"مشاركة على وسائل التواصل"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:D.map(e=>(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>A(e.id),className:`${e.color} text-white border-0 arabic-text`,children:[e.icon,(0,t.jsx)("span",{className:"mr-2",children:e.name})]},e.id))})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,t.jsx)(J,{className:"h-16 w-16 text-gray-400"})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"رمز QR للمشاركة السريعة"})]})]})]})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsx)(o.ZB,{className:"text-lg arabic-text",children:"تحميل التصميم"}),(0,t.jsx)(o.BT,{className:"arabic-text",children:"احصل على نسخة من تصميمك بصيغ مختلفة"})]}),(0,t.jsxs)(o.Wu,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:[{format:"png",name:"صورة PNG",description:"جودة عالية للطباعة"},{format:"jpg",name:"صورة JPG",description:"حجم أصغر للمشاركة"},{format:"pdf",name:"ملف PDF",description:"للطباعة الاحترافية"},{format:"svg",name:"ملف SVG",description:"قابل للتحرير"}].map(e=>(0,t.jsxs)("button",{onClick:()=>M(e.format),className:"flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]}),(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})]},e.format))}),(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>{window.print()},className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(V,{className:"h-4 w-4 mr-2"}),"طباعة التصميم"]})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{className:"text-lg arabic-text",children:"إحصائيات التصميم"})}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"12"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"مشاهدة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"3"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إعجاب"})]})]})})]}),(0,t.jsxs)(c.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة للمفضلة"]})]})}var H=s(15079),T=s(54987),R=s(28561);let U=(0,d.A)("shirt",[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]]);var Z=s(92363);let E=(0,d.A)("ribbon",[["path",{d:"M12 11.22C11 9.997 10 9 10 8a2 2 0 0 1 4 0c0 1-.998 2.002-2.01 3.22",key:"1rnhq3"}],["path",{d:"m12 18 2.57-3.5",key:"116vt7"}],["path",{d:"M6.243 9.016a7 7 0 0 1 11.507-.009",key:"10dq0b"}],["path",{d:"M9.35 14.53 12 11.22",key:"tdsyp2"}],["path",{d:"M9.35 14.53C7.728 12.246 6 10.221 6 7a6 5 0 0 1 12 0c-.005 3.22-1.778 5.235-3.43 7.5l3.557 4.527a1 1 0 0 1-.203 1.43l-1.894 1.36a1 1 0 0 1-1.384-.215L12 18l-2.679 3.593a1 1 0 0 1-1.39.213l-1.865-1.353a1 1 0 0 1-.203-1.422z",key:"nmifey"}]]);var X=s(56085);let F={gown:{color:"black",style:"classic",size:"M",fabric:"premium"},cap:{color:"black",style:"traditional",tassel:{color:"gold",style:"classic"}},stole:{enabled:!1,color:"gold",pattern:"plain",text:"",embroidery:!1},accessories:{hood:!1,sash:!1,medal:!1}},B={colors:[{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:5},{name:"أزرق داكن",value:"navy",hex:"#1e3a8a",category:"classic",popularity:4},{name:"بورجوندي",value:"burgundy",hex:"#7c2d12",category:"premium",popularity:3},{name:"أخضر داكن",value:"forest",hex:"#166534",category:"modern",popularity:2},{name:"بنفسجي",value:"purple",hex:"#7c3aed",category:"modern",popularity:3,isNew:!0},{name:"رمادي",value:"gray",hex:"#4b5563",category:"classic",popularity:3}],tasselColors:[{name:"ذهبي",value:"gold",hex:"#fbbf24",category:"classic",popularity:5},{name:"فضي",value:"silver",hex:"#e5e7eb",category:"premium",popularity:4},{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:4},{name:"أبيض",value:"white",hex:"#ffffff",category:"classic",popularity:3},{name:"أزرق",value:"blue",hex:"#3b82f6",category:"modern",popularity:2},{name:"أحمر",value:"red",hex:"#ef4444",category:"modern",popularity:2,isNew:!0}],gownStyles:[{name:"كلاسيكي",value:"classic",description:"التصميم التقليدي الأنيق"},{name:"عصري",value:"modern",description:"تصميم معاصر مع لمسات حديثة"},{name:"فاخر",value:"luxury",description:"تصميم راقي مع تفاصيل مميزة"}],fabrics:[{name:"قياسي",value:"standard",price:0},{name:"مميز",value:"premium",price:50},{name:"فاخر",value:"luxury",price:100}],sizes:["XS","S","M","L","XL","XXL"]};function I(){let{t:e}=(0,l.B)(),[a,s]=(0,r.useState)(F),[d,x]=(0,r.useState)("gown"),[h,m]=(0,r.useState)(299.99),p=(e,a)=>{s(s=>({...s,[e]:{...s[e],...a}}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:"\uD83C\uDFA8 تخصيص زي التخرج"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 arabic-text",children:"صمم زي التخرج المثالي الذي يعكس شخصيتك"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-6",children:(0,t.jsxs)("div",{className:"sticky top-24",children:[(0,t.jsx)(b,{configuration:a,className:"mb-6"}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{className:"arabic-text",children:"ملخص السعر"})}),(0,t.jsxs)(o.Wu,{children:[(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الثوب الأساسي:"}),(0,t.jsx)("span",{children:"299 درهم"})]}),a.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الوشاح:"}),(0,t.jsx)("span",{children:"50 درهم"})]}),a.accessories.hood&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"غطاء الرأس:"}),(0,t.jsx)("span",{children:"30 درهم"})]})]}),(0,t.jsx)("div",{className:"border-t pt-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-semibold arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-blue-600",children:[h," درهم"]})]})}),(0,t.jsxs)(c.$,{className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"إضافة للسلة"]})]})]}),(0,t.jsx)(D,{designData:a,designName:"تصميم زي التخرج المخصص",onSave:(e,a)=>{console.log("Saving design:",e,a)},onShare:e=>{console.log("Sharing on:",e)}})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(o.ZB,{className:"arabic-text",children:"خيارات التخصيص"}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{s(F)},children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"إعادة تعيين"]})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)(y.tU,{value:d,onValueChange:x,children:[(0,t.jsxs)(y.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(y.Xi,{value:"gown",className:"arabic-text",children:[(0,t.jsx)(U,{className:"h-4 w-4 mr-2"}),"الثوب"]}),(0,t.jsxs)(y.Xi,{value:"cap",className:"arabic-text",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"القبعة"]}),(0,t.jsxs)(y.Xi,{value:"stole",className:"arabic-text",children:[(0,t.jsx)(E,{className:"h-4 w-4 mr-2"}),"الوشاح"]}),(0,t.jsxs)(y.Xi,{value:"accessories",className:"arabic-text",children:[(0,t.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"الإكسسوارات"]})]}),(0,t.jsxs)(y.av,{value:"gown",className:"space-y-6 mt-6",children:[(0,t.jsx)(M,{title:"لون الثوب",colors:B.colors,selectedColor:a.gown.color,onColorChange:e=>p("gown",{color:e}),showCategories:!0,showSearch:!0,allowCustom:!0}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"نمط الثوب"}),(0,t.jsx)("div",{className:"grid gap-3",children:B.gownStyles.map(e=>(0,t.jsxs)("button",{onClick:()=>p("gown",{style:e.value}),className:`p-4 rounded-lg border-2 text-left transition-colors ${a.gown.style===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]},e.value))})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"المقاس"}),(0,t.jsxs)(H.l6,{value:a.gown.size,onValueChange:e=>p("gown",{size:e}),children:[(0,t.jsx)(H.bq,{children:(0,t.jsx)(H.yv,{})}),(0,t.jsx)(H.gC,{children:B.sizes.map(e=>(0,t.jsx)(H.eb,{value:e,children:e},e))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"نوع القماش"}),(0,t.jsx)("div",{className:"grid gap-3",children:B.fabrics.map(e=>(0,t.jsxs)("button",{onClick:()=>p("gown",{fabric:e.value}),className:`p-4 rounded-lg border-2 flex justify-between items-center transition-colors ${a.gown.fabric===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:e.name}),e.price>0&&(0,t.jsxs)(n.E,{variant:"secondary",children:["+",e.price," درهم"]})]},e.value))})]})]}),(0,t.jsxs)(y.av,{value:"cap",className:"space-y-6 mt-6",children:[(0,t.jsx)(M,{title:"لون القبعة",colors:B.colors,selectedColor:a.cap.color,onColorChange:e=>p("cap",{color:e}),showCategories:!0}),(0,t.jsx)(M,{title:"لون الشرابة",colors:B.tasselColors,selectedColor:a.cap.tassel.color,onColorChange:e=>p("cap",{tassel:{...a.cap.tassel,color:e}}),showCategories:!1})]}),(0,t.jsxs)(y.av,{value:"stole",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.d,{id:"stole-enabled",checked:a.stole.enabled,onCheckedChange:e=>p("stole",{enabled:e})}),(0,t.jsx)(j.J,{htmlFor:"stole-enabled",className:"arabic-text",children:"إضافة وشاح التخرج"})]}),a.stole.enabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(M,{title:"لون الوشاح",colors:B.tasselColors,selectedColor:a.stole.color,onColorChange:e=>p("stole",{color:e}),showCategories:!1}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.d,{id:"stole-embroidery",checked:a.stole.embroidery,onCheckedChange:e=>p("stole",{embroidery:e})}),(0,t.jsx)(j.J,{htmlFor:"stole-embroidery",className:"arabic-text",children:"تطريز مخصص (+50 درهم)"})]})]})]}),(0,t.jsx)(y.av,{value:"accessories",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"غطاء الرأس الأكاديمي"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للدرجات العليا (+30 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.hood,onCheckedChange:e=>p("accessories",{hood:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"حزام الشرف"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للمتفوقين (+25 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.sash,onCheckedChange:e=>p("accessories",{sash:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(j.J,{className:"arabic-text",children:"ميدالية التخرج"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"تذكار مميز (+40 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.medal,onCheckedChange:e=>p("accessories",{medal:e})})]})]})})]})})]})})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(60687);s(43210);var r=s(78148),l=s(4780);function i({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},88722:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>n});var t=s(65239),r=s(48088),l=s(88170),i=s.n(l),c=s(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(a,o);let n={children:["",{children:["customize",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60588)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/customize/page",pathname:"/customize",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},92363:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},96882:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97051:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},98971:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,8773,1345,3044,5336,2884,7770],()=>s(88722));module.exports=t})();