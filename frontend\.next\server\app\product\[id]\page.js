(()=>{var e={};e.id=1e3,e.ids=[1e3],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44258:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>_});var r=a(60687),t=a(43210),i=a(16189),l=a(8520),n=a(29523),c=a(44493),d=a(96834),o=a(15079),x=a(85763),h=a(43984),m=a(30917),u=a(81471),p=a(28559),g=a(27351),j=a(67760),b=a(64398),v=a(28561),f=a(81620),N=a(88059),w=a(99891),y=a(13943),k=a(85814),C=a.n(k);let A={name:"ثوب التخرج الكلاسيكي الفاخر",description:"ثوب تخرج أنيق ومريح للمناسبات الرسمية، مصنوع من أجود الخامات المستوردة مع تفاصيل دقيقة وتصميم عصري يجمع بين الأناقة والراحة.",price:299.99,rental_price:99.99,colors:[{name:"أسود",value:"black",hex:"#000000"},{name:"أزرق داكن",value:"navy",hex:"#1e3a8a"},{name:"بورجوندي",value:"burgundy",hex:"#7c2d12"}],sizes:["S","M","L","XL","XXL"],images:["/api/placeholder/500/600","/api/placeholder/500/600","/api/placeholder/500/600","/api/placeholder/500/600"],rating:4.8,reviews:124,isNew:!0,features:["خامة عالية الجودة","تصميم مريح وأنيق","مقاوم للتجاعيد","سهل العناية والغسيل","متوفر بمقاسات متعددة"],specifications:{المادة:"بوليستر عالي الجودة",الوزن:"450 جرام",العناية:"غسيل جاف أو غسيل عادي",المنشأ:"تركيا",الضمان:"سنة واحدة"}};function _(){(0,i.useParams)();let{t:e}=(0,l.B)(),[s,a]=(0,t.useState)(A.colors[0]),[k,_]=(0,t.useState)(""),[P,q]=(0,t.useState)(0),[D,E]=(0,t.useState)(!1),[T,G]=(0,t.useState)("rental");return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,r.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(C(),{href:"/catalog",children:(0,r.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"العودة للكتالوج"]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white arabic-text",children:"تفاصيل المنتج"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.J,{}),(0,r.jsx)(h.U,{}),(0,r.jsx)(u.B,{})]})]})}),(0,r.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:A.images[P],alt:A.name,className:"w-full h-96 object-cover rounded-lg shadow-lg"}),A.isNew&&(0,r.jsx)(d.E,{className:"absolute top-4 right-4 bg-green-500",children:"جديد"}),(0,r.jsx)("button",{onClick:()=>E(!D),className:"absolute top-4 left-4 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:scale-110 transition-transform",children:(0,r.jsx)(j.A,{className:`h-5 w-5 ${D?"text-red-500 fill-current":"text-gray-400"}`})})]}),(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto",children:A.images.map((e,s)=>(0,r.jsx)("button",{onClick:()=>q(s),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${P===s?"border-blue-500":"border-gray-200 dark:border-gray-700"}`,children:(0,r.jsx)("img",{src:e,alt:`${A.name} ${s+1}`,className:"w-full h-full object-cover"})},s))})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsx)(b.A,{className:`h-5 w-5 ${s<Math.floor(A.rating)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,r.jsx)("span",{className:"text-lg font-semibold",children:A.rating}),(0,r.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:["(",A.reviews," تقييم)"]})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:A.name}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg leading-relaxed arabic-text",children:A.description}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)(n.$,{variant:"rental"===T?"default":"outline",onClick:()=>G("rental"),className:"arabic-text",children:"إيجار"}),(0,r.jsx)(n.$,{variant:"purchase"===T?"default":"outline",onClick:()=>G("purchase"),className:"arabic-text",children:"شراء"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-blue-600 dark:text-blue-400",children:["rental"===T?A.rental_price:A.price," درهم"]}),"rental"===T&&(0,r.jsxs)("div",{className:"text-lg text-gray-500 line-through",children:[A.price," درهم"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold arabic-text",children:"اللون:"}),(0,r.jsx)("div",{className:"flex gap-3",children:A.colors.map(e=>(0,r.jsxs)("button",{onClick:()=>a(e),className:`flex items-center gap-2 px-4 py-2 rounded-lg border-2 transition-colors ${s.value===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-300",style:{backgroundColor:e.hex}}),(0,r.jsx)("span",{className:"arabic-text",children:e.name})]},e.value))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold arabic-text",children:"المقاس:"}),(0,r.jsxs)(o.l6,{value:k,onValueChange:_,children:[(0,r.jsx)(o.bq,{className:"w-full",children:(0,r.jsx)(o.yv,{placeholder:"اختر المقاس"})}),(0,r.jsx)(o.gC,{children:A.sizes.map(e=>(0,r.jsx)(o.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)(n.$,{size:"lg",className:"flex-1 arabic-text",disabled:!k,children:[(0,r.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"إضافة للسلة"]}),(0,r.jsx)(n.$,{size:"lg",variant:"outline",children:(0,r.jsx)(f.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold arabic-text",children:"المميزات:"}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-2",children:A.features.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsx)("span",{className:"arabic-text",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-6 border-t",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(N.A,{className:"h-8 w-8 text-blue-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm arabic-text",children:"توصيل مجاني"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(w.A,{className:"h-8 w-8 text-green-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm arabic-text",children:"ضمان الجودة"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 text-orange-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm arabic-text",children:"إرجاع مجاني"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-12",children:(0,r.jsxs)(x.tU,{defaultValue:"specifications",className:"w-full",children:[(0,r.jsxs)(x.j7,{className:"grid w-full grid-cols-3",children:[(0,r.jsx)(x.Xi,{value:"specifications",className:"arabic-text",children:"المواصفات"}),(0,r.jsx)(x.Xi,{value:"reviews",className:"arabic-text",children:"التقييمات"}),(0,r.jsx)(x.Xi,{value:"care",className:"arabic-text",children:"العناية"})]}),(0,r.jsx)(x.av,{value:"specifications",className:"mt-6",children:(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{className:"arabic-text",children:"مواصفات المنتج"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(A.specifications).map(([e,s])=>(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,r.jsxs)("span",{className:"font-semibold arabic-text",children:[e,":"]}),(0,r.jsx)("span",{className:"arabic-text",children:s})]},e))})})]})}),(0,r.jsx)(x.av,{value:"reviews",className:"mt-6",children:(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{className:"arabic-text",children:"تقييمات العملاء"}),(0,r.jsx)(c.BT,{className:"arabic-text",children:"شاهد ما يقوله عملاؤنا عن هذا المنتج"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("p",{className:"text-center text-gray-500 py-8 arabic-text",children:"سيتم إضافة التقييمات قريباً..."})})]})}),(0,r.jsx)(x.av,{value:"care",className:"mt-6",children:(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{className:"arabic-text",children:"تعليمات العناية"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4 arabic-text",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"الغسيل:"}),(0,r.jsx)("p",{children:"يُفضل الغسيل الجاف أو الغسيل بالماء البارد"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"التجفيف:"}),(0,r.jsx)("p",{children:"تجفيف في الهواء الطلق بعيداً عن أشعة الشمس المباشرة"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"الكي:"}),(0,r.jsx)("p",{children:"كي على درجة حرارة منخفضة مع استخدام قطعة قماش واقية"})]})]})})]})})]})})]})]})}},54899:(e,s,a)=>{Promise.resolve().then(a.bind(a,85948))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77372:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var r=a(65239),t=a(48088),i=a(88170),l=a.n(i),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,85948)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\product\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\product\\[id]\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81471:(e,s,a)=>{"use strict";a.d(s,{B:()=>j});var r=a(60687),t=a(63213),i=a(8520),l=a(29523),n=a(21342),c=a(99891),d=a(7430),o=a(88059),x=a(27351),h=a(58869),m=a(84027),u=a(40083),p=a(85814),g=a.n(p);function j(){let{user:e,profile:s,signOut:a}=(0,t.A)(),{t:p}=(0,i.B)();if(!e||!s)return(0,r.jsx)(l.$,{variant:"outline",asChild:!0,children:(0,r.jsx)("a",{href:"/auth",children:p("auth.login")})});let j=async()=>{await a(),window.location.href="/"};return(0,r.jsxs)(n.rI,{children:[(0,r.jsx)(n.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"outline",size:"icon",children:[(0,r.jsx)(h.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,r.jsx)("span",{className:"sr-only",children:"User menu"})]})}),(0,r.jsxs)(n.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(n.lp,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:s.full_name}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e.email}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(e=>{switch(e){case t.g.ADMIN:return(0,r.jsx)(c.A,{className:"h-4 w-4"});case t.g.SCHOOL:return(0,r.jsx)(d.A,{className:"h-4 w-4"});case t.g.DELIVERY:return(0,r.jsx)(o.A,{className:"h-4 w-4"});case t.g.STUDENT:return(0,r.jsx)(x.A,{className:"h-4 w-4"});default:return(0,r.jsx)(h.A,{className:"h-4 w-4"})}})(s.role),(0,r.jsx)("span",{children:(e=>{switch(e){case t.g.ADMIN:return"مدير";case t.g.SCHOOL:return"مدرسة";case t.g.DELIVERY:return"شريك توصيل";case t.g.STUDENT:return"طالب";default:return"مستخدم"}})(s.role)})]})]})}),(0,r.jsx)(n.mB,{}),(0,r.jsx)(n._2,{asChild:!0,children:(0,r.jsxs)(g(),{href:"/profile",className:"cursor-pointer flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:p("navigation.profile")})]})}),(0,r.jsx)(n._2,{asChild:!0,children:(0,r.jsxs)(g(),{href:(()=>{if(!s)return"/dashboard/student";switch(s.role){case t.g.ADMIN:return"/dashboard/admin";case t.g.SCHOOL:return"/dashboard/school";case t.g.DELIVERY:return"/dashboard/delivery";case t.g.STUDENT:default:return"/dashboard/student"}})(),className:"cursor-pointer flex items-center",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:p("navigation.dashboard")})]})}),(0,r.jsx)(n.mB,{}),(0,r.jsxs)(n._2,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:j,children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:p("auth.logout")})]})]})]})}},85948:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\product\\[id]\\page.tsx","default")},91851:(e,s,a)=>{Promise.resolve().then(a.bind(a,44258))},96834:(e,s,a)=>{"use strict";a.d(s,{E:()=>c});var r=a(60687);a(43210);var t=a(8730),i=a(24224),l=a(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:s,asChild:a=!1,...i}){let c=a?t.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),e),...i})}}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[4447,8773,1345,5336,7770],()=>a(77372));module.exports=r})();