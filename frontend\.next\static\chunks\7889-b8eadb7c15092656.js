"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7889],{47655:(e,t,r)=>{r.d(t,{LM:()=>q,OK:()=>K,VM:()=>C,bL:()=>G,lr:()=>N});var n=r(12115),o=r(63655),l=r(28905),i=r(46081),a=r(6101),s=r(39033),c=r(94315),d=r(52712),u=r(89367),h=r(85185),p=r(95155),f="ScrollArea",[m,v]=(0,i.A)(f),[w,b]=m(f),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[u,h]=n.useState(null),[f,m]=n.useState(null),[v,b]=n.useState(null),[g,S]=n.useState(null),[y,E]=n.useState(null),[C,T]=n.useState(0),[x,L]=n.useState(0),[R,P]=n.useState(!1),[A,_]=n.useState(!1),j=(0,a.s)(t,e=>h(e)),D=(0,c.jH)(i);return(0,p.jsx)(w,{scope:r,type:l,dir:D,scrollHideDelay:s,scrollArea:u,viewport:f,onViewportChange:m,content:v,onContentChange:b,scrollbarX:g,onScrollbarXChange:S,scrollbarXEnabled:R,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:E,scrollbarYEnabled:A,onScrollbarYEnabledChange:_,onCornerWidthChange:T,onCornerHeightChange:L,children:(0,p.jsx)(o.sG.div,{dir:D,...d,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":x+"px",...e.style}})})});g.displayName=f;var S="ScrollAreaViewport",y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,c=b(S,r),d=n.useRef(null),u=(0,a.s)(t,d,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});y.displayName=S;var E="ScrollAreaScrollbar",C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=b(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,p.jsx)(x,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,p.jsx)(L,{...o,ref:t,forceMount:r}):"always"===l.type?(0,p.jsx)(R,{...o,ref:t}):null});C.displayName=E;var T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=b(E,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,p.jsx)(l.C,{present:r||a,children:(0,p.jsx)(L,{"data-state":a?"visible":"hidden",...o,ref:t})})}),x=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...a}=e,s=b(E,e.__scopeScrollArea),c="horizontal"===e.orientation,d=F(()=>f("SCROLL_END"),100),[u,f]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>f("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,s.scrollHideDelay,f]),n.useEffect(()=>{let e=s.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,c,f,d]),(0,p.jsx)(l.C,{present:i||"hidden"!==u,children:(0,p.jsx)(R,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,h.m)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,h.m)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),L=n.forwardRef((e,t)=>{let r=b(E,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),c="horizontal"===e.orientation,d=F(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return B(r.viewport,d),B(r.content,d),(0,p.jsx)(l.C,{present:o||a,children:(0,p.jsx)(R,{"data-state":a?"visible":"hidden",...i,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=b(E,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=M(s.viewport,s.content),u={...o,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function h(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=X(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return U([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(P,{...u,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=h(e,l.dir))}}):"vertical"===r?(0,p.jsx)(A,{...u,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=h(e))}}):null}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=b(E,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),u=(0,a.s)(t,d,i.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,p.jsx)(D,{"data-orientation":"horizontal",...l,ref:u,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:O(s.paddingLeft),paddingEnd:O(s.paddingRight)}})}})}),A=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=b(E,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),u=(0,a.s)(t,d,i.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,p.jsx)(D,{"data-orientation":"vertical",...l,ref:u,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:O(s.paddingTop),paddingEnd:O(s.paddingBottom)}})}})}),[_,j]=m(E),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:d,onThumbPointerDown:u,onThumbPositionChange:f,onDragScroll:m,onWheelScroll:v,onResize:w,...g}=e,S=b(E,r),[y,C]=n.useState(null),T=(0,a.s)(t,e=>C(e)),x=n.useRef(null),L=n.useRef(""),R=S.viewport,P=l.content-l.viewport,A=(0,s.c)(v),j=(0,s.c)(f),D=F(w,10);function k(e){x.current&&m({x:e.clientX-x.current.left,y:e.clientY-x.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==y?void 0:y.contains(t))&&A(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,y,P,A]),n.useEffect(j,[l,j]),B(y,D),B(S.content,D),(0,p.jsx)(_,{scope:r,scrollbar:y,hasThumb:i,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:j,onThumbPointerDown:(0,s.c)(u),children:(0,p.jsx)(o.sG.div,{...g,ref:T,style:{position:"absolute",...g.style},onPointerDown:(0,h.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),x.current=y.getBoundingClientRect(),L.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),k(e))}),onPointerMove:(0,h.m)(e.onPointerMove,k),onPointerUp:(0,h.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=L.current,S.viewport&&(S.viewport.style.scrollBehavior=""),x.current=null})})})}),k="ScrollAreaThumb",N=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=j(k,e.__scopeScrollArea);return(0,p.jsx)(l.C,{present:r||o.hasThumb,children:(0,p.jsx)(H,{ref:t,...n})})}),H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=b(k,r),c=j(k,r),{onThumbPositionChange:d}=c,u=(0,a.s)(t,e=>c.onThumbChange(e)),f=n.useRef(void 0),m=F(()=>{f.current&&(f.current(),f.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{m(),f.current||(f.current=V(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,d]),(0,p.jsx)(o.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,h.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,h.m)(e.onPointerUp,c.onThumbPointerUp)})});N.displayName=k;var W="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=b(W,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(I,{...e,ref:t}):null});z.displayName=W;var I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=b(W,r),[a,s]=n.useState(0),[c,d]=n.useState(0),u=!!(a&&c);return B(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),B(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),u?(0,p.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function O(e){return e?parseInt(e,10):0}function M(e,t){let r=e/t;return isNaN(r)?0:r}function X(e){let t=M(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Y(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=X(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,u.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return U([0,i],[0,l-n])(a)}function U(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function F(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function B(e,t){let r=(0,s.c)(t);(0,d.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var G=g,q=y,K=z},51362:(e,t,r)=>{r.d(t,{D:()=>c,N:()=>d});var n=r(12115),o=(e,t,r,n,o,l,i,a)=>{let s=document.documentElement,c=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&l?o.map(e=>l[e]||e):o;r?(s.classList.remove(...n),s.classList.add(l&&l[t]?l[t]:t)):s.setAttribute(e,t)}),r=t,a&&c.includes(r)&&(s.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},l=["light","dark"],i="(prefers-color-scheme: dark)",a=n.createContext(void 0),s={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(a))?e:s},d=e=>n.useContext(a)?n.createElement(n.Fragment,null,e.children):n.createElement(h,{...e}),u=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:c="theme",themes:d=u,defaultTheme:h=o?"system":"light",attribute:w="data-theme",value:b,children:g,nonce:S,scriptProps:y}=e,[E,C]=n.useState(()=>f(c,h)),[T,x]=n.useState(()=>"system"===E?v():E),L=b?Object.values(b):d,R=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=v());let n=b?b[t]:t,i=r?m(S):null,a=document.documentElement,c=e=>{"class"===e?(a.classList.remove(...L),n&&a.classList.add(n)):e.startsWith("data-")&&(n?a.setAttribute(e,n):a.removeAttribute(e))};if(Array.isArray(w)?w.forEach(c):c(w),s){let e=l.includes(h)?h:null,r=l.includes(t)?t:e;a.style.colorScheme=r}null==i||i()},[S]),P=n.useCallback(e=>{let t="function"==typeof e?e(E):e;C(t);try{localStorage.setItem(c,t)}catch(e){}},[E]),A=n.useCallback(e=>{x(v(e)),"system"===E&&o&&!t&&R("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),n.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?C(e.newValue):P(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[P]),n.useEffect(()=>{R(null!=t?t:E)},[t,E]);let _=n.useMemo(()=>({theme:E,setTheme:P,forcedTheme:t,resolvedTheme:"system"===E?T:E,themes:o?[...d,"system"]:d,systemTheme:o?T:void 0}),[E,P,t,T,o,d]);return n.createElement(a.Provider,{value:_},n.createElement(p,{forcedTheme:t,storageKey:c,attribute:w,enableSystem:o,enableColorScheme:s,defaultTheme:h,value:b,themes:d,nonce:S,scriptProps:y}),g)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:l,enableSystem:i,enableColorScheme:a,defaultTheme:s,value:c,themes:d,nonce:u,scriptProps:h}=e,p=JSON.stringify([l,r,s,t,d,c,i,a]).slice(1,-1);return n.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),f=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},89367:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}}}]);