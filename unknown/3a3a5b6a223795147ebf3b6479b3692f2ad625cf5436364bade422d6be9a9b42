"use client"

import { useState, useCallback, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Upload,
  X,
  FileImage,
  AlertCircle,
  Check,
  RotateCw,
  Trash2,
  WifiOff
} from 'lucide-react'

interface ImageFile {
  file: File
  preview: string
  id: string
  uploading?: boolean
  uploaded?: boolean
  error?: string
  fallbackUrl?: string // للصور المحفوظة في localStorage
}

interface ImageUploaderProps {
  images: ImageFile[]
  onImagesChange: (images: ImageFile[]) => void
  maxImages?: number
  maxSize?: number // بالبايت
  acceptedTypes?: string[]
  className?: string
}

export function ImageUploader({
  images,
  onImagesChange,
  maxImages = 10,
  maxSize = 5 * 1024 * 1024, // 5MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  className = ""
}: ImageUploaderProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})
  const [isOnline, setIsOnline] = useState(true)
  const [uploadErrors, setUploadErrors] = useState<string[]>([])

  // التأكد من أن images هو مصفوفة دائماً
  const safeImages = useMemo(() => Array.isArray(images) ? images : [], [images])

  // التحقق من حالة الاتصال
  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/health', { method: 'HEAD' })
      const online = response.ok
      setIsOnline(online)
      return online
    } catch {
      setIsOnline(false)
      return false
    }
  }, [])

  // التحقق من صحة الملف
  const validateFile = useCallback((file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return 'نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP'
    }

    if (file.size > maxSize) {
      return `حجم الملف كبير جداً. الحد الأقصى ${(maxSize / 1024 / 1024).toFixed(1)} ميجابايت`
    }

    return null
  }, [acceptedTypes, maxSize])

  // حفظ الصورة في localStorage كنسخة احتياطية
  const saveFallbackImage = useCallback((file: File, id: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        try {
          const base64 = reader.result as string
          localStorage.setItem(`fallback_image_${id}`, base64)
          resolve(base64)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }, [])

  // ضغط الصورة قبل الرفع
  const compressImage = useCallback(async (file: File): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // تحديد الأبعاد الجديدة (أقصى عرض/ارتفاع 1200px)
        const maxSize = 1200
        let { width, height } = img

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width
            width = maxSize
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height
            height = maxSize
          }
        }

        canvas.width = width
        canvas.height = height

        // رسم الصورة المضغوطة
        ctx?.drawImage(img, 0, 0, width, height)

        // تحويل إلى blob مع جودة 0.8
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file) // في حالة فشل الضغط، استخدم الملف الأصلي
          }
        }, 'image/jpeg', 0.8)
      }

      img.src = URL.createObjectURL(file)
    })
  }, [])

  // رفع الصورة إلى الخادم مع إعادة المحاولة
  const uploadImageWithRetry = useCallback(async (file: File, id: string, retries = 2): Promise<string> => {
    // ضغط الصورة أولاً
    const compressedFile = await compressImage(file)

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        setUploadProgress(prev => ({ ...prev, [id]: (attempt - 1) * 40 }))

        const formData = new FormData()
        formData.append('files', compressedFile)
        formData.append('folder', 'products')

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const data = await response.json()
        setUploadProgress(prev => ({ ...prev, [id]: 100 }))

        if (data.uploadedFiles && data.uploadedFiles.length > 0) {
          return data.uploadedFiles[0].url
        } else {
          throw new Error('لم يتم إرجاع رابط الصورة')
        }
      } catch (error) {
        console.error(`Upload attempt ${attempt} failed:`, error)

        if (attempt === retries) {
          // إذا فشلت جميع المحاولات، استخدم النسخة الاحتياطية
          const fallbackUrl = await saveFallbackImage(file, id)
          setUploadProgress(prev => ({ ...prev, [id]: 100 }))
          return fallbackUrl
        }

        // انتظار قبل إعادة المحاولة
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
    throw new Error('فشل في رفع الصورة')
  }, [compressImage, saveFallbackImage])

  // معالجة رفع الملفات
  const handleFiles = useCallback(async (files: FileList) => {
    const newImages: ImageFile[] = []
    const currentImages = Array.isArray(images) ? images : []
    const currentCount = currentImages.length
    const errors: string[] = []

    // التحقق من الاتصال أولاً
    await checkConnection()

    for (let i = 0; i < files.length && currentCount + newImages.length < maxImages; i++) {
      const file = files[i]
      const error = validateFile(file)
      const id = `${Date.now()}-${i}`

      if (error) {
        errors.push(`${file.name}: ${error}`)
        continue
      }

      try {
        // إنشاء معاينة للصورة
        const preview = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.onerror = reject
          reader.readAsDataURL(file)
        })

        const newImage: ImageFile = {
          file,
          preview,
          id,
          uploading: true,
          uploaded: false
        }

        newImages.push(newImage)
      } catch (previewError) {
        console.error('Preview creation failed:', previewError)
        errors.push(`${file.name}: فشل في إنشاء المعاينة`)
      }
    }

    // تحديث الحالة فوراً لإظهار جميع الصور الجديدة
    if (newImages.length > 0) {
      const updatedImages = [...images, ...newImages]
      onImagesChange(updatedImages)

      // رفع الصور بشكل متوازي (أسرع)
      const uploadPromises = newImages.map(async (newImage) => {
        try {
          const uploadedUrl = await uploadImageWithRetry(newImage.file, newImage.id)

          // تحديث الصورة بالرابط المرفوع
          onImagesChange(prev => prev.map(img =>
            img.id === newImage.id
              ? { ...img, uploading: false, uploaded: true, fallbackUrl: uploadedUrl }
              : img
          ))
        } catch (uploadError) {
          console.error('Upload failed:', uploadError)
          errors.push(`${newImage.file.name}: فشل في الرفع`)

          // تحديث حالة الصورة لإظهار الخطأ
          onImagesChange(prev => prev.map(img =>
            img.id === newImage.id
              ? { ...img, uploading: false, uploaded: false, error: 'فشل في الرفع' }
              : img
          ))
        }
      })

      // انتظار رفع جميع الصور
      await Promise.allSettled(uploadPromises)
    }

    if (errors.length > 0) {
      setUploadErrors(errors)
      setTimeout(() => setUploadErrors([]), 5000) // إخفاء الأخطاء بعد 5 ثوان
    }
  }, [images, maxImages, onImagesChange, checkConnection, validateFile, uploadImageWithRetry])

  // معالجة السحب والإفلات
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  // معالجة اختيار الملفات
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files)
      // إعادة تعيين قيمة الـ input لضمان إمكانية اختيار نفس الملف مرة أخرى
      e.target.value = ''
    }
  }

  // حذف صورة
  const removeImage = (id: string) => {
    const updatedImages = safeImages.filter(img => img.id !== id)
    onImagesChange(updatedImages)
  }

  // تحريك صورة (ترتيب)
  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...safeImages]
    const [movedImage] = updatedImages.splice(fromIndex, 1)
    updatedImages.splice(toIndex, 0, movedImage)
    onImagesChange(updatedImages)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* حالة الاتصال */}
      {!isOnline && (
        <Alert className="border-orange-200 bg-orange-50">
          <WifiOff className="h-4 w-4" />
          <AlertDescription className="arabic-text">
            لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية.
          </AlertDescription>
        </Alert>
      )}

      {/* رسائل الخطأ */}
      {uploadErrors.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {uploadErrors.map((error, index) => (
                <div key={index} className="text-sm arabic-text">{error}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
        } ${images.length >= maxImages ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <Upload className={`h-12 w-12 mx-auto mb-4 ${
          dragActive ? 'text-blue-500' : 'text-gray-400'
        }`} />
        
        <div className="space-y-2">
          <p className="text-lg font-medium arabic-text">
            {dragActive ? 'أفلت الصور هنا' : 'اسحب الصور هنا أو'}
          </p>
          
          {safeImages.length < maxImages && (
            <>
              <input
                ref={(input) => {
                  if (input) {
                    (window as any).fileInput = input
                  }
                }}
                type="file"
                multiple
                accept={acceptedTypes.join(',')}
                className="hidden"
                onChange={handleInputChange}
                disabled={safeImages.length >= maxImages}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                disabled={safeImages.length >= maxImages}
                onClick={() => {
                  const input = (window as any).fileInput
                  if (input) {
                    input.click()
                  }
                }}
              >
                <FileImage className="h-4 w-4 mr-2" />
                اختر الصور
              </Button>
            </>
          )}
        </div>

        <div className="mt-4 space-y-1">
          <p className="text-sm text-gray-500 arabic-text">
            يمكنك رفع حتى {maxImages} صور بحجم أقصى {(maxSize / 1024 / 1024).toFixed(1)} ميجابايت لكل صورة
          </p>
          <p className="text-xs text-gray-400">
            الصيغ المدعومة: {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}
          </p>
        </div>

        {safeImages.length >= maxImages && (
          <div className="mt-4 flex items-center justify-center gap-2 text-orange-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm arabic-text">تم الوصول للحد الأقصى من الصور</span>
          </div>
        )}
      </div>

      {/* عرض الصور المرفوعة */}
      {safeImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium arabic-text">
              الصور المرفوعة ({safeImages.length}/{maxImages})
            </h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onImagesChange([])}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              حذف الكل
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {safeImages.map((image, index) => (
              <Card key={image.id} className="relative group overflow-hidden">
                <CardContent className="p-0">
                  {image.error ? (
                    <div className="aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20">
                      <div className="text-center p-4">
                        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                        <p className="text-xs text-red-600 arabic-text">{image.error}</p>
                      </div>
                    </div>
                  ) : (
                    <>
                      <img
                        src={image.preview}
                        alt={`صورة ${index + 1}`}
                        className="w-full aspect-square object-cover"
                      />
                      
                      {/* شريط التقدم */}
                      {image.uploading && (
                        <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg">
                            <div className="flex items-center gap-2 mb-3">
                              <RotateCw className="h-5 w-5 animate-spin text-blue-500" />
                              <span className="text-sm font-medium arabic-text">جاري الرفع...</span>
                            </div>
                            <Progress value={uploadProgress[image.id] || 0} className="h-3 mb-2" />
                            <div className="text-xs text-center text-gray-600 dark:text-gray-400">
                              {uploadProgress[image.id] || 0}%
                            </div>
                          </div>
                        </div>
                      )}

                      {/* مؤشر الانتهاء */}
                      {image.uploaded && (
                        <div className="absolute top-2 left-2">
                          <Badge className={image.fallbackUrl?.startsWith('data:') ? 'bg-orange-600' : 'bg-green-600'}>
                            <Check className="h-3 w-3 mr-1" />
                            {image.fallbackUrl?.startsWith('data:') ? 'محفوظ محلياً' : 'تم الرفع'}
                          </Badge>
                        </div>
                      )}

                      {/* مؤشر عدم الاتصال */}
                      {!isOnline && image.uploaded && image.fallbackUrl?.startsWith('data:') && (
                        <div className="absolute bottom-2 left-2">
                          <Badge variant="outline" className="bg-white/90">
                            <WifiOff className="h-3 w-3 mr-1" />
                            غير متصل
                          </Badge>
                        </div>
                      )}

                      {/* أزرار التحكم */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex gap-1">
                          <Button
                            type="button"
                            size="sm"
                            variant="destructive"
                            onClick={() => removeImage(image.id)}
                            className="h-8 w-8 p-0"
                            disabled={image.uploading}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* مؤشر الصورة الرئيسية */}
                      {index === 0 && (
                        <Badge className="absolute bottom-2 left-2 bg-blue-600">
                          الصورة الرئيسية
                        </Badge>
                      )}

                      {/* أزرار الترتيب */}
                      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex gap-1">
                          {index > 0 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="secondary"
                              onClick={() => moveImage(index, index - 1)}
                              className="h-6 w-6 p-0"
                            >
                              ←
                            </Button>
                          )}
                          {index < images.length - 1 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="secondary"
                              onClick={() => moveImage(index, index + 1)}
                              className="h-6 w-6 p-0"
                            >
                              →
                            </Button>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
