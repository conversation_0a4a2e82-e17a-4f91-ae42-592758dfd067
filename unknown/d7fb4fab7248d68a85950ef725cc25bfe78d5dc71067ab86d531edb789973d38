# نظام إدارة الطلبات - Graduation Toqs

## نظرة عامة

تم إنشاء نظام شامل لإدارة الطلبات في منصة Graduation Toqs يتيح للمديرين متابعة ومعالجة جميع طلبات العملاء بكفاءة عالية.

## المميزات الرئيسية

### 1. صفحة إدارة الطلبات (`/dashboard/admin/orders`)
- عرض جميع الطلبات في جدول منظم ومرتب
- إحصائيات شاملة للطلبات والمبيعات
- فلترة متقدمة للطلبات حسب:
  - حالة الطلب (في الانتظار، مؤكد، قيد الإنتاج، تم الشحن، تم التسليم، ملغي)
  - حالة الدفع (في الانتظار، مدفوع، فشل، مسترد)
  - البحث في رقم الطلب واسم العميل والبريد الإلكتروني
- ترتيب الطلبات حسب التاريخ، المبلغ، اسم العميل
- إجراءات سريعة على كل طلب (عرض، تحديث، حذف)

### 2. نافذة تفاصيل الطلب
- عرض شامل لجميع معلومات الطلب
- معلومات العميل والمدرسة
- تفاصيل عناصر الطلب والتخصيصات
- عنوان التوصيل ومعلومات الشحن
- ملخص مالي مفصل
- إمكانية تحديث حالة الطلب وحالة الدفع
- إضافة وتعديل الملاحظات

### 3. مكونات واجهة المستخدم المتخصصة
- **OrderStatusBadge**: عرض حالة الطلب بألوان وأيقونات مميزة
- **PaymentStatusBadge**: عرض حالة الدفع بتصميم واضح
- **OrderDetailsDialog**: نافذة منبثقة شاملة لتفاصيل الطلب

### 4. إحصائيات الطلبات
- إجمالي عدد الطلبات
- عدد الطلبات المعلقة
- عدد الطلبات المكتملة
- إجمالي المبيعات
- المدفوعات المعلقة

## هيكل النظام

### نماذج البيانات

#### MockOrder
```typescript
interface MockOrder {
  id: string
  order_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone?: string
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'
  items: MockOrderItem[]
  subtotal: number
  tax: number
  shipping_cost: number
  total: number
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method?: string
  shipping_address: ShippingAddress
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  delivery_date?: string
  school_id?: string
  school_name?: string
}
```

#### MockOrderItem
```typescript
interface MockOrderItem {
  id: string
  order_id: string
  product_id: string
  product_name: string
  product_image: string
  category: string
  quantity: number
  unit_price: number
  total_price: number
  customizations?: {
    color?: string
    size?: string
    embroidery?: string
    special_requests?: string
  }
}
```

### API Endpoints

#### GET `/api/orders`
- جلب جميع الطلبات مع إمكانية الفلترة
- معاملات الاستعلام:
  - `status`: فلترة حسب حالة الطلب
  - `customer`: البحث في معلومات العميل
  - `school`: فلترة حسب المدرسة
  - `date_from` و `date_to`: فلترة حسب التاريخ
  - `sort_by` و `sort_order`: ترتيب النتائج
  - `limit` و `offset`: تقسيم الصفحات

#### GET `/api/orders/[id]`
- جلب تفاصيل طلب واحد

#### PUT `/api/orders/[id]`
- تحديث معلومات الطلب
- يمكن تحديث: الحالة، حالة الدفع، رقم التتبع، تاريخ التسليم، الملاحظات

#### DELETE `/api/orders/[id]`
- حذف الطلب (مع قيود الأمان)

### الملفات المنشأة

```
frontend/src/
├── app/
│   ├── api/orders/
│   │   ├── route.ts                     # API الطلبات الرئيسي
│   │   └── [id]/route.ts               # API طلب واحد
│   └── dashboard/admin/orders/
│       └── page.tsx                     # صفحة إدارة الطلبات
├── components/admin/
│   ├── OrderStatusBadge.tsx            # مكون حالة الطلب
│   └── OrderDetailsDialog.tsx          # نافذة تفاصيل الطلب
└── lib/
    └── mockData.ts                      # البيانات الوهمية (محدث)
```

## البيانات الوهمية

تم إضافة 3 طلبات وهمية متنوعة تشمل:
- طلبات بحالات مختلفة (قيد الإنتاج، تم التسليم، في الانتظار)
- عملاء من جامعات مختلفة
- منتجات متنوعة مع تخصيصات
- معلومات شحن ودفع كاملة

## التكامل مع النظام

### لوحة تحكم الأدمن
- تم إضافة رابط "إدارة الطلبات" في الإجراءات السريعة
- تم إضافة رابط في قسم إدارة المحتوى

### إدارة البيانات
- تم توسيع `MockDataManager` لدعم الطلبات
- دوال جديدة: `getOrders()`, `saveOrders()`, `generateOrderNumber()`

## الأمان والتحقق

- التحقق من صلاحيات الأدمن قبل الوصول
- قيود على حذف الطلبات المشحونة أو المسلمة
- التحقق من صحة البيانات في API

## التصميم والواجهة

- متوافق مع نظام التصميم الحالي
- دعم الوضع الليلي
- نصوص عربية مع اتجاه RTL
- تصميم responsive للأجهزة المختلفة
- ألوان مميزة لحالات الطلبات المختلفة

## الاستخدام

### للوصول لإدارة الطلبات:
1. تسجيل الدخول كأدمن
2. الذهاب إلى لوحة تحكم الأدمن
3. النقر على "إدارة الطلبات"

### لعرض تفاصيل طلب:
1. النقر على أيقونة الإجراءات (⋮) بجانب الطلب
2. اختيار "عرض التفاصيل"

### لتحديث حالة طلب:
1. فتح تفاصيل الطلب
2. تغيير الحالة في قسم "تحديث الطلب"
3. النقر على "حفظ التغييرات"

## التطوير المستقبلي

يمكن توسيع النظام ليشمل:
- تصدير الطلبات إلى Excel/PDF
- إشعارات تلقائية للعملاء
- تتبع الشحنات المتقدم
- تقارير مبيعات مفصلة
- دمج مع أنظمة الدفع الحقيقية
- إدارة المخزون المتقدمة

## الملاحظات التقنية

- النظام يستخدم البيانات الوهمية حالياً للتطوير
- جميع العمليات محفوظة في localStorage
- التصميم قابل للتوسع لدعم قاعدة بيانات حقيقية
- الكود منظم ومعلق باللغة العربية
