(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5758],{5743:(e,s,a)=>{Promise.resolve().then(a.bind(a,89352))},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>r});var t=a(95155);a(12115);var l=a(60704),i=a(59434);function r(e){let{className:s,...a}=e;return(0,t.jsx)(l.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)(l.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)(l.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)(l.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",s),...a})}},24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>r});var t=a(95155);a(12115);var l=a(55863),i=a(59434);function r(e){let{className:s,value:a,...r}=e;return(0,t.jsx)(l.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...r,children:(0,t.jsx)(l.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(95155);a(12115);var l=a(59434);function i(e){let{className:s,type:a,...i}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},89352:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>W});var t=a(95155),l=a(12115),i=a(40283),r=a(37784),c=a(66695),n=a(30285),d=a(26126),x=a(24944),m=a(17313),h=a(62523),j=a(91788),o=a(84616),u=a(17580),N=a(27809),p=a(55868),g=a(33109),b=a(72713),v=a(37108),f=a(47924),y=a(57434),w=a(6736),_=a(66932),k=a(29869),A=a(92657),Z=a(13717),$=a(19420),B=a(71366),z=a(4516);function W(){let{user:e,profile:s,loading:a}=(0,i.A)(),[W,E]=(0,l.useState)("overview"),[C,R]=(0,l.useState)([]),[S,T]=(0,l.useState)([]),[L,X]=(0,l.useState)({total_students:0,active_orders:0,total_revenue:0,completion_rate:0}),[D,F]=(0,l.useState)(!0),[O,U]=(0,l.useState)("");(0,l.useEffect)(()=>{let e=[{id:"1",name:"أحمد محمد علي",email:"<EMAIL>",class:"الصف الثاني عشر - أ",orders_count:2,total_spent:389.98,status:"active"},{id:"2",name:"فاطمة أحمد حسن",email:"<EMAIL>",class:"الصف الثاني عشر - ب",orders_count:1,total_spent:299.99,status:"active"},{id:"3",name:"محمد عبدالله سالم",email:"<EMAIL>",class:"الصف الثاني عشر - أ",orders_count:3,total_spent:567.97,status:"graduated"}],s=[{id:"1",student_name:"أحمد محمد علي",student_class:"الصف الثاني عشر - أ",items:["زي التخرج الكلاسيكي","قبعة التخرج"],total:389.98,status:"pending",created_at:"2024-01-20"},{id:"2",student_name:"فاطمة أحمد حسن",student_class:"الصف الثاني عشر - ب",items:["زي التخرج المميز"],total:299.99,status:"confirmed",created_at:"2024-01-19"}],a={total_students:e.length,active_orders:s.filter(e=>"completed"!==e.status).length,total_revenue:s.reduce((e,s)=>e+s.total,0),completion_rate:85};R(e),T(s),X(a),F(!1)},[]);let V=e=>{switch(e){case"active":case"completed":return"bg-green-100 text-green-800";case"graduated":case"confirmed":return"bg-blue-100 text-blue-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"pending":return"bg-yellow-100 text-yellow-800"}},P=e=>{switch(e){case"active":return"نشط";case"graduated":return"متخرج";case"inactive":return"غير نشط";case"pending":return"في الانتظار";case"confirmed":return"مؤكد";case"completed":return"مكتمل";default:return"غير معروف"}},q=C.filter(e=>e.name.toLowerCase().includes(O.toLowerCase())||e.class.toLowerCase().includes(O.toLowerCase()));return a||D?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(r.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(r.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"لوحة تحكم المدرسة \uD83C\uDFEB"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة طلاب المدرسة وطلبات أزياء التخرج"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تصدير البيانات"]}),(0,t.jsxs)(n.$,{size:"sm",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"إضافة طالب"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلاب"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:L.total_students})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات النشطة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:L.active_orders})]}),(0,t.jsx)(N.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الإيرادات"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[L.total_revenue.toFixed(2)," درهم"]})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"معدل الإنجاز"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[L.completion_rate,"%"]})]}),(0,t.jsx)(g.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsxs)(m.tU,{value:W,onValueChange:E,children:[(0,t.jsxs)(m.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(m.Xi,{value:"overview",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"نظرة عامة"]}),(0,t.jsxs)(m.Xi,{value:"students",className:"arabic-text",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الطلاب"]}),(0,t.jsxs)(m.Xi,{value:"orders",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"track-orders",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"تتبع الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"reports",className:"arabic-text",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"التقارير"]})]}),(0,t.jsxs)(m.av,{value:"overview",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إحصائيات الطلبات الشهرية"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"رسم بياني للطلبات"})]})})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"توزيع الطلبات حسب الصف"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(w.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"رسم دائري للتوزيع"})]})})})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"النشاط الأخير"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:S.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"h-5 w-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.student_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.student_class," - ",e.items.join(", ")]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-medium",children:[e.total," درهم"]}),(0,t.jsx)(d.E,{className:V(e.status),children:P(e.status)})]})]},e.id))})})]})]}),(0,t.jsxs)(m.av,{value:"students",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(h.p,{placeholder:"البحث عن طالب...",value:O,onChange:e=>U(e.target.value),className:"pl-10 arabic-text"})]})}),(0,t.jsxs)(n.$,{variant:"outline",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"فلترة"]}),(0,t.jsxs)(n.$,{variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"استيراد"]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"قائمة الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إدارة طلاب المدرسة وطلباتهم"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:q.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.class}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)(d.E,{className:V(e.status),children:P(e.status)}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text",children:[e.orders_count," طلب - ",e.total_spent," درهم"]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(Z.A,{className:"h-4 w-4"})})]})]})]},e.id))})})]})]}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلبات المدرسة"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"طلب جماعي جديد"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:S.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:[e.student_name," - ",e.student_class]})]}),(0,t.jsx)(d.E,{className:V(e.status),children:P(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text mb-2",children:"العناصر المطلوبة:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.items.map((e,s)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"تعديل الطلب"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل الفاتورة"]})]})]})})]},e.id))})]}),(0,t.jsx)(m.av,{value:"reports",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلبات الشهري"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات مفصلة عن طلبات الشهر الحالي"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-bold",children:S.length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المكتملة:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:S.filter(e=>"completed"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المعلقة:"}),(0,t.jsx)("span",{className:"font-bold text-yellow-600",children:S.filter(e=>"pending"===e.status).length})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات عن نشاط الطلاب"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلاب النشطون:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:C.filter(e=>"active"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المتخرجون:"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:C.filter(e=>"graduated"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"متوسط الإنفاق:"}),(0,t.jsxs)("span",{className:"font-bold",children:[(C.reduce((e,s)=>e+s.total_spent,0)/C.length).toFixed(2)," درهم"]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]})]})}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلبات المدرسة"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"طلب جماعي جديد"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:S.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:[e.student_name," - ",e.student_class]})]}),(0,t.jsx)(d.E,{className:V(e.status),children:P(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text mb-2",children:"العناصر المطلوبة:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.items.map((e,s)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"تعديل الطلب"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل الفاتورة"]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"track-orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تتبع طلبات المدرسة"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(h.p,{placeholder:"بحث برقم الطلب أو اسم الطالب...",className:"w-80"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"بحث"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"حالة الطلب:"}),(0,t.jsxs)("select",{className:"px-3 py-1 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"جميع الحالات"}),(0,t.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,t.jsx)("option",{value:"processing",children:"قيد التحضير"}),(0,t.jsx)("option",{value:"shipped",children:"تم الشحن"}),(0,t.jsx)("option",{value:"delivered",children:"تم التسليم"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"الفترة الزمنية:"}),(0,t.jsxs)("select",{className:"px-3 py-1 border rounded-md",children:[(0,t.jsx)("option",{value:"week",children:"آخر أسبوع"}),(0,t.jsx)("option",{value:"month",children:"آخر شهر"}),(0,t.jsx)("option",{value:"quarter",children:"آخر 3 أشهر"})]})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:S.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:["الطالب: ",e.student_name," | تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.E,{className:V(e.status),children:P(e.status)}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تفاصيل"})]})]})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:"مراحل الطلب"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الاستلام"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(["processing","shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300")}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"قيد التحضير"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(["shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300")}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الشحن"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("delivered"===e.status?"bg-green-500":"bg-gray-300")}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم التسليم"})]})]}),(0,t.jsx)(x.k,{value:getOrderProgress(e.status),className:"h-2"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"عدد القطع"}),(0,t.jsx)("p",{className:"font-medium",children:e.items.length})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المبلغ الإجمالي"}),(0,t.jsxs)("p",{className:"font-medium",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"طريقة الدفع"}),(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.payment_method})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"اتصال بالطالب"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"إرسال رسالة"})]}),"shipped"===e.status&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الشحنة"})]})]})]})})]},e.id))})]}),(0,t.jsx)(m.av,{value:"reports",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلبات الشهري"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات مفصلة عن طلبات الشهر الحالي"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-bold",children:S.length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المكتملة:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:S.filter(e=>"completed"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المعلقة:"}),(0,t.jsx)("span",{className:"font-bold text-yellow-600",children:S.filter(e=>"pending"===e.status).length})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات عن نشاط الطلاب"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلاب النشطون:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:C.filter(e=>"active"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المتخرجون:"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:C.filter(e=>"graduated"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"متوسط الإنفاق:"}),(0,t.jsxs)("span",{className:"font-bold",children:[(C.reduce((e,s)=>e+s.total_spent,0)/C.length).toFixed(2)," درهم"]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]})]})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,5486,5148,8698,6874,7889,1475,6072,2632,7443,7784,8441,1684,7358],()=>s(5743)),_N_E=e.O()}]);