{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient(cookieStore: ReturnType<typeof cookies>) {\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          try {\n            cookieStore.set({ name, value, ...options })\n          } catch (error) {\n            // The `set` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n        remove(name: string, options: any) {\n          try {\n            cookieStore.set({ name, value: '', ...options })\n          } catch (error) {\n            // The `delete` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS,aAAa,WAAuC;IAClE,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM;wBAAO,GAAG,OAAO;oBAAC;gBAC5C,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM,OAAO;wBAAI,GAAG,OAAO;oBAAC;gBAChD,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/pages/slug/%5Bslug%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { cookies } from 'next/headers'\nimport { createClient } from '@/lib/supabase/server'\n\n// GET - جلب صفحة بواسطة الـ slug\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { slug: string } }\n) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    const { searchParams } = new URL(request.url)\n    const language = searchParams.get('language') || 'ar'\n\n    // جلب الصفحة مع محتواها باللغة المحددة\n    const { data: page, error } = await supabase\n      .from('pages')\n      .select(`\n        *,\n        page_content!inner(\n          language,\n          title,\n          content,\n          meta_description,\n          meta_keywords\n        ),\n        profiles(full_name)\n      `)\n      .eq('slug', params.slug)\n      .eq('page_content.language', language)\n      .single()\n\n    if (error) {\n      if (error.code === 'PGRST116') {\n        return NextResponse.json(\n          { error: 'الصفحة غير موجودة' },\n          { status: 404 }\n        )\n      }\n      console.error('Error fetching page by slug:', error)\n      return NextResponse.json(\n        { error: 'فشل في جلب الصفحة' },\n        { status: 500 }\n      )\n    }\n\n    // التحقق من صلاحية عرض الصفحة غير المنشورة\n    if (!page.is_published) {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) {\n        return NextResponse.json(\n          { error: 'الصفحة غير متاحة' },\n          { status: 404 }\n        )\n      }\n\n      const { data: profile } = await supabase\n        .from('profiles')\n        .select('role')\n        .eq('id', user.id)\n        .single()\n\n      if (!profile || profile.role !== 'admin') {\n        return NextResponse.json(\n          { error: 'الصفحة غير متاحة' },\n          { status: 404 }\n        )\n      }\n    }\n\n    return NextResponse.json({ page })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAgC;IAExC,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,EAAE,CAAC,QAAQ,OAAO,IAAI,EACtB,EAAE,CAAC,yBAAyB,UAC5B,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YACA,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,2CAA2C;QAC3C,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;gBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAmB,GAC5B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;YAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;gBACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAmB,GAC5B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}