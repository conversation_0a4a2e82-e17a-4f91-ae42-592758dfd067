(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5006],{17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>i});var t=s(95155);s(12115);var r=s(60704),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",a),...s})}},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>i});var t=s(95155);s(12115);var r=s(55863),l=s(59434);function i(e){let{className:a,value:s,...i}=e;return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...i,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},34898:(e,a,s)=>{Promise.resolve().then(s.bind(s,75277))},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>c,TN:()=>n});var t=s(95155);s(12115);var r=s(74466),l=s(59434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function c(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:s}),a),...r})}function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...s})}},75277:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Y});var t=s(95155),r=s(12115),l=s(40283),i=s(37784),c=s(66695),n=s(30285),d=s(26126),o=s(62523),x=s(85057),m=s(59409),h=s(54165),p=s(17313),u=s(85127),j=s(44838),g=s(88539),b=s(80333),N=s(37108),v=s(33127),f=s(27213),y=s(38564),w=s(85339),k=s(84616),C=s(54416),_=s(92657),A=s(4229),S=s(24944),E=s(55365),T=s(94449),F=s(29869),z=s(42118),q=s(62525),U=s(32568),R=s(5196);function D(e){let{images:a,onImagesChange:s,maxImages:l=10,maxSize:i=5242880,acceptedTypes:o=["image/jpeg","image/png","image/webp"],className:x=""}=e,[m,h]=(0,r.useState)(!1),[p,u]=(0,r.useState)({}),[j,g]=(0,r.useState)(!0),[b,N]=(0,r.useState)([]),v=(0,r.useMemo)(()=>Array.isArray(a)?a:[],[a]),f=(0,r.useCallback)(async()=>{try{let e=(await fetch("/api/health",{method:"HEAD"})).ok;return g(e),e}catch(e){return g(!1),!1}},[]),y=(0,r.useCallback)(e=>o.includes(e.type)?e.size>i?"حجم الملف كبير جداً. الحد الأقصى ".concat((i/1024/1024).toFixed(1)," ميجابايت"):null:"نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP",[o,i]),k=(0,r.useCallback)((e,a)=>new Promise((s,t)=>{let r=new FileReader;r.onload=()=>{try{let e=r.result;localStorage.setItem("fallback_image_".concat(a),e),s(e)}catch(e){t(e)}},r.onerror=t,r.readAsDataURL(e)}),[]),_=(0,r.useCallback)(async e=>new Promise(a=>{let s=document.createElement("canvas"),t=s.getContext("2d"),r=new Image;r.onload=()=>{let{width:l,height:i}=r;l>i?l>1200&&(i=1200*i/l,l=1200):i>1200&&(l=1200*l/i,i=1200),s.width=l,s.height=i,null==t||t.drawImage(r,0,0,l,i),s.toBlob(s=>{s?a(new File([s],e.name,{type:"image/jpeg",lastModified:Date.now()})):a(e)},"image/jpeg",.8)},r.src=URL.createObjectURL(e)}),[]),A=(0,r.useCallback)(async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,t=await _(e);for(let r=1;r<=s;r++)try{u(e=>({...e,[a]:(r-1)*40}));let e=new FormData;e.append("files",t),e.append("folder","products");let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error("HTTP ".concat(s.status));let l=await s.json();if(u(e=>({...e,[a]:100})),l.uploadedFiles&&l.uploadedFiles.length>0)return l.uploadedFiles[0].url;throw Error("لم يتم إرجاع رابط الصورة")}catch(t){if(console.error("Upload attempt ".concat(r," failed:"),t),r===s){let s=await k(e,a);return u(e=>({...e,[a]:100})),s}await new Promise(e=>setTimeout(e,1e3*r))}throw Error("فشل في رفع الصورة")},[_,k]),D=(0,r.useCallback)(async e=>{let t=[],r=(Array.isArray(a)?a:[]).length,i=[];await f();for(let a=0;a<e.length&&r+t.length<l;a++){let s=e[a],r=y(s),l="".concat(Date.now(),"-").concat(a);if(r){i.push("".concat(s.name,": ").concat(r));continue}try{let e=await new Promise((e,a)=>{let t=new FileReader;t.onload=a=>{var s;return e(null==(s=a.target)?void 0:s.result)},t.onerror=a,t.readAsDataURL(s)}),a={file:s,preview:e,id:l,uploading:!0,uploaded:!1};t.push(a)}catch(e){console.error("Preview creation failed:",e),i.push("".concat(s.name,": فشل في إنشاء المعاينة"))}}if(t.length>0){s([...a,...t]);let e=t.map(async e=>{try{let a=await A(e.file,e.id);s(s=>s.map(s=>s.id===e.id?{...s,uploading:!1,uploaded:!0,fallbackUrl:a}:s))}catch(a){console.error("Upload failed:",a),i.push("".concat(e.file.name,": فشل في الرفع")),s(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!1,error:"فشل في الرفع"}:a))}});await Promise.allSettled(e)}i.length>0&&(N(i),setTimeout(()=>N([]),5e3))},[a,l,s,f,y,A]),$=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?h(!0):"dragleave"===e.type&&h(!1)},[]),L=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),h(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&D(e.dataTransfer.files)},[D]),P=e=>{s(v.filter(a=>a.id!==e))},B=(e,a)=>{let t=[...v],[r]=t.splice(e,1);t.splice(a,0,r),s(t)};return(0,t.jsxs)("div",{className:"space-y-4 ".concat(x),children:[!j&&(0,t.jsxs)(E.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(T.A,{className:"h-4 w-4"}),(0,t.jsx)(E.TN,{className:"arabic-text",children:"لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية."})]}),b.length>0&&(0,t.jsxs)(E.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),(0,t.jsx)(E.TN,{children:(0,t.jsx)("div",{className:"space-y-1",children:b.map((e,a)=>(0,t.jsx)("div",{className:"text-sm arabic-text",children:e},a))})})]}),(0,t.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors ".concat(m?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400"," ").concat(a.length>=l?"opacity-50 pointer-events-none":""),onDragEnter:$,onDragLeave:$,onDragOver:$,onDrop:L,children:[(0,t.jsx)(F.A,{className:"h-12 w-12 mx-auto mb-4 ".concat(m?"text-blue-500":"text-gray-400")}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium arabic-text",children:m?"أفلت الصور هنا":"اسحب الصور هنا أو"}),v.length<l&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("input",{ref:e=>{e&&(window.fileInput=e)},type:"file",multiple:!0,accept:o.join(","),className:"hidden",onChange:e=>{e.target.files&&e.target.files.length>0&&(D(e.target.files),e.target.value="")},disabled:v.length>=l}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",disabled:v.length>=l,onClick:()=>{let e=window.fileInput;e&&e.click()},children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"اختر الصور"]})]})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-1",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500 arabic-text",children:["يمكنك رفع حتى ",l," صور بحجم أقصى ",(i/1024/1024).toFixed(1)," ميجابايت لكل صورة"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["الصيغ المدعومة: ",o.map(e=>e.split("/")[1].toUpperCase()).join(", ")]})]}),v.length>=l&&(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2 text-orange-600",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الوصول للحد الأقصى من الصور"})]})]}),v.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium arabic-text",children:["الصور المرفوعة (",v.length,"/",l,")"]}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>s([]),className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"حذف الكل"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:v.map((e,s)=>{var r,l,i;return(0,t.jsx)(c.Zp,{className:"relative group overflow-hidden",children:(0,t.jsx)(c.Wu,{className:"p-0",children:e.error?(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20",children:(0,t.jsxs)("div",{className:"text-center p-4",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-xs text-red-600 arabic-text",children:e.error})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("img",{src:e.preview,alt:"صورة ".concat(s+1),className:"w-full aspect-square object-cover"}),e.uploading&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 animate-spin text-blue-500"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"جاري الرفع..."})]}),(0,t.jsx)(S.k,{value:p[e.id]||0,className:"h-3 mb-2"}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-600 dark:text-gray-400",children:[p[e.id]||0,"%"]})]})}),e.uploaded&&(0,t.jsx)("div",{className:"absolute top-2 left-2",children:(0,t.jsxs)(d.E,{className:(null==(r=e.fallbackUrl)?void 0:r.startsWith("data:"))?"bg-orange-600":"bg-green-600",children:[(0,t.jsx)(R.A,{className:"h-3 w-3 mr-1"}),(null==(l=e.fallbackUrl)?void 0:l.startsWith("data:"))?"محفوظ محلياً":"تم الرفع"]})}),!j&&e.uploaded&&(null==(i=e.fallbackUrl)?void 0:i.startsWith("data:"))&&(0,t.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white/90",children:[(0,t.jsx)(T.A,{className:"h-3 w-3 mr-1"}),"غير متصل"]})}),(0,t.jsx)("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsx)("div",{className:"flex gap-1",children:(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"destructive",onClick:()=>P(e.id),className:"h-8 w-8 p-0",disabled:e.uploading,children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})})}),0===s&&(0,t.jsx)(d.E,{className:"absolute bottom-2 left-2 bg-blue-600",children:"الصورة الرئيسية"}),(0,t.jsx)("div",{className:"absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[s>0&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>B(s,s-1),className:"h-6 w-6 p-0",children:"←"}),s<a.length-1&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>B(s,s+1),className:"h-6 w-6 p-0",children:"→"})]})})]})})},e.id)})})]})]})}let $=["أسود","أزرق داكن","بورجوندي","ذهبي","فضي","أبيض","أحمر","أخضر","بنفسجي","وردي","برتقالي","بني"],L=["XS","S","M","L","XL","XXL","XXXL","واحد"];function P(e){var a;let{onSubmit:s,onCancel:l,initialData:i={},isEditing:h=!1}=e,[u,j]=(0,r.useState)({name:i.name||"",description:i.description||"",category:i.category||"",price:i.price||0,rental_price:i.rental_price||0,colors:i.colors||[],sizes:i.sizes||[],images:i.images||[],stock_quantity:i.stock_quantity||0,is_available:null==(a=i.is_available)||a,features:i.features||[],specifications:i.specifications||{}}),[S,E]=(0,r.useState)([]),[T,F]=(0,r.useState)(!0),[z,q]=(0,r.useState)(""),[U,R]=(0,r.useState)(""),[P,B]=(0,r.useState)(""),[O,Z]=(0,r.useState)(""),[J,W]=(0,r.useState)(""),[X,I]=(0,r.useState)({});(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();E(a.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{F(!1)}})()},[]);let V=()=>{let e={};return u.name.trim()||(e.name="اسم المنتج مطلوب"),u.description.trim()||(e.description="وصف المنتج مطلوب"),u.category||(e.category="فئة المنتج مطلوبة"),u.price<=0&&(e.price="السعر يجب أن يكون أكبر من صفر"),0===u.colors.length&&(e.colors="يجب إضافة لون واحد على الأقل"),0===u.sizes.length&&(e.sizes="يجب إضافة مقاس واحد على الأقل"),u.stock_quantity<0&&(e.stock_quantity="كمية المخزون لا يمكن أن تكون سالبة"),I(e),0===Object.keys(e).length},H=e=>{j(a=>({...a,colors:a.colors.filter(a=>a!==e)}))},G=e=>{j(a=>({...a,sizes:a.sizes.filter(a=>a!==e)}))},M=e=>{j(a=>({...a,features:a.features.filter(a=>a!==e)}))},Q=e=>{j(a=>{let s={...a.specifications};return delete s[e],{...a,specifications:s}})};return(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),V()&&s(u)},className:"space-y-6",children:[(0,t.jsxs)(p.tU,{defaultValue:"basic",className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(p.Xi,{value:"basic",className:"arabic-text",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"المعلومات الأساسية"]}),(0,t.jsxs)(p.Xi,{value:"details",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"التفاصيل والألوان"]}),(0,t.jsxs)(p.Xi,{value:"images",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"الصور"]}),(0,t.jsxs)(p.Xi,{value:"features",className:"arabic-text",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"المميزات والمواصفات"]})]}),(0,t.jsxs)(p.av,{value:"basic",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"name",className:"arabic-text",children:"اسم المنتج *"}),(0,t.jsx)(o.p,{id:"name",value:u.name,onChange:e=>j(a=>({...a,name:e.target.value})),placeholder:"أدخل اسم المنتج",className:"arabic-text"}),X.name&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.name]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"category",className:"arabic-text",children:"فئة المنتج *"}),(0,t.jsxs)(m.l6,{value:u.category,onValueChange:e=>j(a=>({...a,category:e})),disabled:T,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:T?"جاري تحميل الفئات...":"اختر فئة المنتج"})}),(0,t.jsx)(m.gC,{children:S.map(e=>(0,t.jsx)(m.eb,{value:e.slug,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{className:"arabic-text",children:e.name_ar})]})},e.slug))})]}),X.category&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.category]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"description",className:"arabic-text",children:"وصف المنتج *"}),(0,t.jsx)(g.T,{id:"description",value:u.description,onChange:e=>j(a=>({...a,description:e.target.value})),placeholder:"أدخل وصف مفصل للمنتج",className:"arabic-text min-h-[100px]"}),X.description&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.description]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"price",className:"arabic-text",children:"السعر (درهم) *"}),(0,t.jsx)(o.p,{id:"price",type:"number",min:"0",step:"0.01",value:u.price,onChange:e=>j(a=>({...a,price:parseFloat(e.target.value)||0})),placeholder:"0.00"}),X.price&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.price]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"rental_price",className:"arabic-text",children:"سعر الإيجار (درهم)"}),(0,t.jsx)(o.p,{id:"rental_price",type:"number",min:"0",step:"0.01",value:u.rental_price,onChange:e=>j(a=>({...a,rental_price:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"stock_quantity",className:"arabic-text",children:"كمية المخزون *"}),(0,t.jsx)(o.p,{id:"stock_quantity",type:"number",min:"0",value:u.stock_quantity,onChange:e=>j(a=>({...a,stock_quantity:parseInt(e.target.value)||0})),placeholder:"0"}),X.stock_quantity&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.stock_quantity]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.d,{id:"is_available",checked:u.is_available,onCheckedChange:e=>j(a=>({...a,is_available:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_available",className:"arabic-text",children:"متاح للبيع"})]})]}),(0,t.jsxs)(p.av,{value:"details",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"الألوان المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف الألوان المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:z,onValueChange:q,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر لون"})}),(0,t.jsx)(m.gC,{children:$.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:z,onChange:e=>q(e.target.value),placeholder:"أو أدخل لون مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{z.trim()&&!u.colors.includes(z.trim())&&(j(e=>({...e,colors:[...e.colors,z.trim()]})),q(""))},size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:u.colors.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>H(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]},a))}),X.colors&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.colors]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"المقاسات المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المقاسات المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:U,onValueChange:R,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر مقاس"})}),(0,t.jsx)(m.gC,{children:L.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:U,onChange:e=>R(e.target.value),placeholder:"أو أدخل مقاس مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{U.trim()&&!u.sizes.includes(U.trim())&&(j(e=>({...e,sizes:[...e.sizes,U.trim()]})),R(""))},size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:u.sizes.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>G(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]},a))}),X.sizes&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),X.sizes]})]})]})]}),(0,t.jsx)(p.av,{value:"images",className:"space-y-6 mt-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"صور المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)(D,{images:u.images,onImagesChange:e=>j(a=>({...a,images:e})),maxImages:8,maxSize:5242880,acceptedTypes:["image/jpeg","image/png","image/webp"]})})]})}),(0,t.jsxs)(p.av,{value:"features",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مميزات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المميزات الرئيسية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:P,onChange:e=>B(e.target.value),placeholder:"أدخل ميزة جديدة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{P.trim()&&!u.features.includes(P.trim())&&(j(e=>({...e,features:[...e.features,P.trim()]})),B(""))},size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"space-y-2",children:u.features.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:e}),(0,t.jsx)("button",{type:"button",onClick:()=>M(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]},a))}),0===u.features.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مميزات بعد"})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مواصفات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المواصفات التقنية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(o.p,{value:O,onChange:e=>Z(e.target.value),placeholder:"اسم المواصفة (مثل: المادة)",className:"arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:J,onChange:e=>W(e.target.value),placeholder:"قيمة المواصفة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{O.trim()&&J.trim()&&(j(e=>({...e,specifications:{...e.specifications,[O.trim()]:J.trim()}})),Z(""),W(""))},size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:Object.entries(u.specifications).map(e=>{let[a,s]=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsxs)("span",{className:"font-medium",children:[a,":"]})," ",s]}),(0,t.jsx)("button",{type:"button",onClick:()=>Q(a),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]},a)})}),0===Object.keys(u.specifications).length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مواصفات بعد"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:l,children:"إلغاء"}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"معاينة"]}),(0,t.jsxs)(n.$,{type:"submit",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),h?"تحديث المنتج":"إضافة المنتج"]})]})]})}function B(e){let{product:a,open:s,onOpenChange:l,onSave:i}=e,[c,n]=(0,r.useState)(!1),d=async e=>{try{n(!0),await i({...e,id:null==a?void 0:a.id}),l(!1)}catch(e){console.error("Error updating product:",e)}finally{n(!1)}};return a?(0,t.jsx)(h.lG,{open:s,onOpenChange:l,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(h.c7,{children:(0,t.jsxs)(h.L3,{className:"arabic-text",children:["تعديل المنتج: ",a.name]})}),(0,t.jsx)(P,{initialData:{name:a.name,description:a.description,category:a.category,price:a.price,rental_price:a.rental_price,colors:a.colors,sizes:a.sizes,images:a.images.map((e,a)=>({id:"existing-".concat(a),file:new File([],"existing-image"),preview:e,uploaded:!0,fallbackUrl:e})),stock_quantity:a.stock_quantity,is_available:a.is_available,features:a.features||[],specifications:a.specifications||{}},onSubmit:d,onCancel:()=>{l(!1)},isEditing:!0})]})}):null}var O=s(7226),Z=s(6982),J=s(6874),W=s.n(J),X=s(35169),I=s(91788),V=s(1243),H=s(33109),G=s(47924),M=s(13717),Q=s(5623);let K={gown:"ثوب التخرج",cap:"قبعة التخرج",tassel:"الشرابة",stole:"الوشاح",hood:"القلنسوة"};function Y(){let{user:e,profile:a}=(0,l.A)(),s=(0,Z.dj)(),[b,v]=(0,r.useState)([]),[f,w]=(0,r.useState)([]),[C,A]=(0,r.useState)(""),[S,E]=(0,r.useState)("all"),[T,F]=(0,r.useState)("all"),[z,U]=(0,r.useState)("created_at"),[R,D]=(0,r.useState)("desc"),[$,L]=(0,r.useState)(!0),[J,Y]=(0,r.useState)(!1),[ee,ea]=(0,r.useState)(null),[es,et]=(0,r.useState)(!1),[er,el]=(0,r.useState)({open:!1,productId:"",productName:""}),[ei,ec]=(0,r.useState)([]),[en,ed]=(0,r.useState)(!1),[eo,ex]=(0,r.useState)(null),[em,eh]=(0,r.useState)({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),[ep,eu]=(0,r.useState)("products");(0,r.useEffect)(()=>{ej()},[]);let ej=async()=>{try{L(!0);let e=await fetch("/api/products");if(!e.ok)throw Error("فشل في جلب المنتجات");let a=await e.json();v(a.products||[]),w(a.products||[])}catch(e){console.error("Error fetching products:",e),s.error("فشل في جلب المنتجات")}finally{L(!1)}};(0,r.useEffect)(()=>{let e=[...b];C&&(e=e.filter(e=>e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase()))),"all"!==S&&(e=e.filter(e=>e.category===S)),"all"!==T&&(e=e.filter(e=>"available"===T?e.is_available:!e.is_available)),e.sort((e,a)=>{let s=e[z],t=a[z];return(("price"===z||"stock_quantity"===z||"rating"===z)&&(s=Number(s)||0,t=Number(t)||0),"asc"===R)?s>t?1:-1:s<t?1:-1}),w(e)},[b,C,S,T,z,R]);let eg=(e,a)=>{el({open:!0,productId:e,productName:a})},eb=async()=>{try{let e=await fetch("/api/products/".concat(er.productId),{method:"DELETE"});if(!e.ok){let a=await e.json();throw Error(a.error||"فشل في حذف المنتج")}await ej(),s.success("تم حذف المنتج بنجاح!")}catch(e){console.error("Error deleting product:",e),s.error(e instanceof Error?e.message:"فشل في حذف المنتج")}},eN=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch("/api/products/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_available:!a.is_available})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة المنتج")}await ej(),s.success("تم ".concat(a.is_available?"إلغاء تفعيل":"تفعيل"," المنتج بنجاح"))}catch(e){console.error("Error toggling availability:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة المنتج")}},ev=async e=>{try{L(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في إضافة المنتج")}await t.json(),await ej(),Y(!1),s.success("تم إضافة المنتج بنجاح!")}catch(e){console.error("Error adding product:",e),s.error(e instanceof Error?e.message:"فشل في إضافة المنتج")}finally{L(!1)}},ef=e=>{ea(e),et(!0)},ey=async e=>{try{L(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث المنتج")}await ej(),s.success("تم تحديث المنتج بنجاح!")}catch(e){console.error("Error updating product:",e),s.error(e instanceof Error?e.message:"فشل في تحديث المنتج")}finally{L(!1)}},ew=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();ec(a.categories||[])}}catch(e){console.error("Error fetching categories:",e)}};(0,r.useEffect)(()=>{ew()},[]);let ek=async()=>{try{let e=eo?"/api/categories/".concat(eo.id):"/api/categories",a=eo?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(em)});if(t.ok)await ew(),eC(),s.success(eo?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!");else{let e=await t.json();s.error(e.error||"فشل في حفظ الفئة")}}catch(e){console.error("Error saving category:",e),s.error("فشل في حفظ الفئة")}},eC=()=>{eh({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),ex(null),ed(!1)},e_=e=>{eh({name_ar:e.name_ar,slug:e.slug,description:e.description||"",icon:e.icon||"",is_active:e.is_active,order_index:e.order_index}),ex(e),ed(!0)},eA=async e=>{try{let a=await fetch("/api/categories/".concat(e),{method:"DELETE"});if(a.ok)await ew(),s.success("تم حذف الفئة بنجاح!");else{let e=await a.json();s.error(e.error||"فشل في حذف الفئة")}}catch(e){console.error("Error deleting category:",e),s.error("فشل في حذف الفئة")}};return(console.log("User:",e),console.log("Profile:",a),console.log("Profile role:",null==a?void 0:a.role),e&&(null==a?void 0:a.role)==="admin")?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(W(),{href:"/dashboard/admin",children:[(0,t.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المنتجات والفئات \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة منتجات وفئات المنصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),"products"===ep&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>Y(!0),children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]}),"categories"===ep&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>{eC(),ed(!0)},children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})]})]})]}),(0,t.jsxs)(p.tU,{value:ep,onValueChange:eu,className:"space-y-6",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(p.Xi,{value:"products",className:"arabic-text",children:"المنتجات"}),(0,t.jsx)(p.Xi,{value:"categories",className:"arabic-text",children:"الفئات"})]}),(0,t.jsxs)(p.av,{value:"products",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المنتجات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.length})]}),(0,t.jsx)(N.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المتاحة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_available).length})]}),(0,t.jsx)(y.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"مخزون منخفض"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.stock_quantity<20).length})]}),(0,t.jsx)(V.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"متوسط التقييم"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(b.reduce((e,a)=>e+(a.rating||0),0)/b.length).toFixed(1)})]}),(0,t.jsx)(H.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsx)(c.Zp,{className:"mb-6",children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(G.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"البحث في المنتجات...",value:C,onChange:e=>A(e.target.value),className:"pl-10 arabic-text"})]}),(0,t.jsxs)(m.l6,{value:S,onValueChange:E,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع الفئات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع الفئات"}),Object.entries(K).map(e=>{let[a,s]=e;return(0,t.jsx)(m.eb,{value:a,children:s},a)})]})]}),(0,t.jsxs)(m.l6,{value:T,onValueChange:F,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع المنتجات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(m.eb,{value:"available",children:"متاح"}),(0,t.jsx)(m.eb,{value:"unavailable",children:"غير متاح"})]})]}),(0,t.jsxs)(m.l6,{value:z,onValueChange:U,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"ترتيب حسب"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"created_at",children:"تاريخ الإنشاء"}),(0,t.jsx)(m.eb,{value:"name",children:"الاسم"}),(0,t.jsx)(m.eb,{value:"price",children:"السعر"}),(0,t.jsx)(m.eb,{value:"stock_quantity",children:"المخزون"}),(0,t.jsx)(m.eb,{value:"rating",children:"التقييم"})]})]}),(0,t.jsxs)(m.l6,{value:R,onValueChange:e=>D(e),children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"desc",children:"تنازلي"}),(0,t.jsx)(m.eb,{value:"asc",children:"تصاعدي"})]})]})]})})}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["قائمة المنتجات (",f.length,")"]})}),(0,t.jsx)(c.Wu,{children:$?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-500 mt-2 arabic-text",children:"جاري التحميل..."})]}):0===f.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(N.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد منتجات"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المنتج"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الفئة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"السعر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المخزون"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"التقييم"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الحالة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)("tbody",{children:f.map(e=>{var a;return(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:e.images[0]||"/api/placeholder/80/80",alt:e.name,className:"w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700"}),e.images.length>1&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:e.images.length})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text line-clamp-1",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500 arabic-text line-clamp-2 mt-1",children:e.description})]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:K[e.category]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"price font-medium text-gray-900 dark:text-white",children:[e.price," درهم"]}),e.rental_price&&(0,t.jsxs)("p",{className:"price text-gray-500",children:["إيجار: ",e.rental_price," درهم"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium ".concat(e.stock_quantity<20?"text-red-600":e.stock_quantity<50?"text-orange-600":"text-green-600"),children:e.stock_quantity}),e.stock_quantity<20&&(0,t.jsx)(V.A,{className:"h-4 w-4 text-red-600"})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"rating flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"number text-sm font-medium",children:(null==(a=e.rating)?void 0:a.toFixed(1))||"N/A"}),(0,t.jsxs)("span",{className:"number text-xs text-gray-500",children:["(",e.reviews_count||0,")"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_available?"default":"secondary",className:"arabic-text",children:e.is_available?"متاح":"غير متاح"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"actions flex items-center gap-2",children:[(0,t.jsx)(n.$,{size:"sm",variant:"outline",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>ef(e),children:(0,t.jsx)(M.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eN(e.id),children:e.is_available?"\uD83D\uDD12":"\uD83D\uDD13"}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eg(e.id,e.name),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(q.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})})})]})]}),(0,t.jsx)(p.av,{value:"categories",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إدارة الفئات"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nd,{className:"arabic-text",children:"الاسم"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الحالة"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الترتيب"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)(u.BF,{children:ei.map(e=>(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 arabic-text",children:e.description})]})]})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,t.jsx)(u.nA,{children:e.order_index}),(0,t.jsx)(u.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,t.jsx)(u.nA,{children:(0,t.jsxs)(j.rI,{children:[(0,t.jsx)(j.ty,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(j.SQ,{align:"end",children:[(0,t.jsxs)(j._2,{onClick:()=>e_(e),children:[(0,t.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsxs)(j._2,{onClick:()=>eA(e.id),className:"text-red-600",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})})]})]}),(0,t.jsx)(h.lG,{open:en,onOpenChange:ed,children:(0,t.jsxs)(h.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:eo?"تعديل الفئة":"إضافة فئة جديدة"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:eo?"تعديل بيانات الفئة":"أدخل بيانات الفئة الجديدة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-name",className:"arabic-text",children:"اسم الفئة (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-name",value:em.name_ar,onChange:e=>eh(a=>({...a,name_ar:e.target.value})),placeholder:"أدخل اسم الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-slug",className:"arabic-text",children:"الرابط المختصر (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-slug",value:em.slug,onChange:e=>eh(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-description",className:"arabic-text",children:"الوصف"}),(0,t.jsx)(g.T,{id:"category-description",value:em.description,onChange:e=>eh(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-icon",className:"arabic-text",children:"الأيقونة"}),(0,t.jsx)(o.p,{id:"category-icon",value:em.icon,onChange:e=>eh(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDFF7️"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"category-active",checked:em.is_active,onChange:e=>eh(a=>({...a,is_active:e.target.checked}))}),(0,t.jsx)(x.J,{htmlFor:"category-active",className:"arabic-text",children:"فئة نشطة"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:eC,children:"إلغاء"}),(0,t.jsx)(n.$,{onClick:ek,children:eo?"تحديث":"إضافة"})]})]})]})}),(0,t.jsx)(h.lG,{open:J,onOpenChange:Y,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:"إضافة منتج جديد"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:"أدخل تفاصيل المنتج الجديد"})]}),(0,t.jsx)(P,{onSubmit:ev,onCancel:()=>Y(!1)})]})}),(0,t.jsx)(B,{product:ee,open:es,onOpenChange:et,onSave:ey}),(0,t.jsx)(O.T,{open:er.open,onOpenChange:e=>el(a=>({...a,open:e})),title:"تأكيد حذف المنتج",description:'هل أنت متأكد من حذف المنتج "'.concat(er.productName,'"؟ هذا الإجراء لا يمكن التراجع عنه.'),confirmText:"حذف",cancelText:"إلغاء",variant:"destructive",onConfirm:eb}),(0,t.jsx)(Z.N9,{toasts:s.toasts,onRemove:s.removeToast})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 arabic-text",children:"غير مصرح لك بالوصول"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2 arabic-text",children:"هذه الصفحة مخصصة للمديرين فقط"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["User: ",e?"موجود":"غير موجود"]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Role: ",(null==a?void 0:a.role)||"غير محدد"]})]})})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>c,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>x,nd:()=>o});var t=s(95155),r=s(12115),l=s(59434);let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});i.displayName="Table";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",s),...r})});c.displayName="TableHeader";let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});n.displayName="TableBody",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let x=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});x.displayName="TableCell",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,1672,1056,5847,2632,7443,7784,8351,8441,1684,7358],()=>a(34898)),_N_E=e.O()}]);