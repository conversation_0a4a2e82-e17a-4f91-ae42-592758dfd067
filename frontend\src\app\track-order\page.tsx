"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Search,
  Package,
  Truck,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  Calendar,
  Navigation as NavigationIcon,
  Eye,
  Download,
  Share2
} from 'lucide-react'

// أنواع البيانات
interface TrackingEvent {
  id: string
  status: string
  description: string
  location: string
  timestamp: string
  isCompleted: boolean
}

interface OrderTracking {
  orderNumber: string
  currentStatus: string
  estimatedDelivery: string
  trackingNumber: string
  carrier: string
  progress: number
  events: TrackingEvent[]
  customerInfo: {
    name: string
    phone: string
    address: string
  }
  items: Array<{
    name: string
    quantity: number
    image: string
  }>
}

export default function TrackOrderPage() {
  const { user, profile } = useAuth()
  const [trackingNumber, setTrackingNumber] = useState('')
  const [orderTracking, setOrderTracking] = useState<OrderTracking | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleTrackOrder = async () => {
    if (!trackingNumber.trim()) {
      setError('يرجى إدخال رقم الطلب أو رقم التتبع')
      return
    }

    setLoading(true)
    setError('')

    // محاكاة استعلام التتبع
    setTimeout(() => {
      const mockTracking: OrderTracking = {
        orderNumber: trackingNumber,
        currentStatus: 'في الطريق',
        estimatedDelivery: '2024-01-22T15:00:00Z',
        trackingNumber: 'TRK-' + trackingNumber,
        carrier: 'شركة التوصيل السريع',
        progress: 75,
        events: [
          {
            id: '1',
            status: 'تم تأكيد الطلب',
            description: 'تم استلام طلبك وتأكيده بنجاح',
            location: 'مركز المعالجة - دبي',
            timestamp: '2024-01-20T09:00:00Z',
            isCompleted: true
          },
          {
            id: '2',
            status: 'قيد التحضير',
            description: 'جاري تحضير منتجاتك للشحن',
            location: 'مستودع الإنتاج - دبي',
            timestamp: '2024-01-20T14:30:00Z',
            isCompleted: true
          },
          {
            id: '3',
            status: 'تم الشحن',
            description: 'تم شحن طلبك وهو في طريقه إليك',
            location: 'مركز التوزيع - دبي',
            timestamp: '2024-01-21T08:00:00Z',
            isCompleted: true
          },
          {
            id: '4',
            status: 'في الطريق',
            description: 'الطلب في طريقه للتوصيل',
            location: 'مركز التوزيع المحلي - الشارقة',
            timestamp: '2024-01-21T16:45:00Z',
            isCompleted: true
          },
          {
            id: '5',
            status: 'خرج للتوصيل',
            description: 'الطلب مع مندوب التوصيل',
            location: 'في الطريق إلى العنوان',
            timestamp: '2024-01-22T10:00:00Z',
            isCompleted: false
          },
          {
            id: '6',
            status: 'تم التسليم',
            description: 'تم تسليم الطلب بنجاح',
            location: 'عنوان العميل',
            timestamp: '',
            isCompleted: false
          }
        ],
        customerInfo: {
          name: profile?.full_name || 'أحمد محمد',
          phone: profile?.phone || '+971501234567',
          address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة'
        },
        items: [
          {
            name: 'زي التخرج الكلاسيكي',
            quantity: 1,
            image: '/api/placeholder/80/80'
          },
          {
            name: 'قبعة التخرج المميزة',
            quantity: 1,
            image: '/api/placeholder/80/80'
          }
        ]
      }

      setOrderTracking(mockTracking)
      setLoading(false)
    }, 1500)
  }

  const getStatusIcon = (status: string, isCompleted: boolean) => {
    if (isCompleted) {
      return <CheckCircle className="h-5 w-5 text-green-600" />
    } else if (status === 'خرج للتوصيل') {
      return <Truck className="h-5 w-5 text-blue-600" />
    } else {
      return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'تم تأكيد الطلب': return 'bg-blue-100 text-blue-800'
      case 'قيد التحضير': return 'bg-yellow-100 text-yellow-800'
      case 'تم الشحن': return 'bg-purple-100 text-purple-800'
      case 'في الطريق': return 'bg-orange-100 text-orange-800'
      case 'خرج للتوصيل': return 'bg-green-100 text-green-800'
      case 'تم التسليم': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            تتبع الطلب 📦
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            تابع حالة طلبك ومكان وصوله
          </p>
        </div>

        {/* Search Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="arabic-text">البحث عن طلب</CardTitle>
            <CardDescription className="arabic-text">
              أدخل رقم الطلب أو رقم التتبع لمعرفة حالة طلبك
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="tracking" className="arabic-text">رقم الطلب أو رقم التتبع</Label>
                <Input
                  id="tracking"
                  placeholder="مثال: GT-240120-001 أو TRK-123456"
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                  className="arabic-text"
                />
              </div>
              <div className="flex items-end">
                <Button onClick={handleTrackOrder} disabled={loading}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  <span className="arabic-text">تتبع</span>
                </Button>
              </div>
            </div>
            {error && (
              <div className="mt-3 flex items-center gap-2 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm arabic-text">{error}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tracking Results */}
        {orderTracking && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Tracking Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Current Status */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="arabic-text">حالة الطلب الحالية</CardTitle>
                      <CardDescription className="arabic-text">
                        رقم الطلب: {orderTracking.orderNumber}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusColor(orderTracking.currentStatus)}>
                      {orderTracking.currentStatus}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium arabic-text">تقدم الطلب</span>
                        <span className="text-sm text-gray-600">{orderTracking.progress}%</span>
                      </div>
                      <Progress value={orderTracking.progress} className="h-3" />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">التوصيل المتوقع</p>
                          <p className="font-medium">
                            {new Date(orderTracking.estimatedDelivery).toLocaleDateString('en-US')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Truck className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">شركة الشحن</p>
                          <p className="font-medium arabic-text">{orderTracking.carrier}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Package className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">رقم التتبع</p>
                        <p className="font-medium font-mono">{orderTracking.trackingNumber}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tracking Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تاريخ الطلب</CardTitle>
                  <CardDescription className="arabic-text">
                    تتبع مراحل طلبك من التأكيد حتى التسليم
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {orderTracking.events.map((event, index) => (
                      <div key={event.id} className="flex gap-4">
                        <div className="flex flex-col items-center">
                          <div className={`w-10 h-10 rounded-full border-2 flex items-center justify-center ${
                            event.isCompleted 
                              ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' 
                              : event.status === 'خرج للتوصيل'
                              ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                              : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                          }`}>
                            {getStatusIcon(event.status, event.isCompleted)}
                          </div>
                          {index < orderTracking.events.length - 1 && (
                            <div className={`w-0.5 h-12 mt-2 ${
                              event.isCompleted ? 'bg-green-200' : 'bg-gray-200'
                            }`} />
                          )}
                        </div>
                        <div className="flex-1 pb-6">
                          <div className="flex justify-between items-start mb-1">
                            <h3 className={`font-medium arabic-text ${
                              event.isCompleted ? 'text-gray-900 dark:text-white' : 'text-gray-500'
                            }`}>
                              {event.status}
                            </h3>
                            {event.timestamp && (
                              <span className="text-sm text-gray-500">
                                {new Date(event.timestamp).toLocaleString('en-US')}
                              </span>
                            )}
                          </div>
                          <p className={`text-sm arabic-text ${
                            event.isCompleted ? 'text-gray-600 dark:text-gray-400' : 'text-gray-400'
                          }`}>
                            {event.description}
                          </p>
                          <div className="flex items-center gap-1 mt-1">
                            <MapPin className="h-3 w-3 text-gray-400" />
                            <span className={`text-xs arabic-text ${
                              event.isCompleted ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              {event.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">منتجات الطلب</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {orderTracking.items.map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0">
                          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"></div>
                        </div>
                        <div>
                          <p className="font-medium text-sm arabic-text">{item.name}</p>
                          <p className="text-xs text-gray-600 dark:text-gray-400 arabic-text">
                            الكمية: {item.quantity}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Address */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">عنوان التوصيل</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="text-sm arabic-text">{orderTracking.customerInfo.address}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{orderTracking.customerInfo.phone}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إجراءات سريعة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full arabic-text">
                    <NavigationIcon className="h-4 w-4 mr-2" />
                    تتبع على الخريطة
                  </Button>
                  
                  <Button variant="outline" className="w-full arabic-text">
                    <Download className="h-4 w-4 mr-2" />
                    تحميل تفاصيل التتبع
                  </Button>
                  
                  <Button variant="outline" className="w-full arabic-text">
                    <Share2 className="h-4 w-4 mr-2" />
                    مشاركة حالة الطلب
                  </Button>
                </CardContent>
              </Card>

              {/* Contact Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تحتاج مساعدة؟</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium arabic-text">اتصل بنا</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">+971 4 123 4567</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium arabic-text">راسلنا</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400"><EMAIL></p>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full arabic-text">
                    تواصل مع الدعم
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* No Results */}
        {!orderTracking && !loading && trackingNumber && (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text">
                لم يتم العثور على الطلب
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
                تأكد من رقم الطلب أو رقم التتبع وحاول مرة أخرى
              </p>
              <div className="flex justify-center gap-4">
                <Button variant="outline" className="arabic-text">
                  <Phone className="h-4 w-4 mr-2" />
                  اتصل بالدعم
                </Button>
                <Button className="arabic-text" asChild>
                  <a href="/dashboard/student">لوحة التحكم</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
