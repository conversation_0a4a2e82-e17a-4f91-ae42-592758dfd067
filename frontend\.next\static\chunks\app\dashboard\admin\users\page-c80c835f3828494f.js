(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4274],{20259:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>O});var l=a(95155),t=a(12115),r=a(45876),c=a(37784),i=a(30285),n=a(66695),d=a(62523),x=a(26126),m=a(59409),h=a(44838),o=a(85057),j=a(88539),u=a(54165),p=a(80333),v=a(75525),N=a(87949),g=a(53896),b=a(29799);a(71007);var f=a(78749),y=a(92657);function w(e){let{onSubmit:s,onCancel:a}=e,[r,c]=(0,t.useState)({email:"",full_name:"",phone:"",role:"student",password:"",confirmPassword:"",status:"active",verified:!1,notes:""}),[x,h]=(0,t.useState)(!1),[w,_]=(0,t.useState)(!1),[C,Z]=(0,t.useState)({}),[k,A]=(0,t.useState)(!1),S=()=>{let e={};return r.email?/\S+@\S+\.\S+/.test(r.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",r.full_name?r.full_name.length<2&&(e.full_name="الاسم يجب أن يكون أكثر من حرفين"):e.full_name="الاسم الكامل مطلوب",r.password?r.password.length<6&&(e.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):e.password="كلمة المرور مطلوبة",r.password!==r.confirmPassword&&(e.confirmPassword="كلمة المرور غير متطابقة"),r.phone&&!/^\+?[1-9]\d{1,14}$/.test(r.phone)&&(e.phone="رقم الهاتف غير صحيح"),"student"===r.role&&r.student_id&&r.student_id.length<3&&(e.student_id="رقم الطالب يجب أن يكون 3 أحرف على الأقل"),"school"!==r.role||r.school_name||(e.school_name="اسم المدرسة مطلوب"),"delivery"!==r.role||r.delivery_company||(e.delivery_company="اسم شركة التوصيل مطلوب"),Z(e),0===Object.keys(e).length},J=async e=>{if(e.preventDefault(),S()){A(!0);try{let{confirmPassword:e,...a}=r;await s(a)}catch(e){console.error("Error creating user:",e)}finally{A(!1)}}},F=(e,s)=>{c(a=>({...a,[e]:s})),C[e]&&Z(s=>({...s,[e]:""}))};return(0,l.jsx)(u.lG,{open:!0,onOpenChange:a,children:(0,l.jsxs)(u.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(u.c7,{children:[(0,l.jsx)(u.L3,{className:"arabic-text",children:"إضافة مستخدم جديد"}),(0,l.jsx)(u.rr,{className:"arabic-text",children:"أدخل بيانات المستخدم الجديد وحدد دوره في النظام"})]}),(0,l.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"المعلومات الأساسية"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"full_name",className:"arabic-text",children:"الاسم الكامل *"}),(0,l.jsx)(d.p,{id:"full_name",value:r.full_name,onChange:e=>F("full_name",e.target.value),placeholder:"أدخل الاسم الكامل",className:C.full_name?"border-red-500":""}),C.full_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.full_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني *"}),(0,l.jsx)(d.p,{id:"email",type:"email",value:r.email,onChange:e=>F("email",e.target.value),placeholder:"<EMAIL>",className:C.email?"border-red-500":""}),C.email&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.email})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,l.jsx)(d.p,{id:"phone",value:r.phone,onChange:e=>F("phone",e.target.value),placeholder:"+971501234567",className:C.phone?"border-red-500":""}),C.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.phone})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"role",className:"arabic-text",children:"الدور *"}),(0,l.jsxs)(m.l6,{value:r.role,onValueChange:e=>F("role",e),children:[(0,l.jsx)(m.bq,{children:(0,l.jsx)(m.yv,{placeholder:"اختر الدور"})}),(0,l.jsxs)(m.gC,{children:[(0,l.jsx)(m.eb,{value:"student",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4"}),"طالب"]})}),(0,l.jsx)(m.eb,{value:"school",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(g.A,{className:"h-4 w-4"}),"مدرسة"]})}),(0,l.jsx)(m.eb,{value:"delivery",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),"شركة توصيل"]})}),(0,l.jsx)(m.eb,{value:"admin",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"مدير"]})})]})]})]})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"كلمة المرور"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"password",className:"arabic-text",children:"كلمة المرور *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"password",type:x?"text":"password",value:r.password,onChange:e=>F("password",e.target.value),placeholder:"أدخل كلمة المرور",className:C.password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>h(!x),children:x?(0,l.jsx)(f.A,{className:"h-4 w-4"}):(0,l.jsx)(y.A,{className:"h-4 w-4"})})]}),C.password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.password})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"confirmPassword",className:"arabic-text",children:"تأكيد كلمة المرور *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"confirmPassword",type:w?"text":"password",value:r.confirmPassword,onChange:e=>F("confirmPassword",e.target.value),placeholder:"أعد إدخال كلمة المرور",className:C.confirmPassword?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>_(!w),children:w?(0,l.jsx)(f.A,{className:"h-4 w-4"}):(0,l.jsx)(y.A,{className:"h-4 w-4"})})]}),C.confirmPassword&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.confirmPassword})]})]})})]}),"student"===r.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات الطالب"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"student_id",className:"arabic-text",children:"رقم الطالب"}),(0,l.jsx)(d.p,{id:"student_id",value:r.student_id||"",onChange:e=>F("student_id",e.target.value),placeholder:"STU2024001",className:C.student_id?"border-red-500":""}),C.student_id&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.student_id})]})})]}),"school"===r.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات المدرسة"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"school_name",className:"arabic-text",children:"اسم المدرسة *"}),(0,l.jsx)(d.p,{id:"school_name",value:r.school_name||"",onChange:e=>F("school_name",e.target.value),placeholder:"جامعة الإمارات العربية المتحدة",className:C.school_name?"border-red-500":""}),C.school_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.school_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"school_address",className:"arabic-text",children:"عنوان المدرسة"}),(0,l.jsx)(j.T,{id:"school_address",value:r.school_address||"",onChange:e=>F("school_address",e.target.value),placeholder:"العنوان الكامل للمدرسة",rows:3})]})]})]}),"delivery"===r.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات شركة التوصيل"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"delivery_company",className:"arabic-text",children:"اسم الشركة *"}),(0,l.jsx)(d.p,{id:"delivery_company",value:r.delivery_company||"",onChange:e=>F("delivery_company",e.target.value),placeholder:"شركة التوصيل السريع",className:C.delivery_company?"border-red-500":""}),C.delivery_company&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.delivery_company})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"delivery_license",className:"arabic-text",children:"رقم الترخيص"}),(0,l.jsx)(d.p,{id:"delivery_license",value:r.delivery_license||"",onChange:e=>F("delivery_license",e.target.value),placeholder:"رقم ترخيص الشركة"})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"إعدادات الحساب"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(o.J,{className:"arabic-text",children:"حالة الحساب"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب نشطاً أم لا"})]}),(0,l.jsx)(p.d,{checked:"active"===r.status,onCheckedChange:e=>F("status",e?"active":"inactive")})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(o.J,{className:"arabic-text",children:"حساب محقق"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب محققاً أم لا"})]}),(0,l.jsx)(p.d,{checked:r.verified,onCheckedChange:e=>F("verified",e)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"notes",className:"arabic-text",children:"ملاحظات"}),(0,l.jsx)(j.T,{id:"notes",value:r.notes||"",onChange:e=>F("notes",e.target.value),placeholder:"ملاحظات إضافية حول المستخدم",rows:3})]})]})]}),(0,l.jsxs)(u.Es,{children:[(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:a,children:"إلغاء"}),(0,l.jsx)(i.$,{type:"submit",disabled:k,children:k?"جاري الإضافة...":"إضافة المستخدم"})]})]})]})})}function _(e){let{user:s,onSubmit:a,onCancel:r}=e,[c,x]=(0,t.useState)({email:s.email,full_name:s.full_name,phone:s.phone||"",role:s.role,status:s.status,verified:s.verified,student_id:s.student_id||"",school_name:s.school_name||"",delivery_company:s.delivery_company||"",new_password:"",confirm_new_password:""}),[h,j]=(0,t.useState)(!1),[w,_]=(0,t.useState)(!1),[C,Z]=(0,t.useState)({}),[k,A]=(0,t.useState)(!1),S=()=>{let e={};return c.email?/\S+@\S+\.\S+/.test(c.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",c.full_name?c.full_name.length<2&&(e.full_name="الاسم يجب أن يكون أكثر من حرفين"):e.full_name="الاسم الكامل مطلوب",c.new_password&&(c.new_password.length<6&&(e.new_password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"),c.new_password!==c.confirm_new_password&&(e.confirm_new_password="كلمة المرور غير متطابقة")),c.phone&&!/^\+?[1-9]\d{1,14}$/.test(c.phone)&&(e.phone="رقم الهاتف غير صحيح"),"student"===c.role&&c.student_id&&c.student_id.length<3&&(e.student_id="رقم الطالب يجب أن يكون 3 أحرف على الأقل"),"school"!==c.role||c.school_name||(e.school_name="اسم المدرسة مطلوب"),"delivery"!==c.role||c.delivery_company||(e.delivery_company="اسم شركة التوصيل مطلوب"),Z(e),0===Object.keys(e).length},J=async e=>{if(e.preventDefault(),S()){A(!0);try{let{confirm_new_password:e,...s}=c;s.new_password||delete s.new_password,await a(s)}catch(e){console.error("Error updating user:",e)}finally{A(!1)}}},F=(e,s)=>{x(a=>({...a,[e]:s})),C[e]&&Z(s=>({...s,[e]:""}))};return(0,l.jsx)(u.lG,{open:!0,onOpenChange:r,children:(0,l.jsxs)(u.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(u.c7,{children:[(0,l.jsx)(u.L3,{className:"arabic-text",children:"تعديل بيانات المستخدم"}),(0,l.jsxs)(u.rr,{className:"arabic-text",children:["تعديل بيانات المستخدم: ",s.full_name]})]}),(0,l.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"المعلومات الأساسية"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"full_name",className:"arabic-text",children:"الاسم الكامل *"}),(0,l.jsx)(d.p,{id:"full_name",value:c.full_name,onChange:e=>F("full_name",e.target.value),placeholder:"أدخل الاسم الكامل",className:C.full_name?"border-red-500":""}),C.full_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.full_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني *"}),(0,l.jsx)(d.p,{id:"email",type:"email",value:c.email,onChange:e=>F("email",e.target.value),placeholder:"<EMAIL>",className:C.email?"border-red-500":""}),C.email&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.email})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,l.jsx)(d.p,{id:"phone",value:c.phone,onChange:e=>F("phone",e.target.value),placeholder:"+971501234567",className:C.phone?"border-red-500":""}),C.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.phone})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"role",className:"arabic-text",children:"الدور *"}),(0,l.jsxs)(m.l6,{value:c.role,onValueChange:e=>F("role",e),children:[(0,l.jsx)(m.bq,{children:(0,l.jsx)(m.yv,{placeholder:"اختر الدور"})}),(0,l.jsxs)(m.gC,{children:[(0,l.jsx)(m.eb,{value:"student",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4"}),"طالب"]})}),(0,l.jsx)(m.eb,{value:"school",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(g.A,{className:"h-4 w-4"}),"مدرسة"]})}),(0,l.jsx)(m.eb,{value:"delivery",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),"شركة توصيل"]})}),(0,l.jsx)(m.eb,{value:"admin",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"مدير"]})})]})]})]})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{children:[(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"تغيير كلمة المرور"}),(0,l.jsx)(n.BT,{className:"arabic-text",children:"اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور"})]}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"new_password",className:"arabic-text",children:"كلمة المرور الجديدة"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"new_password",type:h?"text":"password",value:c.new_password||"",onChange:e=>F("new_password",e.target.value),placeholder:"كلمة المرور الجديدة",className:C.new_password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>j(!h),children:h?(0,l.jsx)(f.A,{className:"h-4 w-4"}):(0,l.jsx)(y.A,{className:"h-4 w-4"})})]}),C.new_password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.new_password})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"confirm_new_password",className:"arabic-text",children:"تأكيد كلمة المرور الجديدة"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"confirm_new_password",type:w?"text":"password",value:c.confirm_new_password||"",onChange:e=>F("confirm_new_password",e.target.value),placeholder:"تأكيد كلمة المرور الجديدة",className:C.confirm_new_password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>_(!w),children:w?(0,l.jsx)(f.A,{className:"h-4 w-4"}):(0,l.jsx)(y.A,{className:"h-4 w-4"})})]}),C.confirm_new_password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.confirm_new_password})]})]})})]}),"student"===c.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات الطالب"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"student_id",className:"arabic-text",children:"رقم الطالب"}),(0,l.jsx)(d.p,{id:"student_id",value:c.student_id||"",onChange:e=>F("student_id",e.target.value),placeholder:"STU2024001",className:C.student_id?"border-red-500":""}),C.student_id&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.student_id})]})})]}),"school"===c.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات المدرسة"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"school_name",className:"arabic-text",children:"اسم المدرسة *"}),(0,l.jsx)(d.p,{id:"school_name",value:c.school_name||"",onChange:e=>F("school_name",e.target.value),placeholder:"جامعة الإمارات العربية المتحدة",className:C.school_name?"border-red-500":""}),C.school_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.school_name})]})})]}),"delivery"===c.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات شركة التوصيل"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"delivery_company",className:"arabic-text",children:"اسم الشركة *"}),(0,l.jsx)(d.p,{id:"delivery_company",value:c.delivery_company||"",onChange:e=>F("delivery_company",e.target.value),placeholder:"شركة التوصيل السريع",className:C.delivery_company?"border-red-500":""}),C.delivery_company&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:C.delivery_company})]})})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"إعدادات الحساب"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{className:"arabic-text",children:"حالة الحساب"}),(0,l.jsxs)(m.l6,{value:c.status,onValueChange:e=>F("status",e),children:[(0,l.jsx)(m.bq,{children:(0,l.jsx)(m.yv,{placeholder:"اختر الحالة"})}),(0,l.jsxs)(m.gC,{children:[(0,l.jsx)(m.eb,{value:"active",children:"نشط"}),(0,l.jsx)(m.eb,{value:"inactive",children:"غير نشط"}),(0,l.jsx)(m.eb,{value:"suspended",children:"معلق"})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(o.J,{className:"arabic-text",children:"حساب محقق"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب محققاً أم لا"})]}),(0,l.jsx)(p.d,{checked:c.verified,onCheckedChange:e=>F("verified",e)})]})]})]}),(0,l.jsxs)(u.Es,{children:[(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:r,children:"إلغاء"}),(0,l.jsx)(i.$,{type:"submit",disabled:k,children:k?"جاري التحديث...":"حفظ التغييرات"})]})]})]})})}var C=a(7226),Z=a(6982),k=a(6874),A=a.n(k),S=a(17580),J=a(35169),F=a(84616),B=a(47924),R=a(91788),W=a(29869),T=a(53904),$=a(5623),E=a(13717),L=a(9446),P=a(55670),z=a(62525);let D=[{id:"1",email:"<EMAIL>",full_name:"مدير النظام",phone:"+971501234567",role:"admin",status:"active",created_at:"2024-01-15T10:00:00Z",last_login:"2024-01-20T14:30:00Z",verified:!0},{id:"2",email:"<EMAIL>",full_name:"أحمد محمد علي",phone:"+971507654321",role:"student",status:"active",created_at:"2024-01-16T09:15:00Z",last_login:"2024-01-20T12:45:00Z",student_id:"STU2024001",verified:!0},{id:"3",email:"<EMAIL>",full_name:"جامعة الإمارات",phone:"+97126123456",role:"school",status:"active",created_at:"2024-01-10T08:00:00Z",last_login:"2024-01-20T11:20:00Z",school_name:"جامعة الإمارات العربية المتحدة",verified:!0},{id:"4",email:"<EMAIL>",full_name:"شركة التوصيل السريع",phone:"+97144567890",role:"delivery",status:"active",created_at:"2024-01-12T07:30:00Z",last_login:"2024-01-19T16:10:00Z",delivery_company:"شركة التوصيل السريع",verified:!1}];function O(){let[e,s]=(0,t.useState)(D),[a,o]=(0,t.useState)(D),[j,u]=(0,t.useState)(""),[p,f]=(0,t.useState)("all"),[y,k]=(0,t.useState)("all"),[O,q]=(0,t.useState)(!1),[V,U]=(0,t.useState)(null),[G,I]=(0,t.useState)(null),[Q,H]=(0,t.useState)(!1),{toasts:K,removeToast:M,success:X}=(0,Z.dj)();(0,t.useEffect)(()=>{let s=e;j&&(s=s.filter(e=>{var s,a,l,t;return e.full_name.toLowerCase().includes(j.toLowerCase())||e.email.toLowerCase().includes(j.toLowerCase())||(null==(s=e.phone)?void 0:s.includes(j))||(null==(a=e.student_id)?void 0:a.toLowerCase().includes(j.toLowerCase()))||(null==(l=e.school_name)?void 0:l.toLowerCase().includes(j.toLowerCase()))||(null==(t=e.delivery_company)?void 0:t.toLowerCase().includes(j.toLowerCase()))})),"all"!==p&&(s=s.filter(e=>e.role===p)),"all"!==y&&(s=s.filter(e=>e.status===y)),o(s)},[e,j,p,y]);let Y=e=>{s(s=>s.map(s=>s.id===e?{...s,status:"active"===s.status?"suspended":"active"}:s)),X("تم تحديث حالة المستخدم","success")},ee=e=>{s(s=>s.map(s=>s.id===e?{...s,verified:!s.verified}:s)),X("تم تحديث حالة التحقق","success")},es=e=>{switch(e){case"admin":return(0,l.jsx)(v.A,{className:"h-4 w-4"});case"student":return(0,l.jsx)(N.A,{className:"h-4 w-4"});case"school":return(0,l.jsx)(g.A,{className:"h-4 w-4"});case"delivery":return(0,l.jsx)(b.A,{className:"h-4 w-4"});default:return(0,l.jsx)(S.A,{className:"h-4 w-4"})}},ea=e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"inactive":return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";case"suspended":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";default:return"bg-gray-100 text-gray-800"}},el=e=>{switch(e){case"active":return"نشط";case"inactive":return"غير نشط";case"suspended":return"معلق";default:return e}},et=e=>{switch(e){case"admin":return"مدير";case"student":return"طالب";case"school":return"مدرسة";case"delivery":return"توصيل";default:return e}};return(0,l.jsx)(r.O,{allowedRoles:["admin"],children:(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,l.jsx)(c.V,{}),(0,l.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,l.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,l.jsxs)(A(),{href:"/dashboard/admin",children:[(0,l.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المستخدمين \uD83D\uDC65"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة حسابات المستخدمين والصلاحيات والأدوار"})]}),(0,l.jsxs)(i.$,{onClick:()=>q(!0),children:[(0,l.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"إضافة مستخدم"]})]})]}),(0,l.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"إجمالي المستخدمين"}),(0,l.jsx)(S.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.length}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground arabic-text",children:["+",e.filter(e=>"active"===e.status).length," نشط"]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"الطلاب"}),(0,l.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"student"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"مستخدم طالب"})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"المدارس"}),(0,l.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"school"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"مؤسسة تعليمية"})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"شركات التوصيل"}),(0,l.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"delivery"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"شركة توصيل"})]})]})]}),(0,l.jsxs)(n.Zp,{className:"mb-8",children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"arabic-text",children:"البحث والتصفية"})}),(0,l.jsx)(n.Wu,{children:(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(B.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,l.jsx)(d.p,{placeholder:"البحث بالاسم، البريد الإلكتروني، الهاتف...",value:j,onChange:e=>u(e.target.value),className:"pl-10 arabic-text"})]})}),(0,l.jsxs)(m.l6,{value:p,onValueChange:f,children:[(0,l.jsx)(m.bq,{className:"w-full md:w-48",children:(0,l.jsx)(m.yv,{placeholder:"تصفية بالدور"})}),(0,l.jsxs)(m.gC,{children:[(0,l.jsx)(m.eb,{value:"all",children:"جميع الأدوار"}),(0,l.jsx)(m.eb,{value:"admin",children:"مدير"}),(0,l.jsx)(m.eb,{value:"student",children:"طالب"}),(0,l.jsx)(m.eb,{value:"school",children:"مدرسة"}),(0,l.jsx)(m.eb,{value:"delivery",children:"توصيل"})]})]}),(0,l.jsxs)(m.l6,{value:y,onValueChange:k,children:[(0,l.jsx)(m.bq,{className:"w-full md:w-48",children:(0,l.jsx)(m.yv,{placeholder:"تصفية بالحالة"})}),(0,l.jsxs)(m.gC,{children:[(0,l.jsx)(m.eb,{value:"all",children:"جميع الحالات"}),(0,l.jsx)(m.eb,{value:"active",children:"نشط"}),(0,l.jsx)(m.eb,{value:"inactive",children:"غير نشط"}),(0,l.jsx)(m.eb,{value:"suspended",children:"معلق"})]})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),(0,l.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"استيراد"]})]})]})})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)(n.ZB,{className:"arabic-text",children:["قائمة المستخدمين (",a.length,")"]}),(0,l.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>window.location.reload(),children:[(0,l.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"تحديث"]})]})}),(0,l.jsx)(n.Wu,{children:(0,l.jsxs)("div",{className:"overflow-x-auto",children:[(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b",children:[(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"المستخدم"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الدور"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الحالة"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"التحقق"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"آخر دخول"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الإجراءات"})]})}),(0,l.jsx)("tbody",{children:a.map(e=>(0,l.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:e.full_name.charAt(0)})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text",children:e.full_name}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:e.email}),e.phone&&(0,l.jsx)("p",{className:"text-xs text-gray-400",children:e.phone})]})]})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[es(e.role),(0,l.jsx)("span",{className:"arabic-text",children:et(e.role)})]})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)(x.E,{className:ea(e.status),children:el(e.status)})}),(0,l.jsx)("td",{className:"py-4 px-4",children:e.verified?(0,l.jsx)(x.E,{className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:"محقق"}):(0,l.jsx)(x.E,{variant:"outline",children:"غير محقق"})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:e.last_login?new Date(e.last_login).toLocaleDateString("ar-AE"):"لم يدخل بعد"})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)("div",{className:"actions flex items-center gap-2",children:(0,l.jsxs)(h.rI,{children:[(0,l.jsx)(h.ty,{asChild:!0,children:(0,l.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,l.jsx)($.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(h.SQ,{align:"end",children:[(0,l.jsx)(h.lp,{className:"arabic-text",children:"الإجراءات"}),(0,l.jsx)(h.mB,{}),(0,l.jsxs)(h._2,{onClick:()=>U(e),children:[(0,l.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,l.jsx)(h._2,{onClick:()=>Y(e.id),children:"active"===e.status?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"تعليق"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,l.jsx)(h._2,{onClick:()=>ee(e.id),children:e.verified?"إلغاء التحقق":"تحقق"}),(0,l.jsx)(h.mB,{}),(0,l.jsxs)(h._2,{onClick:()=>I(e),className:"text-red-600",children:[(0,l.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})})]},e.id))})]}),0===a.length&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(S.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد مستخدمين"})]})]})})]})]}),O&&(0,l.jsx)(w,{onSubmit:e=>{let a={id:Date.now().toString(),email:e.email,full_name:e.full_name,phone:e.phone,role:e.role,status:"active",created_at:new Date().toISOString(),verified:!1,...e};s(e=>[a,...e]),q(!1),X("تم إضافة المستخدم بنجاح","success")},onCancel:()=>q(!1)}),V&&(0,l.jsx)(_,{user:V,onSubmit:e=>{V&&(s(s=>s.map(s=>s.id===V.id?{...s,...e}:s)),U(null),X("تم تحديث بيانات المستخدم بنجاح","success"))},onCancel:()=>U(null)}),G&&(0,l.jsx)(C.T,{title:"حذف المستخدم",message:'هل أنت متأكد من حذف المستخدم "'.concat(G.full_name,'"؟ هذا الإجراء لا يمكن التراجع عنه.'),onConfirm:()=>{G&&(s(e=>e.filter(e=>e.id!==G.id)),I(null),X("تم حذف المستخدم بنجاح","success"))},onCancel:()=>I(null),confirmText:"حذف",cancelText:"إلغاء",variant:"destructive"}),(0,l.jsx)(Z.N9,{toasts:K,onRemove:M})]})})}},45876:(e,s,a)=>{"use strict";a.d(s,{O:()=>d});var l=a(95155),t=a(12115),r=a(35695),c=a(40283),i=a(66695),n=a(51154);function d(e){let{children:s,requiredRole:a,redirectTo:d="/auth"}=e,{user:x,profile:m,loading:h,hasRole:o}=(0,c.A)(),j=(0,r.useRouter)(),[u,p]=(0,t.useState)(!1);return((0,t.useEffect)(()=>{p(!0)},[]),(0,t.useEffect)(()=>{if(u&&!h){if(!x)return void j.push(d);if(a&&!o(a))return void j.push("/unauthorized")}},[u,x,m,h,a,o,j,d]),!u||h)?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,l.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,l.jsx)(n.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):x&&(!a||o(a))?(0,l.jsx)(l.Fragment,{children:s}):null}},78640:(e,s,a)=>{Promise.resolve().then(a.bind(a,20259))}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,5486,5148,8698,6874,7889,1475,1672,1056,9045,2632,7443,7784,8351,8441,1684,7358],()=>s(78640)),_N_E=e.O()}]);