"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6072],{4516:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6736:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},13717:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},27809:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29869:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33109:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},37108:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},55863:(e,a,t)=>{t.d(a,{C1:()=>x,bL:()=>b});var n=t(12115),r=t(46081),l=t(63655),o=t(95155),d="Progress",[i,s]=(0,r.A)(d),[c,u]=i(d),p=n.forwardRef((e,a)=>{var t,n,r,d;let{__scopeProgress:i,value:s=null,max:u,getValueLabel:p=y,...h}=e;(u||0===u)&&!m(u)&&console.error((t="".concat(u),n="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let v=m(u)?u:100;null===s||A(s,v)||console.error((r="".concat(s),d="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(d,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=A(s,v)?s:null,x=f(b)?p(b,v):void 0;return(0,o.jsx)(c,{scope:i,value:b,max:v,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":f(b)?b:void 0,"aria-valuetext":x,role:"progressbar","data-state":k(b,v),"data-value":null!=b?b:void 0,"data-max":v,...h,ref:a})})});p.displayName=d;var h="ProgressIndicator",v=n.forwardRef((e,a)=>{var t;let{__scopeProgress:n,...r}=e,d=u(h,n);return(0,o.jsx)(l.sG.div,{"data-state":k(d.value,d.max),"data-value":null!=(t=d.value)?t:void 0,"data-max":d.max,...r,ref:a})});function y(e,a){return"".concat(Math.round(e/a*100),"%")}function k(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function f(e){return"number"==typeof e}function m(e){return f(e)&&!isNaN(e)&&e>0}function A(e,a){return f(e)&&!isNaN(e)&&e<=a&&e>=0}v.displayName=h;var b=p,x=v},55868:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},60704:(e,a,t)=>{t.d(a,{B8:()=>z,UC:()=>I,bL:()=>q,l9:()=>H});var n=t(12115),r=t(85185),l=t(46081),o=t(89196),d=t(28905),i=t(63655),s=t(94315),c=t(5845),u=t(61285),p=t(95155),h="Tabs",[v,y]=(0,l.A)(h,[o.RG]),k=(0,o.RG)(),[f,m]=v(h),A=n.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,onValueChange:r,defaultValue:l,orientation:o="horizontal",dir:d,activationMode:v="automatic",...y}=e,k=(0,s.jH)(d),[m,A]=(0,c.i)({prop:n,onChange:r,defaultProp:null!=l?l:"",caller:h});return(0,p.jsx)(f,{scope:t,baseId:(0,u.B)(),value:m,onValueChange:A,orientation:o,dir:k,activationMode:v,children:(0,p.jsx)(i.sG.div,{dir:k,"data-orientation":o,...y,ref:a})})});A.displayName=h;var b="TabsList",x=n.forwardRef((e,a)=>{let{__scopeTabs:t,loop:n=!0,...r}=e,l=m(b,t),d=k(t);return(0,p.jsx)(o.bL,{asChild:!0,...d,orientation:l.orientation,dir:l.dir,loop:n,children:(0,p.jsx)(i.sG.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:a})})});x.displayName=b;var M="TabsTrigger",g=n.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,disabled:l=!1,...d}=e,s=m(M,t),c=k(t),u=C(s.baseId,n),h=N(s.baseId,n),v=n===s.value;return(0,p.jsx)(o.q7,{asChild:!0,...c,focusable:!l,active:v,children:(0,p.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":h,"data-state":v?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...d,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;v||l||!e||s.onValueChange(n)})})})});g.displayName=M;var j="TabsContent",w=n.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,forceMount:l,children:o,...s}=e,c=m(j,t),u=C(c.baseId,r),h=N(c.baseId,r),v=r===c.value,y=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(d.C,{present:l||v,children:t=>{let{present:n}=t;return(0,p.jsx)(i.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:h,tabIndex:0,...s,ref:a,style:{...e.style,animationDuration:y.current?"0s":void 0},children:n&&o})}})});function C(e,a){return"".concat(e,"-trigger-").concat(a)}function N(e,a){return"".concat(e,"-content-").concat(a)}w.displayName=j;var q=A,z=x,H=g,I=w},66932:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},71366:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},72713:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},84616:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},91788:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,a,t)=>{t.d(a,{A:()=>n});let n=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);