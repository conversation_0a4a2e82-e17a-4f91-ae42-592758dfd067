"use strict";(()=>{var e={};e.id=3654,e.ids=[3654],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75295:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),p=r(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_unpublished"),s=t.get("language")||"ar",n=p.CN.getPages();r||(n=n.filter(e=>e.is_published));let o=n.map(e=>({...e,page_content:e.page_content.filter(e=>e.language===s)}));return i.NextResponse.json({pages:o,total:o.length})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function l(e){try{let t=cookies(),r=createClient(t),{data:{user:s}}=await r.auth.getUser();if(!s)return i.NextResponse.json({error:"غير مصرح لك بالوصول"},{status:401});let{data:n}=await r.from("profiles").select("role").eq("id",s.id).single();if(!n||"admin"!==n.role)return i.NextResponse.json({error:"غير مصرح لك بهذا الإجراء"},{status:403});let{slug:o,is_published:a,featured_image:p,content:u}=await e.json();if(!o||!u||!u.ar||!u.ar.title||!u.ar.content)return i.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let{data:l}=await r.from("pages").select("id").eq("slug",o).single();if(l)return i.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let{data:d,error:c}=await r.from("pages").insert({slug:o,is_published:a??!1,featured_image:p||null,author_id:s.id}).select().single();if(c)return console.error("Error inserting page:",c),i.NextResponse.json({error:"فشل في إضافة الصفحة"},{status:500});let g=[];for(let[e,t]of Object.entries(u))t&&"object"==typeof t&&t.title&&t.content&&g.push({page_id:d.id,language:e,title:t.title,content:t.content,meta_description:t.meta_description||null,meta_keywords:t.meta_keywords||null});if(g.length>0){let{error:e}=await r.from("page_content").insert(g);if(e)return await r.from("pages").delete().eq("id",d.id),console.error("Error inserting page content:",e),i.NextResponse.json({error:"فشل في إضافة محتوى الصفحة"},{status:500})}return i.NextResponse.json({message:"تم إضافة الصفحة بنجاح",page:d},{status:201})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/pages/route",pathname:"/api/pages",filename:"route",bundlePath:"app/api/pages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:g,serverHooks:x}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(75295));module.exports=s})();