(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{17853:(e,r,t)=>{Promise.resolve().then(t.bind(t,44879))},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},40283:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,AuthProvider:()=>n,g:()=>s});var a=t(95155),o=t(12115),s=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});let l=(0,o.createContext)(void 0);function n(e){let{children:r}=e,[t,s]=(0,o.useState)(null),[n,c]=(0,o.useState)(null),[i,d]=(0,o.useState)(!0),[u,m]=(0,o.useState)(!1);(0,o.useEffect)(()=>{m(!0)},[]),(0,o.useEffect)(()=>{u&&(async()=>{try{let e=localStorage.getItem("mockUser"),r=localStorage.getItem("mockProfile");if(e&&r){let t=JSON.parse(e),a=JSON.parse(r);if(t&&a&&t.id&&a.id){let e=localStorage.getItem("sessionTimestamp"),r=Date.now(),o=e?r-parseInt(e):0;e&&o<864e5?(s(t),c(a),console.log("User data loaded from localStorage:",{userData:t,profileData:a})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{d(!1)}})()},[u]),(0,o.useEffect)(()=>{if(!t||!n)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},r=["click","keypress","scroll","mousemove"];return r.forEach(r=>{document.addEventListener(r,e,{passive:!0})}),()=>{r.forEach(r=>{document.removeEventListener(r,e)})}},[t,n]);let f=async(e,r,t)=>(console.log("Sign up:",e,t),{data:{user:{id:"1",email:e}},error:null}),g=async(e,r)=>{console.log("Sign in:",e);let t={id:"1",email:e},a="student";e.includes("admin")?a="admin":e.includes("school")?a="school":e.includes("delivery")&&(a="delivery");let o={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:a,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};s(t),c(o);try{localStorage.setItem("mockUser",JSON.stringify(t)),localStorage.setItem("mockProfile",JSON.stringify(o)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:t,mockProfile:o})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{"admin"===a?window.location.href="/dashboard/admin":"school"===a?window.location.href="/dashboard/school":"delivery"===a?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:t},error:null}},h=async()=>{try{return s(null),c(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},v=async e=>{if(!t)return{data:null,error:"No user logged in"};let r={...n,...e};return c(r),{data:r,error:null}};return(0,a.jsx)(l.Provider,{value:{user:t,profile:n,loading:i,signUp:f,signIn:g,signOut:h,updateProfile:v,hasRole:e=>{if(!n)return!1;let r={admin:4,school:3,delivery:2,student:1};return r[n.role]>=r[e]}},children:r})}function c(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(95155),o=t(12115),s=t(45876),l=t(40283),n=t(66695),c=t(51154);function i(){let{profile:e}=(0,l.A)();return(0,o.useEffect)(()=>{if(e)switch(e.role){case l.g.ADMIN:window.location.href="/dashboard/admin";break;case l.g.SCHOOL:window.location.href="/dashboard/school";break;case l.g.DELIVERY:window.location.href="/dashboard/delivery";break;case l.g.STUDENT:default:window.location.href="/dashboard/student"}},[e]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:(0,a.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-center",children:"جاري إعادة التوجيه إلى لوحة التحكم المناسبة..."})]})})})}function d(){return(0,a.jsx)(s.O,{children:(0,a.jsx)(i,{})})}},45876:(e,r,t)=>{"use strict";t.d(r,{O:()=>i});var a=t(95155),o=t(12115),s=t(35695),l=t(40283),n=t(66695),c=t(51154);function i(e){let{children:r,requiredRole:t,redirectTo:i="/auth"}=e,{user:d,profile:u,loading:m,hasRole:f}=(0,l.A)(),g=(0,s.useRouter)(),[h,v]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{v(!0)},[]),(0,o.useEffect)(()=>{if(h&&!m){if(!d)return void g.push(i);if(t&&!f(t))return void g.push("/unauthorized")}},[h,d,u,m,t,f,g,i]),!h||m)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):d&&(!t||f(t))?(0,a.jsx)(a.Fragment,{children:r}):null}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var a=t(52596),o=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,o.QP)((0,a.$)(r))}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>i,ZB:()=>n,Zp:()=>s,aR:()=>l});var a=t(95155);t(12115);var o=t(59434);function s(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,8441,1684,7358],()=>r(17853)),_N_E=e.O()}]);