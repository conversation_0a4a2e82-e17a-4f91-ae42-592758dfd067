(()=>{var e={};e.id=7623,e.ids=[7623],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var n=r(65239),a=r(48088),i=r(88170),l=r.n(i),s=r(30893),o={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>s[e]);r.d(t,o);let c={children:["",{children:["dashboard",{children:["admin",{children:["menu-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33473)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/menu-management/page",pathname:"/dashboard/admin/menu-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6943:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>d});var n=r(60687);r(43210);var a=r(25911),i=r(78272),l=r(13964),s=r(3589),o=r(4780);function c({...e}){return(0,n.jsx)(a.bL,{"data-slot":"select",...e})}function d({...e}){return(0,n.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...l}){return(0,n.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[r,(0,n.jsx)(a.In,{asChild:!0,children:(0,n.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:r="popper",...i}){return(0,n.jsx)(a.ZL,{children:(0,n.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,n.jsx)(p,{}),(0,n.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(g,{})]})})}function f({className:e,children:t,...r}){return(0,n.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(a.VF,{children:(0,n.jsx)(l.A,{className:"size-4"})})}),(0,n.jsx)(a.p4,{children:t})]})}function p({className:e,...t}){return(0,n.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(s.A,{className:"size-4"})})}function g({className:e,...t}){return(0,n.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"size-4"})})}},18629:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>tV});var a,i,l,s,o,c,d,u,h,f,p=r(60687),g=r(43210),m=r.n(g),v=r(63213),x=r(32884),b=r(29523),y=r(44493),w=r(96834),j=r(89667),k=r(80013),C=r(15079),N=r(54987),M=r(63503),D=r(85763);let E=(0,r(62688).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);var A=r(12597),_=r(13861),S=r(63143),R=r(88233),T=r(47342),z=r(25334),L=r(10022),O=r(28559),P=r(96474),I=r(12941),q=r(52581),U=r(51215);let F="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function B(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function V(e){return"nodeType"in e}function G(e){var t,r;return e?B(e)?e:V(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function H(e){let{Document:t}=G(e);return e instanceof t}function J(e){return!B(e)&&e instanceof G(e).HTMLElement}function $(e){return e instanceof G(e).SVGElement}function W(e){return e?B(e)?e.document:V(e)?H(e)?e:J(e)||$(e)?e.ownerDocument:document:document:document}let X=F?g.useLayoutEffect:g.useEffect;function K(e){let t=(0,g.useRef)(e);return X(()=>{t.current=e}),(0,g.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function Y(e,t){void 0===t&&(t=[e]);let r=(0,g.useRef)(e);return X(()=>{r.current!==e&&(r.current=e)},t),r}function Z(e,t){let r=(0,g.useRef)();return(0,g.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function Q(e){let t=K(e),r=(0,g.useRef)(null),n=(0,g.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,n]}function ee(e){let t=(0,g.useRef)();return(0,g.useEffect)(()=>{t.current=e},[e]),t.current}let et={};function er(e,t){return(0,g.useMemo)(()=>{if(t)return t;let r=null==et[e]?0:et[e]+1;return et[e]=r,e+"-"+r},[e,t])}function en(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let ea=en(1),ei=en(-1);function el(e){if(!e)return!1;let{KeyboardEvent:t}=G(e.target);return t&&e instanceof t}function es(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=G(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let eo=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[eo.Translate.toString(e),eo.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),ec="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",ed={display:"none"};function eu(e){let{id:t,value:r}=e;return m().createElement("div",{id:t,style:ed},r)}function eh(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return m().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let ef=(0,g.createContext)(null),ep={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},eg={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function em(e){let{announcements:t=eg,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=ep}=e,{announce:i,announcement:l}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=er("DndLiveRegion"),[o,c]=(0,g.useState)(!1);(0,g.useEffect)(()=>{c(!0)},[]);var d=(0,g.useMemo)(()=>({onDragStart(e){let{active:r}=e;i(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&i(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;i(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;i(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;i(t.onDragCancel({active:r,over:n}))}}),[i,t]);let u=(0,g.useContext)(ef);if((0,g.useEffect)(()=>{if(!u)throw Error("useDndMonitor must be used within a children of <DndContext>");return u(d)},[d,u]),!o)return null;let h=m().createElement(m().Fragment,null,m().createElement(eu,{id:n,value:a.draggable}),m().createElement(eh,{id:s,announcement:l}));return r?(0,U.createPortal)(h,r):h}function ev(){}function ex(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let eb=Object.freeze({x:0,y:0});function ey(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function ew(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return r-n}function ej(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}function ek(e){let{left:t,top:r,height:n,width:a}=e;return[{x:t,y:r},{x:t+a,y:r},{x:t,y:r+n},{x:t+a,y:r+n}]}function eC(e,t){if(!e||0===e.length)return null;let[r]=e;return t?r[t]:r}function eN(e,t,r){return void 0===t&&(t=e.left),void 0===r&&(r=e.top),{x:t+.5*e.width,y:r+.5*e.height}}let eM=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=eN(t,t.left,t.top),i=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=ey(eN(n),a);i.push({id:t,data:{droppableContainer:e,value:r}})}}return i.sort(ew)},eD=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=ek(t),i=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=ek(n),l=Number((a.reduce((e,t,n)=>e+ey(r[n],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:l}})}}return i.sort(ew)},eE=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,i=r.get(n);if(i){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<i){let l=t.width*t.height,s=e.width*e.height,o=(a-n)*(i-r);return Number((o/(l+s-o)).toFixed(4))}return 0}(i,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(ej)};function eA(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:eb}let e_=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1),eS={ignoreTransform:!1};function eR(e,t){void 0===t&&(t=eS);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=G(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!n)return e;let{scaleX:a,scaleY:i,x:l,y:s}=n,o=e.left-l-(1-a)*parseFloat(r),c=e.top-s-(1-i)*parseFloat(r.slice(r.indexOf(" ")+1)),d=a?e.width/a:e.width,u=i?e.height/i:e.height;return{width:d,height:u,top:c,right:o+d,bottom:c+u,left:o}}(r,t,n))}let{top:n,left:a,width:i,height:l,bottom:s,right:o}=r;return{top:n,left:a,width:i,height:l,bottom:s,right:o}}function eT(e){return eR(e,{ignoreTransform:!0})}function ez(e,t){let r=[];return e?function n(a){var i;if(null!=t&&r.length>=t||!a)return r;if(H(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!J(a)||$(a)||r.includes(a))return r;let l=G(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=G(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,l)&&r.push(a),void 0===(i=l)&&(i=G(a).getComputedStyle(a)),"fixed"===i.position)?r:n(a.parentNode)}(e):r}function eL(e){let[t]=ez(e,1);return null!=t?t:null}function eO(e){return F&&e?B(e)?e:V(e)?H(e)||e===W(e).scrollingElement?window:J(e)?e:null:null:null}function eP(e){return B(e)?e.scrollX:e.scrollLeft}function eI(e){return B(e)?e.scrollY:e.scrollTop}function eq(e){return{x:eP(e),y:eI(e)}}function eU(e){return!!F&&!!e&&e===document.scrollingElement}function eF(e){let t={x:0,y:0},r=eU(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:a,isLeft:i,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let eB={x:.2,y:.2};function eV(e){return e.reduce((e,t)=>ea(e,eq(t)),eb)}let eG=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+eP(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+eI(t),0)}]];class eH{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=ez(t),n=eV(r);for(let[t,a,i]of(this.rect={...e},this.width=e.width,this.height=e.height,eG))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=i(r),l=n[t]-a;return this.rect[e]+l},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eJ{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function e$(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function eW(e){e.preventDefault()}function eX(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(l||(l={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(s||(s={}));let eK={start:[s.Space,s.Enter],cancel:[s.Esc],end:[s.Space,s.Enter,s.Tab]},eY=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case s.Right:return{...r,x:r.x+25};case s.Left:return{...r,x:r.x-25};case s.Down:return{...r,y:r.y+25};case s.Up:return{...r,y:r.y-25}}};class eZ{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eJ(W(t)),this.windowListeners=new eJ(G(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(l.Resize,this.handleCancel),this.windowListeners.add(l.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(l.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&function(e,t){if(void 0===t&&(t=eR),!e)return;let{top:r,left:n,bottom:a,right:i}=t(e);eL(e)&&(a<=0||i<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(r),t(eb)}handleKeyDown(e){if(el(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=eK,coordinateGetter:i=eY,scrollBehavior:l="smooth"}=n,{code:o}=e;if(a.end.includes(o))return void this.handleEnd(e);if(a.cancel.includes(o))return void this.handleCancel(e);let{collisionRect:c}=r.current,d=c?{x:c.left,y:c.top}:eb;this.referenceCoordinates||(this.referenceCoordinates=d);let u=i(e,{active:t,context:r.current,currentCoordinates:d});if(u){let t=ei(u,d),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:i,isRight:o,isLeft:c,isBottom:d,maxScroll:h,minScroll:f}=eF(r),p=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),g={x:Math.min(a===s.Right?p.right-p.width/2:p.right,Math.max(a===s.Right?p.left:p.left+p.width/2,u.x)),y:Math.min(a===s.Down?p.bottom-p.height/2:p.bottom,Math.max(a===s.Down?p.top:p.top+p.height/2,u.y))},m=a===s.Right&&!o||a===s.Left&&!c,v=a===s.Down&&!d||a===s.Up&&!i;if(m&&g.x!==u.x){let e=r.scrollLeft+t.x,i=a===s.Right&&e<=h.x||a===s.Left&&e>=f.x;if(i&&!t.y)return void r.scrollTo({left:e,behavior:l});i?n.x=r.scrollLeft-e:n.x=a===s.Right?r.scrollLeft-h.x:r.scrollLeft-f.x,n.x&&r.scrollBy({left:-n.x,behavior:l});break}if(v&&g.y!==u.y){let e=r.scrollTop+t.y,i=a===s.Down&&e<=h.y||a===s.Up&&e>=f.y;if(i&&!t.x)return void r.scrollTo({top:e,behavior:l});i?n.y=r.scrollTop-e:n.y=a===s.Down?r.scrollTop-h.y:r.scrollTop-f.y,n.y&&r.scrollBy({top:-n.y,behavior:l});break}}this.handleMove(e,ea(ei(u,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eQ(e){return!!(e&&"distance"in e)}function e0(e){return!!(e&&"delay"in e)}eZ.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=eK,onActivation:a}=t,{active:i}=r,{code:l}=e.nativeEvent;if(n.start.includes(l)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class e1{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=G(e);return e instanceof t?e:W(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:i}=a;this.props=e,this.events=t,this.document=W(i),this.documentListeners=new eJ(this.document),this.listeners=new eJ(r),this.windowListeners=new eJ(G(i)),this.initialCoordinates=null!=(n=es(a))?n:eb,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(l.Resize,this.handleCancel),this.windowListeners.add(l.DragStart,eW),this.windowListeners.add(l.VisibilityChange,this.handleCancel),this.windowListeners.add(l.ContextMenu,eW),this.documentListeners.add(l.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(e0(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eQ(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:r,onPending:n}=this.props;n(r,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(l.Click,eX,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(l.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:i,options:{activationConstraint:l}}=a;if(!n)return;let s=null!=(t=es(e))?t:eb,o=ei(n,s);if(!r&&l){if(eQ(l)){if(null!=l.tolerance&&e$(o,l.tolerance))return this.handleCancel();if(e$(o,l.distance))return this.handleStart()}return e0(l)&&e$(o,l.tolerance)?this.handleCancel():void this.handlePending(l,o)}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===s.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let e2={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class e4 extends e1{constructor(e){let{event:t}=e;super(e,e2,W(t.target))}}e4.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let e5={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(o||(o={}));class e6 extends e1{constructor(e){super(e,e5,W(e.event.target))}}e6.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==o.RightClick&&(null==n||n({event:r}),!0)}}];let e3={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class e8 extends e1{constructor(e){super(e,e3)}static setup(){return window.addEventListener(e3.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(e3.move.name,e)};function e(){}}}e8.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(c||(c={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(d||(d={}));let e7={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(u||(u={})),(h||(h={})).Optimized="optimized";let e9=new Map;function te(e,t){return Z(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function tt(e){let{callback:t,disabled:r}=e,n=K(t),a=(0,g.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,g.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function tr(e){return new eH(eR(e),e)}function tn(e,t,r){void 0===t&&(t=tr);let[n,a]=(0,g.useState)(null);function i(){a(n=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let i=t(e);return JSON.stringify(n)===JSON.stringify(i)?n:i})}let l=function(e){let{callback:t,disabled:r}=e,n=K(t),a=(0,g.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,g.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){i();break}}}}),s=tt({callback:i});return X(()=>{i(),e?(null==s||s.observe(e),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())},[e]),n}let ta=[];function ti(e,t){void 0===t&&(t=[]);let r=(0,g.useRef)(null);return(0,g.useEffect)(()=>{r.current=null},t),(0,g.useEffect)(()=>{let t=e!==eb;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?ei(e,r.current):eb}function tl(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let ts=[],to=[{sensor:e4,options:{}},{sensor:eZ,options:{}}],tc={current:{}},td={draggable:{measure:eT},droppable:{measure:eT,strategy:u.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:eR}};class tu extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let th={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new tu,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:ev},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:td,measureDroppableContainers:ev,windowRect:null,measuringScheduled:!1},tf={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:ev,draggableNodes:new Map,over:null,measureDroppableContainers:ev},tp=(0,g.createContext)(tf),tg=(0,g.createContext)(th);function tm(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new tu}}}function tv(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new tu(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,i=e.droppable.containers.get(r);if(!i||n!==i.key)return e;let l=new tu(e.droppable.containers);return l.set(r,{...i,disabled:a}),{...e,droppable:{...e.droppable,containers:l}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let i=new tu(e.droppable.containers);return i.delete(r),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function tx(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,g.useContext)(tp),i=ee(n),l=ee(null==r?void 0:r.id);return(0,g.useEffect)(()=>{if(!t&&!n&&i&&null!=l){if(!el(i)||document.activeElement===i.target)return;let e=a.get(l);if(!e)return;let{activatorNode:t,node:r}=e;(t.current||r.current)&&requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=e.matches(ec)?e:e.querySelector(ec);if(t){t.focus();break}}})}},[n,t,a,l,i]),null}let tb=(0,g.createContext)({...eb,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let ty=(0,g.memo)(function(e){var t,r,n,l,s,o;let{id:h,accessibility:p,autoScroll:v=!0,children:x,sensors:b=to,collisionDetection:y=eE,measuring:w,modifiers:j,...k}=e,[C,N]=(0,g.useReducer)(tv,void 0,tm),[M,D]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[E,A]=(0,g.useState)(f.Uninitialized),_=E===f.Initialized,{draggable:{active:S,nodes:R,translate:T},droppable:{containers:z}}=C,L=null!=S?R.get(S):null,O=(0,g.useRef)({initial:null,translated:null}),P=(0,g.useMemo)(()=>{var e;return null!=S?{id:S,data:null!=(e=null==L?void 0:L.data)?e:tc,rect:O}:null},[S,L]),I=(0,g.useRef)(null),[q,B]=(0,g.useState)(null),[V,H]=(0,g.useState)(null),$=Y(k,Object.values(k)),W=er("DndDescribedBy",h),K=(0,g.useMemo)(()=>z.getEnabled(),[z]),et=(0,g.useMemo)(()=>({draggable:{...td.draggable,...null==w?void 0:w.draggable},droppable:{...td.droppable,...null==w?void 0:w.droppable},dragOverlay:{...td.dragOverlay,...null==w?void 0:w.dragOverlay}}),[null==w?void 0:w.draggable,null==w?void 0:w.droppable,null==w?void 0:w.dragOverlay]),{droppableRects:en,measureDroppableContainers:ei,measuringScheduled:el}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[i,l]=(0,g.useState)(null),{frequency:s,measure:o,strategy:c}=a,d=(0,g.useRef)(e),h=function(){switch(c){case u.Always:return!1;case u.BeforeDragging:return r;default:return!r}}(),f=Y(h),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),f.current||l(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),m=(0,g.useRef)(null),v=Z(t=>{if(h&&!r)return e9;if(!t||t===e9||d.current!==e||null!=i){let t=new Map;for(let r of e){if(!r)continue;if(i&&i.length>0&&!i.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new eH(o(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,i,r,h,o]);return(0,g.useEffect)(()=>{d.current=e},[e]),(0,g.useEffect)(()=>{h||p()},[r,h]),(0,g.useEffect)(()=>{i&&i.length>0&&l(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{h||"number"!=typeof s||null!==m.current||(m.current=setTimeout(()=>{p(),m.current=null},s))},[s,h,p,...n]),{droppableRects:v,measureDroppableContainers:p,measuringScheduled:null!=i}}(K,{dragging:_,dependencies:[T.x,T.y],config:et.droppable}),eo=function(e,t){let r=null!=t?e.get(t):void 0,n=r?r.node.current:null;return Z(e=>{var r;return null==t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(R,S),ec=(0,g.useMemo)(()=>V?es(V):null,[V]),ed=function(){let e=(null==q?void 0:q.autoScrollEnabled)===!1,t="object"==typeof v?!1===v.enabled:!1===v,r=_&&!e&&!t;return"object"==typeof v?{...v,enabled:r}:{enabled:r}}(),eu=te(eo,et.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,i=(0,g.useRef)(!1),{x:l,y:s}="boolean"==typeof a?{x:a,y:a}:a;X(()=>{if(!l&&!s||!t){i.current=!1;return}if(i.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=eA(r(e),n);if(l||(a.x=0),s||(a.y=0),i.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=eL(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,l,s,n,r])}({activeNode:null!=S?R.get(S):null,config:ed.layoutShiftCompensation,initialRect:eu,measure:et.draggable.measure});let eh=tn(eo,et.draggable.measure,eu),ep=tn(eo?eo.parentElement:null),eg=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eo,collisionRect:null,collisions:null,droppableRects:en,draggableNodes:R,draggingNode:null,draggingNodeRect:null,droppableContainers:z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ev=z.getNodeFor(null==(t=eg.current.over)?void 0:t.id),ex=function(e){let{measure:t}=e,[r,n]=(0,g.useState)(null),a=tt({callback:(0,g.useCallback)(e=>{for(let{target:r}of e)if(J(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),[i,l]=Q((0,g.useCallback)(e=>{let r=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return J(t)?t:e}(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]));return(0,g.useMemo)(()=>({nodeRef:i,rect:r,setRef:l}),[r,i,l])}({measure:et.dragOverlay.measure}),ey=null!=(r=ex.nodeRef.current)?r:eo,ew=_?null!=(n=ex.rect)?n:eh:null,ej=!!(ex.nodeRef.current&&ex.rect),ek=function(e){let t=te(e);return eA(e,t)}(ej?null:eh),eN=tl(ey?G(ey):null),eM=function(e){let t=(0,g.useRef)(e),r=Z(r=>e?r&&r!==ta&&e&&t.current&&e.parentNode===t.current.parentNode?r:ez(e):ta,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),r}(_?null!=ev?ev:eo:null),eD=function(e,t){void 0===t&&(t=eR);let[r]=e,n=tl(r?G(r):null),[a,i]=(0,g.useState)(ts);function l(){i(()=>e.length?e.map(e=>eU(e)?n:new eH(t(e),e)):ts)}let s=tt({callback:l});return X(()=>{null==s||s.disconnect(),l(),e.forEach(e=>null==s?void 0:s.observe(e))},[e]),a}(eM),eS=function(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}(j,{transform:{x:T.x-ek.x,y:T.y-ek.y,scaleX:1,scaleY:1},activatorEvent:V,active:P,activeNodeRect:eh,containerNodeRect:ep,draggingNodeRect:ew,over:eg.current.over,overlayNodeRect:ex.rect,scrollableAncestors:eM,scrollableAncestorRects:eD,windowRect:eN}),eT=ec?ea(ec,T):null,eP=function(e){let[t,r]=(0,g.useState)(null),n=(0,g.useRef)(e),a=(0,g.useCallback)(e=>{let t=eO(e.target);t&&r(e=>e?(e.set(t,eq(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=n.current;if(e!==t){i(t);let l=e.map(e=>{let t=eO(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,eq(t)]):null}).filter(e=>null!=e);r(l.length?new Map(l):null),n.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=eO(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>ea(e,t),eb):eV(e):eb,[e,t])}(eM),eI=ti(eP),eG=ti(eP,[eh]),eJ=ea(eS,eI),e$=ew?e_(ew,eS):null,eW=P&&e$?y({active:P,collisionRect:e$,droppableRects:en,droppableContainers:K,pointerCoordinates:eT}):null,eX=eC(eW,"id"),[eK,eY]=(0,g.useState)(null),eZ=(s=ej?eS:ea(eS,eG),o=null!=(l=null==eK?void 0:eK.rect)?l:null,{...s,scaleX:o&&eh?o.width/eh.width:1,scaleY:o&&eh?o.height/eh.height:1}),eQ=(0,g.useRef)(null),e0=(0,g.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==I.current)return;let i=R.get(I.current);if(!i)return;let l=e.nativeEvent,s=new r({active:I.current,activeNode:i,event:l,options:n,context:eg,onAbort(e){if(!R.get(e))return;let{onDragAbort:t}=$.current,r={id:e};null==t||t(r),M({type:"onDragAbort",event:r})},onPending(e,t,r,n){if(!R.get(e))return;let{onDragPending:a}=$.current,i={id:e,constraint:t,initialCoordinates:r,offset:n};null==a||a(i),M({type:"onDragPending",event:i})},onStart(e){let t=I.current;if(null==t)return;let r=R.get(t);if(!r)return;let{onDragStart:n}=$.current,i={activatorEvent:l,active:{id:t,data:r.data,rect:O}};(0,U.unstable_batchedUpdates)(()=>{null==n||n(i),A(f.Initializing),N({type:a.DragStart,initialCoordinates:e,active:t}),M({type:"onDragStart",event:i}),B(eQ.current),H(l)})},onMove(e){N({type:a.DragMove,coordinates:e})},onEnd:o(a.DragEnd),onCancel:o(a.DragCancel)});function o(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:i}=eg.current,s=null;if(t&&i){let{cancelDrop:o}=$.current;s={activatorEvent:l,active:t,collisions:r,delta:i,over:n},e===a.DragEnd&&"function"==typeof o&&await Promise.resolve(o(s))&&(e=a.DragCancel)}I.current=null,(0,U.unstable_batchedUpdates)(()=>{N({type:e}),A(f.Uninitialized),eY(null),B(null),H(null),eQ.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(s){let e=$.current[t];null==e||e(s),M({type:t,event:s})}})}}eQ.current=s},[R]),e1=(0,g.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,i=R.get(n);null!==I.current||!i||a.dndKit||a.defaultPrevented||!0===e(r,t.options,{active:i})&&(a.dndKit={capturedBy:t.sensor},I.current=n,e0(r,t))},[R,e0]),e2=(0,g.useMemo)(()=>b.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:e1(e.handler,t)}))]},[]),[b,e1]);(0,g.useEffect)(()=>{if(!F)return;let e=b.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},b.map(e=>{let{sensor:t}=e;return t})),X(()=>{eh&&E===f.Initializing&&A(f.Initialized)},[eh,E]),(0,g.useEffect)(()=>{let{onDragMove:e}=$.current,{active:t,activatorEvent:r,collisions:n,over:a}=eg.current;if(!t||!r)return;let i={active:t,activatorEvent:r,collisions:n,delta:{x:eJ.x,y:eJ.y},over:a};(0,U.unstable_batchedUpdates)(()=>{null==e||e(i),M({type:"onDragMove",event:i})})},[eJ.x,eJ.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=eg.current;if(!e||null==I.current||!t||!a)return;let{onDragOver:i}=$.current,l=n.get(eX),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,o={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:s};(0,U.unstable_batchedUpdates)(()=>{eY(s),null==i||i(o),M({type:"onDragOver",event:o})})},[eX]),X(()=>{eg.current={activatorEvent:V,active:P,activeNode:eo,collisionRect:e$,collisions:eW,droppableRects:en,draggableNodes:R,draggingNode:ey,draggingNodeRect:ew,droppableContainers:z,over:eK,scrollableAncestors:eM,scrollAdjustedTranslate:eJ},O.current={initial:ew,translated:e$}},[P,eo,eW,e$,R,ey,ew,en,z,eK,eM,eJ]),function(e){let{acceleration:t,activator:r=c.Pointer,canScroll:n,draggingRect:a,enabled:l,interval:s=5,order:o=d.TreeOrder,pointerCoordinates:u,scrollableAncestors:h,scrollableAncestorRects:f,delta:p,threshold:m}=e,v=function(e){let{delta:t,disabled:r}=e,n=ee(t);return Z(e=>{if(r||!n||!e)return e7;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===a.x,[i.Forward]:e.x[i.Forward]||1===a.x},y:{[i.Backward]:e.y[i.Backward]||-1===a.y,[i.Forward]:e.y[i.Forward]||1===a.y}}},[r,t,n])}({delta:p,disabled:!l}),[x,b]=function(){let e=(0,g.useRef)(null);return[(0,g.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,g.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),y=(0,g.useRef)({x:0,y:0}),w=(0,g.useRef)({x:0,y:0}),j=(0,g.useMemo)(()=>{switch(r){case c.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case c.DraggableRect:return a}},[r,a,u]),k=(0,g.useRef)(null),C=(0,g.useCallback)(()=>{let e=k.current;if(!e)return;let t=y.current.x*w.current.x,r=y.current.y*w.current.y;e.scrollBy(t,r)},[]),N=(0,g.useMemo)(()=>o===d.TreeOrder?[...h].reverse():h,[o,h]);(0,g.useEffect)(()=>{if(!l||!h.length||!j)return void b();for(let e of N){if((null==n?void 0:n(e))===!1)continue;let r=f[h.indexOf(e)];if(!r)continue;let{direction:a,speed:l}=function(e,t,r,n,a){let{top:l,left:s,right:o,bottom:c}=r;void 0===n&&(n=10),void 0===a&&(a=eB);let{isTop:d,isBottom:u,isLeft:h,isRight:f}=eF(e),p={x:0,y:0},g={x:0,y:0},m={height:t.height*a.y,width:t.width*a.x};return!d&&l<=t.top+m.height?(p.y=i.Backward,g.y=n*Math.abs((t.top+m.height-l)/m.height)):!u&&c>=t.bottom-m.height&&(p.y=i.Forward,g.y=n*Math.abs((t.bottom-m.height-c)/m.height)),!f&&o>=t.right-m.width?(p.x=i.Forward,g.x=n*Math.abs((t.right-m.width-o)/m.width)):!h&&s<=t.left+m.width&&(p.x=i.Backward,g.x=n*Math.abs((t.left+m.width-s)/m.width)),{direction:p,speed:g}}(e,r,j,t,m);for(let e of["x","y"])v[e][a[e]]||(l[e]=0,a[e]=0);if(l.x>0||l.y>0){b(),k.current=e,x(C,s),y.current=l,w.current=a;return}}y.current={x:0,y:0},w.current={x:0,y:0},b()},[t,C,n,b,l,s,JSON.stringify(j),JSON.stringify(v),x,h,N,f,JSON.stringify(m)])}({...ed,delta:T,draggingRect:e$,pointerCoordinates:eT,scrollableAncestors:eM,scrollableAncestorRects:eD});let e4=(0,g.useMemo)(()=>({active:P,activeNode:eo,activeNodeRect:eh,activatorEvent:V,collisions:eW,containerNodeRect:ep,dragOverlay:ex,draggableNodes:R,droppableContainers:z,droppableRects:en,over:eK,measureDroppableContainers:ei,scrollableAncestors:eM,scrollableAncestorRects:eD,measuringConfiguration:et,measuringScheduled:el,windowRect:eN}),[P,eo,eh,V,eW,ep,ex,R,z,en,eK,ei,eM,eD,et,el,eN]),e5=(0,g.useMemo)(()=>({activatorEvent:V,activators:e2,active:P,activeNodeRect:eh,ariaDescribedById:{draggable:W},dispatch:N,draggableNodes:R,over:eK,measureDroppableContainers:ei}),[V,e2,P,eh,N,W,R,eK,ei]);return m().createElement(ef.Provider,{value:D},m().createElement(tp.Provider,{value:e5},m().createElement(tg.Provider,{value:e4},m().createElement(tb.Provider,{value:eZ},x)),m().createElement(tx,{disabled:(null==p?void 0:p.restoreFocus)===!1})),m().createElement(em,{...p,hiddenTextDescribedById:W}))}),tw=(0,g.createContext)(null),tj="button",tk={timeout:25};n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:i,className:l}=n;if(null!=i&&i.active)for(let[e,r]of Object.entries(i.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=l&&l.active&&t.node.classList.add(l.active),null!=l&&l.dragOverlay&&r.node.classList.add(l.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=l&&l.active&&t.node.classList.remove(l.active)}};function tC(e,t,r){let n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function tN(e){return null!==e&&e>=0}let tM=e=>{let{rects:t,activeIndex:r,overIndex:n,index:a}=e,i=tC(t,n,r),l=t[a],s=i[a];return s&&l?{x:s.left-l.left,y:s.top-l.top,scaleX:s.width/l.width,scaleY:s.height/l.height}:null},tD={scaleX:1,scaleY:1},tE=e=>{var t;let{activeIndex:r,activeNodeRect:n,index:a,rects:i,overIndex:l}=e,s=null!=(t=i[r])?t:n;if(!s)return null;if(a===r){let e=i[l];return e?{x:0,y:r<l?e.top+e.height-(s.top+s.height):e.top-s.top,...tD}:null}let o=function(e,t,r){let n=e[t],a=e[t-1],i=e[t+1];return n?r<t?a?n.top-(a.top+a.height):i?i.top-(n.top+n.height):0:i?i.top-(n.top+n.height):a?n.top-(a.top+a.height):0:0}(i,a,r);return a>r&&a<=l?{x:0,y:-s.height-o,...tD}:a<r&&a>=l?{x:0,y:s.height+o,...tD}:{x:0,y:0,...tD}},tA="Sortable",t_=m().createContext({activeIndex:-1,containerId:tA,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:tM,disabled:{draggable:!1,droppable:!1}});function tS(e){let{children:t,id:r,items:n,strategy:a=tM,disabled:i=!1}=e,{active:l,dragOverlay:s,droppableRects:o,over:c,measureDroppableContainers:d}=(0,g.useContext)(tg),u=er(tA,r),h=null!==s.rect,f=(0,g.useMemo)(()=>n.map(e=>"object"==typeof e&&"id"in e?e.id:e),[n]),p=null!=l,v=l?f.indexOf(l.id):-1,x=c?f.indexOf(c.id):-1,b=(0,g.useRef)(f),y=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(f,b.current),w=-1!==x&&-1===v||y,j="boolean"==typeof i?{draggable:i,droppable:i}:i;X(()=>{y&&p&&d(f)},[y,f,p,d]),(0,g.useEffect)(()=>{b.current=f},[f]);let k=(0,g.useMemo)(()=>({activeIndex:v,containerId:u,disabled:j,disableTransforms:w,items:f,overIndex:x,useDragOverlay:h,sortedRects:f.reduce((e,t,r)=>{let n=o.get(t);return n&&(e[r]=n),e},Array(f.length)),strategy:a}),[v,u,j.draggable,j.droppable,w,f,x,o,h,a]);return m().createElement(t_.Provider,{value:k},t)}let tR=e=>{let{id:t,items:r,activeIndex:n,overIndex:a}=e;return tC(r,n,a).indexOf(t)},tT=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:a,items:i,newIndex:l,previousItems:s,previousContainerId:o,transition:c}=e;return!!c&&!!n&&(s===i||a!==l)&&(!!r||l!==a&&t===o)},tz={duration:200,easing:"ease"},tL="transform",tO=eo.Transition.toString({property:tL,duration:0,easing:"linear"}),tP={roleDescription:"sortable"};function tI(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let tq=[s.Down,s.Right,s.Up,s.Left],tU=(e,t)=>{let{context:{active:r,collisionRect:n,droppableRects:a,droppableContainers:i,over:l,scrollableAncestors:o}}=t;if(tq.includes(e.code)){if(e.preventDefault(),!r||!n)return;let t=[];i.getEnabled().forEach(r=>{if(!r||null!=r&&r.disabled)return;let i=a.get(r.id);if(i)switch(e.code){case s.Down:n.top<i.top&&t.push(r);break;case s.Up:n.top>i.top&&t.push(r);break;case s.Left:n.left>i.left&&t.push(r);break;case s.Right:n.left<i.left&&t.push(r)}});let c=eD({active:r,collisionRect:n,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=eC(c,"id");if(d===(null==l?void 0:l.id)&&c.length>1&&(d=c[1].id),null!=d){let e=i.get(r.id),t=i.get(d),l=t?a.get(t.id):null,s=null==t?void 0:t.node.current;if(s&&l&&e&&t){let r=ez(s).some((e,t)=>o[t]!==e),a=tF(e,t),i=function(e,t){return!!tI(e)&&!!tI(t)&&!!tF(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),c=r||!a?{x:0,y:0}:{x:i?n.width-l.width:0,y:i?n.height-l.height:0},d={x:l.left,y:l.top};return c.x&&c.y?d:ei(d,c)}}}};function tF(e,t){return!!tI(e)&&!!tI(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}function tB({item:e,onEdit:t,onDelete:r,onToggleStatus:n,getTargetIcon:i,childItems:l}){let{attributes:s,listeners:o,setNodeRef:c,transform:d,transition:u,isDragging:h}=function(e){var t,r,n,i;let{animateLayoutChanges:l=tT,attributes:s,disabled:o,data:c,getNewIndex:d=tR,id:u,strategy:h,resizeObserverConfig:f,transition:p=tz}=e,{items:m,containerId:v,activeIndex:x,disabled:b,disableTransforms:y,sortedRects:w,overIndex:j,useDragOverlay:k,strategy:C}=(0,g.useContext)(t_),N=(t=o,r=b,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(n=null==t?void 0:t.draggable)?n:r.draggable,droppable:null!=(i=null==t?void 0:t.droppable)?i:r.droppable}),M=m.indexOf(u),D=(0,g.useMemo)(()=>({sortable:{containerId:v,index:M,items:m},...c}),[v,c,M,m]),E=(0,g.useMemo)(()=>m.slice(m.indexOf(u)),[m,u]),{rect:A,node:_,isOver:S,setNodeRef:R}=function(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:i}=e,l=er("Droppable"),{active:s,dispatch:o,over:c,measureDroppableContainers:d}=(0,g.useContext)(tp),u=(0,g.useRef)({disabled:r}),h=(0,g.useRef)(!1),f=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:m,updateMeasurementsFor:v,timeout:x}={...tk,...i},b=Y(null!=v?v:n),y=tt({callback:(0,g.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{d(Array.isArray(b.current)?b.current:[b.current]),p.current=null},x)},[x]),disabled:m||!s}),[w,j]=Q((0,g.useCallback)((e,t)=>{y&&(t&&(y.unobserve(t),h.current=!1),e&&y.observe(e))},[y])),k=Y(t);return(0,g.useEffect)(()=>{y&&w.current&&(y.disconnect(),h.current=!1,y.observe(w.current))},[w,y]),(0,g.useEffect)(()=>(o({type:a.RegisterDroppable,element:{id:n,key:l,disabled:r,node:w,rect:f,data:k}}),()=>o({type:a.UnregisterDroppable,key:l,id:n})),[n]),(0,g.useEffect)(()=>{r!==u.current.disabled&&(o({type:a.SetDroppableDisabled,id:n,key:l,disabled:r}),u.current.disabled=r)},[n,l,r,o]),{active:s,rect:f,isOver:(null==c?void 0:c.id)===n,node:w,over:c,setNodeRef:j}}({id:u,data:D,disabled:N.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...f}}),{active:T,activatorEvent:z,activeNodeRect:L,attributes:O,setNodeRef:P,listeners:I,isDragging:q,over:U,setActivatorNodeRef:F,transform:B}=function(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,i=er("Draggable"),{activators:l,activatorEvent:s,active:o,activeNodeRect:c,ariaDescribedById:d,draggableNodes:u,over:h}=(0,g.useContext)(tp),{role:f=tj,roleDescription:p="draggable",tabIndex:m=0}=null!=a?a:{},v=(null==o?void 0:o.id)===t,x=(0,g.useContext)(v?tb:tw),[b,y]=Q(),[w,j]=Q(),k=(0,g.useMemo)(()=>l.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[l,t]),C=Y(r);return X(()=>(u.set(t,{id:t,key:i,node:b,activatorNode:w,data:C}),()=>{let e=u.get(t);e&&e.key===i&&u.delete(t)}),[u,t]),{active:o,activatorEvent:s,activeNodeRect:c,attributes:(0,g.useMemo)(()=>({role:f,tabIndex:m,"aria-disabled":n,"aria-pressed":!!v&&f===tj||void 0,"aria-roledescription":p,"aria-describedby":d.draggable}),[n,f,m,v,p,d.draggable]),isDragging:v,listeners:n?void 0:k,node:b,over:h,setNodeRef:y,setActivatorNodeRef:j,transform:x}}({id:u,data:D,attributes:{...tP,...s},disabled:N.draggable}),V=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,g.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(R,P),G=!!T,H=G&&!y&&tN(x)&&tN(j),J=!k&&q,$=J&&H?B:null,W=H?null!=$?$:(null!=h?h:C)({rects:w,activeNodeRect:L,activeIndex:x,overIndex:j,index:M}):null,K=tN(x)&&tN(j)?d({id:u,items:m,activeIndex:x,overIndex:j}):M,Z=null==T?void 0:T.id,ee=(0,g.useRef)({activeId:Z,items:m,newIndex:K,containerId:v}),et=m!==ee.current.items,en=l({active:T,containerId:v,isDragging:q,isSorting:G,id:u,index:M,items:m,newIndex:ee.current.newIndex,previousItems:ee.current.items,previousContainerId:ee.current.containerId,transition:p,wasDragging:null!=ee.current.activeId}),ea=function(e){let{disabled:t,index:r,node:n,rect:a}=e,[i,l]=(0,g.useState)(null),s=(0,g.useRef)(r);return X(()=>{if(!t&&r!==s.current&&n.current){let e=a.current;if(e){let t=eR(n.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&l(r)}}r!==s.current&&(s.current=r)},[t,r,n,a]),(0,g.useEffect)(()=>{i&&l(null)},[i]),i}({disabled:!en,index:M,node:_,rect:A});return(0,g.useEffect)(()=>{G&&ee.current.newIndex!==K&&(ee.current.newIndex=K),v!==ee.current.containerId&&(ee.current.containerId=v),m!==ee.current.items&&(ee.current.items=m)},[G,K,v,m]),(0,g.useEffect)(()=>{if(Z===ee.current.activeId)return;if(null!=Z&&null==ee.current.activeId){ee.current.activeId=Z;return}let e=setTimeout(()=>{ee.current.activeId=Z},50);return()=>clearTimeout(e)},[Z]),{active:T,activeIndex:x,attributes:O,data:D,rect:A,index:M,newIndex:K,items:m,isOver:S,isSorting:G,isDragging:q,listeners:I,node:_,overIndex:j,over:U,setNodeRef:V,setActivatorNodeRef:F,setDroppableNodeRef:R,setDraggableNodeRef:P,transform:null!=ea?ea:W,transition:ea||et&&ee.current.newIndex===M?tO:(!J||el(z))&&p&&(G||en)?eo.Transition.toString({...p,property:tL}):void 0}}({id:e.id}),f={transform:eo.Transform.toString(d),transition:u,opacity:h?.5:1};return(0,p.jsxs)("div",{ref:c,style:f,className:"space-y-2",children:[(0,p.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,p.jsxs)("div",{className:"flex items-center gap-4",children:[(0,p.jsx)("div",{...s,...o,className:"cursor-grab active:cursor-grabbing",children:(0,p.jsx)(E,{className:"h-4 w-4 text-gray-400"})}),(0,p.jsxs)("div",{className:"flex items-center gap-2",children:[i(e.target_type),(0,p.jsx)("span",{className:"font-medium arabic-text",children:e.title_ar}),!e.is_active&&(0,p.jsx)(w.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]})]}),(0,p.jsxs)("div",{className:"flex items-center gap-2",children:[(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>n(e),children:e.is_active?(0,p.jsx)(A.A,{className:"h-4 w-4"}):(0,p.jsx)(_.A,{className:"h-4 w-4"})}),(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>t(e),children:(0,p.jsx)(S.A,{className:"h-4 w-4"})}),(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>r(e.id),children:(0,p.jsx)(R.A,{className:"h-4 w-4"})})]})]}),l.map(e=>(0,p.jsxs)("div",{className:"mr-8 flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,p.jsxs)("div",{className:"flex items-center gap-2",children:[i(e.target_type),(0,p.jsx)("span",{className:"arabic-text",children:e.title_ar}),!e.is_active&&(0,p.jsx)(w.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]}),(0,p.jsxs)("div",{className:"flex items-center gap-2",children:[(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>n(e),children:e.is_active?(0,p.jsx)(A.A,{className:"h-4 w-4"}):(0,p.jsx)(_.A,{className:"h-4 w-4"})}),(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>t(e),children:(0,p.jsx)(S.A,{className:"h-4 w-4"})}),(0,p.jsx)(b.$,{variant:"ghost",size:"sm",onClick:()=>r(e.id),children:(0,p.jsx)(R.A,{className:"h-4 w-4"})})]})]},e.id))]})}function tV(){let{user:e,profile:t}=(0,v.A)(),[r,n]=(0,g.useState)([]),[a,i]=(0,g.useState)(!0),[l,s]=(0,g.useState)(!1),[o,c]=(0,g.useState)(null),[d,u]=(0,g.useState)({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),h=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(ex(e4),ex(eZ,{coordinateGetter:tU})),f=async()=>{try{i(!0);let e=await fetch("/api/menu-items?include_inactive=true"),t=await e.json();e.ok?n(t.menuItems||[]):q.o.error(t.error||"فشل في جلب عناصر القائمة")}catch(e){console.error("Error fetching menu items:",e),q.o.error("خطأ في الاتصال بالخادم")}finally{i(!1)}},m=()=>{u({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),c(null)},w=e=>{c(e),u({title_ar:e.title_ar,title_en:e.title_en||"",title_fr:e.title_fr||"",slug:e.slug,icon:e.icon||"",parent_id:e.parent_id||"",target_type:e.target_type,target_value:e.target_value,is_active:e.is_active}),s(!0)},E=async()=>{try{let e=o?`/api/menu-items/${o.id}`:"/api/menu-items",t=o?"PUT":"POST",r=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify({...d,parent_id:"none"===d.parent_id?null:d.parent_id||null})}),n=await r.json();r.ok?(q.o.success(n.message),s(!1),m(),f()):q.o.error(n.error||"فشل في حفظ عنصر القائمة")}catch(e){console.error("Error saving menu item:",e),q.o.error("خطأ في الاتصال بالخادم")}},S=async e=>{if(confirm("هل أنت متأكد من حذف هذا العنصر؟"))try{let t=await fetch(`/api/menu-items/${e}`,{method:"DELETE"}),r=await t.json();t.ok?(q.o.success(r.message),f()):q.o.error(r.error||"فشل في حذف عنصر القائمة")}catch(e){console.error("Error deleting menu item:",e),q.o.error("خطأ في الاتصال بالخادم")}},R=async e=>{try{let t=await fetch(`/api/menu-items/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,is_active:!e.is_active})}),r=await t.json();t.ok?(q.o.success(r.message),f()):q.o.error(r.error||"فشل في تحديث حالة العنصر")}catch(e){console.error("Error toggling item status:",e),q.o.error("خطأ في الاتصال بالخادم")}},U=async e=>{try{let t=r.map(async t=>t.is_active!==e?(await fetch(`/api/menu-items/${t.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,is_active:e})})).json():null);await Promise.all(t),q.o.success(e?"تم تفعيل جميع عناصر القائمة":"تم إلغاء تفعيل جميع عناصر القائمة"),f()}catch(e){console.error("Error toggling all items:",e),q.o.error("خطأ في تحديث عناصر القائمة")}},F=async e=>{let{active:t,over:a}=e;if(!a||t.id===a.id)return;let i=B.findIndex(e=>e.id===t.id),l=B.findIndex(e=>e.id===a.id);if(-1===i||-1===l)return;let s=tC(B,i,l);[...r],n([...s,...r.filter(e=>e.parent_id)]);try{let e=s.map((e,t)=>({id:e.id,order_index:t+1})),t=await fetch("/api/menu-items/reorder",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:e})}),a=await t.json();t.ok?(q.o.success(a.message),f()):(q.o.error(a.error||"فشل في تحديث الترتيب"),n(r))}catch(e){console.error("Error reordering items:",e),q.o.error("خطأ في الاتصال بالخادم"),n(r)}},B=r.filter(e=>!e.parent_id),V=e=>r.filter(t=>t.parent_id===e),G=e=>{switch(e){case"internal":default:return(0,p.jsx)(T.A,{className:"h-4 w-4"});case"external":return(0,p.jsx)(z.A,{className:"h-4 w-4"});case"page":return(0,p.jsx)(L.A,{className:"h-4 w-4"})}};return e&&t&&"admin"===t.role?(0,p.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,p.jsx)(x.V,{}),(0,p.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,p.jsxs)("div",{className:"mb-8",children:[(0,p.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,p.jsx)(b.$,{variant:"outline",size:"sm",asChild:!0,children:(0,p.jsxs)("a",{href:"/dashboard/admin",children:[(0,p.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة القائمة الرئيسية \uD83D\uDDC2️"}),(0,p.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تحكم في عناصر القائمة الرئيسية وترتيبها"})]}),(0,p.jsxs)("div",{className:"flex gap-3",children:[(0,p.jsx)(M.lG,{open:l,onOpenChange:s,children:(0,p.jsx)(M.zM,{asChild:!0,children:(0,p.jsxs)(b.$,{onClick:m,children:[(0,p.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"إضافة عنصر جديد"]})})}),(0,p.jsxs)(b.$,{variant:"outline",onClick:()=>U(!0),className:"text-green-600 hover:text-green-700",children:[(0,p.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"تفعيل الكل"]}),(0,p.jsxs)(b.$,{variant:"outline",onClick:()=>U(!1),className:"text-red-600 hover:text-red-700",children:[(0,p.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"إلغاء تفعيل الكل"]})]}),(0,p.jsx)(M.lG,{open:l,onOpenChange:s,children:(0,p.jsxs)(M.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,p.jsxs)(M.c7,{children:[(0,p.jsx)(M.L3,{className:"arabic-text",children:o?"تحرير عنصر القائمة":"إضافة عنصر قائمة جديد"}),(0,p.jsx)(M.rr,{className:"arabic-text",children:"املأ البيانات أدناه لإنشاء أو تحديث عنصر القائمة"})]}),(0,p.jsxs)(D.tU,{defaultValue:"basic",className:"w-full",children:[(0,p.jsxs)(D.j7,{className:"grid w-full grid-cols-2",children:[(0,p.jsx)(D.Xi,{value:"basic",className:"arabic-text",children:"البيانات الأساسية"}),(0,p.jsx)(D.Xi,{value:"translations",className:"arabic-text",children:"الترجمات"})]}),(0,p.jsxs)(D.av,{value:"basic",className:"space-y-4",children:[(0,p.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"title_ar",className:"arabic-text",children:"العنوان بالعربية *"}),(0,p.jsx)(j.p,{id:"title_ar",value:d.title_ar,onChange:e=>u({...d,title_ar:e.target.value}),placeholder:"مثال: الرئيسية",className:"arabic-text"})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,p.jsx)(j.p,{id:"slug",value:d.slug,onChange:e=>u({...d,slug:e.target.value}),placeholder:"مثال: home"})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"target_type",className:"arabic-text",children:"نوع الهدف *"}),(0,p.jsxs)(C.l6,{value:d.target_type,onValueChange:e=>u({...d,target_type:e}),children:[(0,p.jsx)(C.bq,{children:(0,p.jsx)(C.yv,{})}),(0,p.jsxs)(C.gC,{children:[(0,p.jsx)(C.eb,{value:"internal",children:"رابط داخلي"}),(0,p.jsx)(C.eb,{value:"external",children:"رابط خارجي"}),(0,p.jsx)(C.eb,{value:"page",children:"صفحة ديناميكية"})]})]})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"target_value",className:"arabic-text",children:"قيمة الهدف *"}),(0,p.jsx)(j.p,{id:"target_value",value:d.target_value,onChange:e=>u({...d,target_value:e.target.value}),placeholder:"internal"===d.target_type?"/catalog":"external"===d.target_type?"https://example.com":"معرف الصفحة"})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة (Lucide)"}),(0,p.jsx)(j.p,{id:"icon",value:d.icon,onChange:e=>u({...d,icon:e.target.value}),placeholder:"مثال: Home"})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"parent_id",className:"arabic-text",children:"العنصر الأب"}),(0,p.jsxs)(C.l6,{value:d.parent_id,onValueChange:e=>u({...d,parent_id:e}),children:[(0,p.jsx)(C.bq,{children:(0,p.jsx)(C.yv,{placeholder:"اختر العنصر الأب (اختياري)"})}),(0,p.jsxs)(C.gC,{children:[(0,p.jsx)(C.eb,{value:"none",children:"بدون عنصر أب"}),B.map(e=>(0,p.jsx)(C.eb,{value:e.id,children:e.title_ar},e.id))]})]})]})]}),(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsx)(N.d,{id:"is_active",checked:d.is_active,onCheckedChange:e=>u({...d,is_active:e})}),(0,p.jsx)(k.J,{htmlFor:"is_active",className:"arabic-text",children:"مفعل"})]})]}),(0,p.jsxs)(D.av,{value:"translations",className:"space-y-4",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"title_en",className:"arabic-text",children:"العنوان بالإنجليزية"}),(0,p.jsx)(j.p,{id:"title_en",value:d.title_en,onChange:e=>u({...d,title_en:e.target.value}),placeholder:"Example: Home"})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)(k.J,{htmlFor:"title_fr",className:"arabic-text",children:"العنوان بالفرنسية"}),(0,p.jsx)(j.p,{id:"title_fr",value:d.title_fr,onChange:e=>u({...d,title_fr:e.target.value}),placeholder:"Exemple: Accueil"})]})]})]}),(0,p.jsxs)(M.Es,{children:[(0,p.jsx)(b.$,{variant:"outline",onClick:()=>s(!1),children:"إلغاء"}),(0,p.jsx)(b.$,{onClick:E,children:o?"تحديث":"إضافة"})]})]})})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,p.jsx)(y.Zp,{children:(0,p.jsx)(y.Wu,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي العناصر"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:r.length})]}),(0,p.jsx)(I.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,p.jsx)(y.Zp,{children:(0,p.jsx)(y.Wu,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المفعلة"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-green-600",children:r.filter(e=>e.is_active).length})]}),(0,p.jsx)(_.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,p.jsx)(y.Zp,{children:(0,p.jsx)(y.Wu,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المعطلة"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-red-600",children:r.filter(e=>!e.is_active).length})]}),(0,p.jsx)(A.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,p.jsx)(y.Zp,{children:(0,p.jsx)(y.Wu,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"القوائم الفرعية"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:r.filter(e=>e.parent_id).length})]}),(0,p.jsx)(O.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,p.jsxs)(y.Zp,{children:[(0,p.jsxs)(y.aR,{children:[(0,p.jsx)(y.ZB,{className:"arabic-text",children:"عناصر القائمة الحالية"}),(0,p.jsx)(y.BT,{className:"arabic-text",children:"إدارة وترتيب عناصر القائمة الرئيسية"})]}),(0,p.jsx)(y.Wu,{children:a?(0,p.jsxs)("div",{className:"text-center py-8",children:[(0,p.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,p.jsx)("p",{className:"mt-2 text-gray-600 arabic-text",children:"جاري التحميل..."})]}):0===r.length?(0,p.jsxs)("div",{className:"text-center py-8",children:[(0,p.jsx)(I.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,p.jsx)("p",{className:"text-gray-600 arabic-text",children:"لا توجد عناصر قائمة"})]}):(0,p.jsx)(ty,{sensors:h,collisionDetection:eM,onDragEnd:F,children:(0,p.jsx)(tS,{items:B.map(e=>e.id),strategy:tE,children:(0,p.jsx)("div",{className:"space-y-2",children:B.map(e=>(0,p.jsx)(tB,{item:e,onEdit:w,onDelete:S,onToggleStatus:R,getTargetIcon:G,childItems:V(e.id)},e.id))})})})})]})]})]}):null}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22099:(e,t,r)=>{Promise.resolve().then(r.bind(r,33473))},25334:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\menu-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40945:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("check-check",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},47342:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var n=r(60687);r(43210);var a=r(90270),i=r(4780);function l({className:e,...t}){return(0,n.jsx)(a.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,n.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},55146:(e,t,r)=>{"use strict";r.d(t,{B8:()=>A,UC:()=>S,bL:()=>E,l9:()=>_});var n=r(43210),a=r(70569),i=r(11273),l=r(72942),s=r(46059),o=r(14163),c=r(43),d=r(65551),u=r(96963),h=r(60687),f="Tabs",[p,g]=(0,i.A)(f,[l.RG]),m=(0,l.RG)(),[v,x]=p(f),b=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:i,orientation:l="horizontal",dir:s,activationMode:p="automatic",...g}=e,m=(0,c.jH)(s),[x,b]=(0,d.i)({prop:n,onChange:a,defaultProp:i??"",caller:f});return(0,h.jsx)(v,{scope:r,baseId:(0,u.B)(),value:x,onValueChange:b,orientation:l,dir:m,activationMode:p,children:(0,h.jsx)(o.sG.div,{dir:m,"data-orientation":l,...g,ref:t})})});b.displayName=f;var y="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,i=x(y,r),s=m(r);return(0,h.jsx)(l.bL,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:n,children:(0,h.jsx)(o.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});w.displayName=y;var j="TabsTrigger",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...s}=e,c=x(j,r),d=m(r),u=M(c.baseId,n),f=D(c.baseId,n),p=n===c.value;return(0,h.jsx)(l.q7,{asChild:!0,...d,focusable:!i,active:p,children:(0,h.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...s,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(n)})})})});k.displayName=j;var C="TabsContent",N=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:i,children:l,...c}=e,d=x(C,r),u=M(d.baseId,a),f=D(d.baseId,a),p=a===d.value,g=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(s.C,{present:i||p,children:({present:r})=>(0,h.jsx)(o.sG.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:r&&l})})});function M(e,t){return`${e}-trigger-${t}`}function D(e,t){return`${e}-content-${t}`}N.displayName=C;var E=b,A=w,_=k,S=N},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62369:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var n=r(43210),a=r(14163),i=r(60687),l="horizontal",s=["horizontal","vertical"],o=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:o=l,...c}=e,d=(r=o,s.includes(r))?o:l;return(0,i.jsx)(a.sG.div,{"data-orientation":d,...n?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});o.displayName="Separator";var c=o},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>f,L3:()=>p,c7:()=>h,lG:()=>s,rr:()=>g,zM:()=>o});var n=r(60687);r(43210);var a=r(26134),i=r(11860),l=r(4780);function s({...e}){return(0,n.jsx)(a.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,n.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,n.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,n.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:r=!0,...s}){return(0,n.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,n.jsx)(d,{}),(0,n.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,r&&(0,n.jsxs)(a.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(i.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,n.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...t})}function g({className:e,...t}){return(0,n.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}},67760:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},71057:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(60687);r(43210);var a=r(78148),i=r(4780);function l({className:e,...t}){return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},82347:(e,t,r)=>{Promise.resolve().then(r.bind(r,18629))},84027:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85763:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>o,av:()=>c,j7:()=>s,tU:()=>l});var n=r(60687);r(43210);var a=r(55146),i=r(4780);function l({className:e,...t}){return(0,n.jsx)(a.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...t})}function s({className:e,...t}){return(0,n.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function o({className:e,...t}){return(0,n.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function c({className:e,...t}){return(0,n.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...t})}},88059:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96882:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97051:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},98971:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,8773,1345,3044,5336,2884],()=>r(5406));module.exports=n})();