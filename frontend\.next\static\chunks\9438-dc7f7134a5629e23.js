"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9438],{13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},35169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},48021:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},50402:(e,t,n)=>{n.d(t,{JR:()=>E,_G:()=>c,be:()=>a,gB:()=>f,gl:()=>w});var r=n(12115),l=n(75143),i=n(78266);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",h=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function f(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:f,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),b=(0,i.YG)(d,n),y=null!==f.rect,m=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?m.indexOf(c.id):-1,D=p?m.indexOf(p.id):-1,E=(0,r.useRef)(m),C=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(m,E.current),M=-1!==D&&-1===x||C,k="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{C&&w&&v(m)},[C,m,w,v]),(0,r.useEffect)(()=>{E.current=m},[m]);let R=(0,r.useMemo)(()=>({activeIndex:x,containerId:b,disabled:k,disableTransforms:M,items:m,overIndex:D,useDragOverlay:y,sortedRects:m.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(m.length)),strategy:o}),[x,b,k.draggable,k.droppable,M,m,D,g,y,o]);return r.createElement(h.Provider,{value:R},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},b="transform",y=i.Ks.Transition.toString({property:b,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function w(e){var t,n,a,u;let{animateLayoutChanges:s=p,attributes:c,disabled:d,data:f,getNewIndex:w=g,id:x,strategy:D,resizeObserverConfig:E,transition:C=v}=e,{items:M,containerId:k,activeIndex:R,disabled:S,disableTransforms:T,sortedRects:L,overIndex:O,useDragOverlay:A,strategy:N}=(0,r.useContext)(h),I=(t=d,n=S,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),z=M.indexOf(x),j=(0,r.useMemo)(()=>({sortable:{containerId:k,index:z,items:M},...f}),[k,f,z,M]),P=(0,r.useMemo)(()=>M.slice(M.indexOf(x)),[M,x]),{rect:B,node:K,isOver:F,setNodeRef:W}=(0,l.zM)({id:x,data:j,disabled:I.droppable,resizeObserverConfig:{updateMeasurementsFor:P,...E}}),{active:Y,activatorEvent:G,activeNodeRect:U,attributes:_,setNodeRef:X,listeners:V,isDragging:q,over:H,setActivatorNodeRef:J,transform:Q}=(0,l.PM)({id:x,data:j,attributes:{...m,...c},disabled:I.draggable}),Z=(0,i.jn)(W,X),$=!!Y,ee=$&&!T&&o(R)&&o(O),et=!A&&q,en=et&&ee?Q:null,er=ee?null!=en?en:(null!=D?D:N)({rects:L,activeNodeRect:U,activeIndex:R,overIndex:O,index:z}):null,el=o(R)&&o(O)?w({id:x,items:M,activeIndex:R,overIndex:O}):z,ei=null==Y?void 0:Y.id,ea=(0,r.useRef)({activeId:ei,items:M,newIndex:el,containerId:k}),eo=M!==ea.current.items,eu=s({active:Y,containerId:k,isDragging:q,isSorting:$,id:x,index:z,items:M,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:C,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:z,node:K,rect:B});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),k!==ea.current.containerId&&(ea.current.containerId=k),M!==ea.current.items&&(ea.current.items=M)},[$,el,k,M]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(null!=ei&&null==ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:Y,activeIndex:R,attributes:_,data:j,rect:B,index:z,newIndex:el,items:M,isOver:F,isSorting:$,isDragging:q,listeners:V,node:K,overIndex:O,over:H,setNodeRef:Z,setActivatorNodeRef:J,setDroppableNodeRef:W,setDraggableNodeRef:X,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===z?y:(!et||(0,i.kx)(G))&&C&&($||eu)?i.Ks.Transition.toString({...C,property:b}):void 0}}function x(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let D=[l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left],E=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:o,over:u,scrollableAncestors:s}}=t;if(D.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];o.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=a.get(n.id);if(i)switch(e.code){case l.vL.Down:r.top<i.top&&t.push(n);break;case l.vL.Up:r.top>i.top&&t.push(n);break;case l.vL.Left:r.left>i.left&&t.push(n);break;case l.vL.Right:r.left<i.left&&t.push(n)}});let c=(0,l.y$)({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=(0,l.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=o.get(n.id),t=o.get(d),u=t?a.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,l.sl)(c).some((e,t)=>s[t]!==e),a=C(e,t),o=function(e,t){return!!x(e)&&!!x(t)&&!!C(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:o?r.width-u.width:0,y:o?r.height-u.height:0},h={x:u.left,y:u.top};return d.x&&d.y?h:(0,i.Re)(h,d)}}}};function C(e,t){return!!x(e)&&!!x(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},60704:(e,t,n)=>{n.d(t,{B8:()=>T,UC:()=>O,bL:()=>S,l9:()=>L});var r=n(12115),l=n(85185),i=n(46081),a=n(89196),o=n(28905),u=n(63655),s=n(94315),c=n(5845),d=n(61285),h=n(95155),f="Tabs",[g,p]=(0,i.A)(f,[a.RG]),v=(0,a.RG)(),[b,y]=g(f),m=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:l,defaultValue:i,orientation:a="horizontal",dir:o,activationMode:g="automatic",...p}=e,v=(0,s.jH)(o),[y,m]=(0,c.i)({prop:r,onChange:l,defaultProp:null!=i?i:"",caller:f});return(0,h.jsx)(b,{scope:n,baseId:(0,d.B)(),value:y,onValueChange:m,orientation:a,dir:v,activationMode:g,children:(0,h.jsx)(u.sG.div,{dir:v,"data-orientation":a,...p,ref:t})})});m.displayName=f;var w="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...l}=e,i=y(w,n),o=v(n);return(0,h.jsx)(a.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:r,children:(0,h.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...l,ref:t})})});x.displayName=w;var D="TabsTrigger",E=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...o}=e,s=y(D,n),c=v(n),d=k(s.baseId,r),f=R(s.baseId,r),g=r===s.value;return(0,h.jsx)(a.q7,{asChild:!0,...c,focusable:!i,active:g,children:(0,h.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":g,"aria-controls":f,"data-state":g?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...o,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;g||i||!e||s.onValueChange(r)})})})});E.displayName=D;var C="TabsContent",M=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:l,forceMount:i,children:a,...s}=e,c=y(C,n),d=k(c.baseId,l),f=R(c.baseId,l),g=l===c.value,p=r.useRef(g);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(o.C,{present:i||g,children:n=>{let{present:r}=n;return(0,h.jsx)(u.sG.div,{"data-state":g?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:f,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&a})}})});function k(e,t){return"".concat(e,"-trigger-").concat(t)}function R(e,t){return"".concat(e,"-content-").concat(t)}M.displayName=C;var S=m,T=x,L=E,O=M},74783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},75143:(e,t,n)=>{let r;n.d(t,{Mp:()=>eB,vL:()=>o,uN:()=>ea,AN:()=>ed,fp:()=>I,y$:()=>z,Sj:()=>F,Vy:()=>A,sl:()=>Y,fF:()=>eY,PM:()=>eW,zM:()=>eU,MS:()=>M,FR:()=>k});var l,i,a,o,u,s,c,d,h,f,g=n(12115),p=n(47650),v=n(78266);let b={display:"none"};function y(e){let{id:t,value:n}=e;return g.createElement("div",{id:t,style:b},n)}function m(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,g.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},D={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function E(e){let{announcements:t=D,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(w);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let h=g.createElement(g.Fragment,null,g.createElement(y,{id:r,value:l.draggable}),g.createElement(m,{id:o,announcement:a}));return n?(0,p.createPortal)(h,n):h}function C(){}function M(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function k(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let R=Object.freeze({x:0,y:0});function S(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function T(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function L(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function O(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function A(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function N(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=N(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=S(N(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(T)},z=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=O(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=O(r),a=Number((l.reduce((e,t,r)=>e+S(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(T)},j=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(L)};function P(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:R}let B=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),K={ignoreTransform:!1};function F(e,t){void 0===t&&(t=K);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function W(e){return F(e,{ignoreTransform:!0})}function Y(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function G(e){let[t]=Y(e,1);return null!=t?t:null}function U(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function _(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function X(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function V(e){return{x:_(e),y:X(e)}}function q(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function H(e){let t={x:0,y:0},n=q(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let J={x:.2,y:.2};function Q(e){return e.reduce((e,t)=>(0,v.WQ)(e,V(t)),R)}let Z=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+_(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+X(t),0)}]];class ${constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=Y(t),r=Q(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,Z))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ee{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function et(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function en(e){e.preventDefault()}function er(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let el={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ei=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class ea{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new ee((0,v.TW)(t)),this.windowListeners=new ee((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=F),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);G(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(R)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=el,coordinateGetter:i=ei,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:R;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:h,minScroll:f}=H(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,b=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=h.x||l===o.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(b&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=h.y||l===o.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eo(e){return!!(e&&"distance"in e)}function eu(e){return!!(e&&"delay"in e)}ea.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=el,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class es{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new ee(this.document),this.listeners=new ee(n),this.windowListeners=new ee((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:R,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,en),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,en),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eu(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eo(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,er,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:R,u=(0,v.Re)(r,o);if(!n&&a){if(eo(a)){if(null!=a.tolerance&&et(u,a.tolerance))return this.handleCancel();if(et(u,a.distance))return this.handleStart()}return eu(a)&&et(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ec={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ed extends es{constructor(e){let{event:t}=e;super(e,ec,(0,v.TW)(t.target))}}ed.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eh={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class ef extends es{constructor(e){super(e,eh,(0,v.TW)(e.event.target))}}ef.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let eg={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ep extends es{constructor(e){super(e,eg)}static setup(){return window.addEventListener(eg.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eg.move.name,e)};function e(){}}}ep.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ev={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(h||(h={})).Optimized="optimized";let eb=new Map;function ey(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function em(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ew(e){return new $(F(e),e)}function ex(e,t,n){void 0===t&&(t=ew);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=em({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eD=[];function eE(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==R;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):R}function eC(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eM=[],ek=[{sensor:ed,options:{}},{sensor:ea,options:{}}],eR={current:{}},eS={draggable:{measure:W},droppable:{measure:W,strategy:d.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:F}};class eT extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eL={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eT,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:C},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eS,measureDroppableContainers:C,windowRect:null,measuringScheduled:!1},eO={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:C,draggableNodes:new Map,over:null,measureDroppableContainers:C},eA=(0,g.createContext)(eO),eN=(0,g.createContext)(eL);function eI(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eT}}}function ez(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eT(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eT(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eT(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function ej(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(eA),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let eP=(0,g.createContext)({...R,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let eB=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:h,accessibility:b,autoScroll:y=!0,children:m,sensors:x=ek,collisionDetection:D=j,measuring:C,modifiers:M,...k}=e,[S,T]=(0,g.useReducer)(ez,void 0,eI),[L,O]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[N,I]=(0,g.useState)(f.Uninitialized),z=N===f.Initialized,{draggable:{active:K,nodes:W,translate:_},droppable:{containers:X}}=S,Z=null!=K?W.get(K):null,ee=(0,g.useRef)({initial:null,translated:null}),et=(0,g.useMemo)(()=>{var e;return null!=K?{id:K,data:null!=(e=null==Z?void 0:Z.data)?e:eR,rect:ee}:null},[K,Z]),en=(0,g.useRef)(null),[er,el]=(0,g.useState)(null),[ei,ea]=(0,g.useState)(null),eo=(0,v.YN)(k,Object.values(k)),eu=(0,v.YG)("DndDescribedBy",h),es=(0,g.useMemo)(()=>X.getEnabled(),[X]),ec=(0,g.useMemo)(()=>({draggable:{...eS.draggable,...null==C?void 0:C.draggable},droppable:{...eS.droppable,...null==C?void 0:C.droppable},dragOverlay:{...eS.dragOverlay,...null==C?void 0:C.dragOverlay}}),[null==C?void 0:C.draggable,null==C?void 0:C.droppable,null==C?void 0:C.dragOverlay]),{droppableRects:ed,measureDroppableContainers:eh,measuringScheduled:ef}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),h=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),f=(0,v.YN)(h),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),f.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),b=(0,g.useRef)(null),y=(0,v.KG)(t=>{if(h&&!n)return eb;if(!t||t===eb||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new $(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,h,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{h||p()},[n,h]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{h||"number"!=typeof o||null!==b.current||(b.current=setTimeout(()=>{p(),b.current=null},o))},[o,h,p,...r]),{droppableRects:y,measureDroppableContainers:p,measuringScheduled:null!=i}}(es,{dragging:z,dependencies:[_.x,_.y],config:ec.droppable}),eg=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(W,K),ep=(0,g.useMemo)(()=>ei?(0,v.e_)(ei):null,[ei]),ew=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=z&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),eT=ey(eg,ec.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=P(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=G(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=K?W.get(K):null,config:ew.layoutShiftCompensation,initialRect:eT,measure:ec.draggable.measure});let eL=ex(eg,ec.draggable.measure,eT),eO=ex(eg?eg.parentElement:null),eB=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eg,collisionRect:null,collisions:null,droppableRects:ed,draggableNodes:W,draggingNode:null,draggingNodeRect:null,droppableContainers:X,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eK=X.getNodeFor(null==(t=eB.current.over)?void 0:t.id),eF=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=em({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:ec.dragOverlay.measure}),eW=null!=(n=eF.nodeRef.current)?n:eg,eY=z?null!=(r=eF.rect)?r:eL:null,eG=!!(eF.nodeRef.current&&eF.rect),eU=function(e){let t=ey(e);return P(e,t)}(eG?null:eL),e_=eC(eW?(0,v.zk)(eW):null),eX=function(e){let t=(0,g.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==eD&&e&&t.current&&e.parentNode===t.current.parentNode?n:Y(e):eD,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(z?null!=eK?eK:eg:null),eV=function(e,t){void 0===t&&(t=F);let[n]=e,r=eC(n?(0,v.zk)(n):null),[l,i]=(0,g.useState)(eM);function a(){i(()=>e.length?e.map(e=>q(e)?r:new $(t(e),e)):eM)}let o=em({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eX),eq=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(M,{transform:{x:_.x-eU.x,y:_.y-eU.y,scaleX:1,scaleY:1},activatorEvent:ei,active:et,activeNodeRect:eL,containerNodeRect:eO,draggingNodeRect:eY,over:eB.current.over,overlayNodeRect:eF.rect,scrollableAncestors:eX,scrollableAncestorRects:eV,windowRect:e_}),eH=ep?(0,v.WQ)(ep,_):null,eJ=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=U(e.target);t&&n(e=>e?(e.set(t,V(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=U(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,V(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=U(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),R):Q(e):R,[e,t])}(eX),eQ=eE(eJ),eZ=eE(eJ,[eL]),e$=(0,v.WQ)(eq,eQ),e0=eY?B(eY,eq):null,e1=et&&e0?D({active:et,collisionRect:e0,droppableRects:ed,droppableContainers:es,pointerCoordinates:eH}):null,e2=A(e1,"id"),[e5,e4]=(0,g.useState)(null),e6=(o=eG?eq:(0,v.WQ)(eq,eZ),u=null!=(a=null==e5?void 0:e5.rect)?a:null,{...o,scaleX:u&&eL?u.width/eL.width:1,scaleY:u&&eL?u.height/eL.height:1}),e7=(0,g.useRef)(null),e9=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==en.current)return;let i=W.get(en.current);if(!i)return;let a=e.nativeEvent,o=new n({active:en.current,activeNode:i,event:a,options:r,context:eB,onAbort(e){if(!W.get(e))return;let{onDragAbort:t}=eo.current,n={id:e};null==t||t(n),L({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!W.get(e))return;let{onDragPending:l}=eo.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),L({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let n=W.get(t);if(!n)return;let{onDragStart:r}=eo.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:ee}};(0,p.unstable_batchedUpdates)(()=>{null==r||r(i),I(f.Initializing),T({type:l.DragStart,initialCoordinates:e,active:t}),L({type:"onDragStart",event:i}),el(e7.current),ea(a)})},onMove(e){T({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eB.current,o=null;if(t&&i){let{cancelDrop:u}=eo.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}en.current=null,(0,p.unstable_batchedUpdates)(()=>{T({type:e}),I(f.Uninitialized),e4(null),el(null),ea(null),e7.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=eo.current[t];null==e||e(o),L({type:t,event:o})}})}}e7.current=o},[W]),e8=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=W.get(r);null!==en.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},en.current=r,e9(n,t))},[W,e9]),e3=(0,g.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e8(e.handler,t)}))]},[]),[x,e8]);(0,g.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eL&&N===f.Initializing&&I(f.Initialized)},[eL,N]),(0,g.useEffect)(()=>{let{onDragMove:e}=eo.current,{active:t,activatorEvent:n,collisions:r,over:l}=eB.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e$.x,y:e$.y},over:l};(0,p.unstable_batchedUpdates)(()=>{null==e||e(i),L({type:"onDragMove",event:i})})},[e$.x,e$.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eB.current;if(!e||null==en.current||!t||!l)return;let{onDragOver:i}=eo.current,a=r.get(e2),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,p.unstable_batchedUpdates)(()=>{e4(o),null==i||i(u),L({type:"onDragOver",event:u})})},[e2]),(0,v.Es)(()=>{eB.current={activatorEvent:ei,active:et,activeNode:eg,collisionRect:e0,collisions:e1,droppableRects:ed,draggableNodes:W,draggingNode:eW,draggingNodeRect:eY,droppableContainers:X,over:e5,scrollableAncestors:eX,scrollAdjustedTranslate:e$},ee.current={initial:eY,translated:e0}},[et,eg,e1,e0,W,eW,eY,ed,X,e5,eX,e$]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:h,scrollableAncestorRects:f,delta:p,threshold:b}=e,y=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ev;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[m,w]=(0,v.$$)(),x=(0,g.useRef)({x:0,y:0}),D=(0,g.useRef)({x:0,y:0}),E=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),C=(0,g.useRef)(null),M=(0,g.useCallback)(()=>{let e=C.current;if(!e)return;let t=x.current.x*D.current.x,n=x.current.y*D.current.y;e.scrollBy(t,n)},[]),k=(0,g.useMemo)(()=>u===c.TreeOrder?[...h].reverse():h,[u,h]);(0,g.useEffect)(()=>{if(!a||!h.length||!E)return void w();for(let e of k){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=J);let{isTop:c,isBottom:d,isLeft:h,isRight:f}=H(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!f&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!h&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,E,t,b);for(let e of["x","y"])y[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),C.current=e,m(M,o),x.current=a,D.current=l;return}}x.current={x:0,y:0},D.current={x:0,y:0},w()},[t,M,r,w,a,o,JSON.stringify(E),JSON.stringify(y),m,h,k,f,JSON.stringify(b)])}({...ew,delta:_,draggingRect:e0,pointerCoordinates:eH,scrollableAncestors:eX,scrollableAncestorRects:eV});let te=(0,g.useMemo)(()=>({active:et,activeNode:eg,activeNodeRect:eL,activatorEvent:ei,collisions:e1,containerNodeRect:eO,dragOverlay:eF,draggableNodes:W,droppableContainers:X,droppableRects:ed,over:e5,measureDroppableContainers:eh,scrollableAncestors:eX,scrollableAncestorRects:eV,measuringConfiguration:ec,measuringScheduled:ef,windowRect:e_}),[et,eg,eL,ei,e1,eO,eF,W,X,ed,e5,eh,eX,eV,ec,ef,e_]),tt=(0,g.useMemo)(()=>({activatorEvent:ei,activators:e3,active:et,activeNodeRect:eL,ariaDescribedById:{draggable:eu},dispatch:T,draggableNodes:W,over:e5,measureDroppableContainers:eh}),[ei,e3,et,eL,T,eu,W,e5,eh]);return g.createElement(w.Provider,{value:O},g.createElement(eA.Provider,{value:tt},g.createElement(eN.Provider,{value:te},g.createElement(eP.Provider,{value:e6},m)),g.createElement(ej,{disabled:(null==b?void 0:b.restoreFocus)===!1})),g.createElement(E,{...b,hiddenTextDescribedById:eu}))}),eK=(0,g.createContext)(null),eF="button";function eW(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:h}=(0,g.useContext)(eA),{role:f=eF,roleDescription:p="draggable",tabIndex:b=0}=null!=l?l:{},y=(null==u?void 0:u.id)===t,m=(0,g.useContext)(y?eP:eK),[w,x]=(0,v.lk)(),[D,E]=(0,v.lk)(),C=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),M=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:D,data:M}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:f,tabIndex:b,"aria-disabled":r,"aria-pressed":!!y&&f===eF||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,f,b,y,p,c.draggable]),isDragging:y,listeners:r?void 0:C,node:w,over:h,setNodeRef:x,setActivatorNodeRef:E,transform:m}}function eY(){return(0,g.useContext)(eN)}let eG={timeout:25};function eU(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(eA),d=(0,g.useRef)({disabled:n}),h=(0,g.useRef)(!1),f=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:b,updateMeasurementsFor:y,timeout:m}={...eG,...i},w=(0,v.YN)(null!=y?y:r),x=em({callback:(0,g.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},m)},[m]),disabled:b||!o}),D=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),h.current=!1),e&&x.observe(e))},[x]),[E,C]=(0,v.lk)(D),M=(0,v.YN)(t);return(0,g.useEffect)(()=>{x&&E.current&&(x.disconnect(),h.current=!1,x.observe(E.current))},[E,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:E,rect:f,data:M}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:f,isOver:(null==s?void 0:s.id)===r,node:E,over:s,setNodeRef:C}}r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}}},78266:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>f,KG:()=>b,Ks:()=>R,Ll:()=>o,Re:()=>C,Sw:()=>i,TW:()=>h,WQ:()=>E,YG:()=>x,YN:()=>v,ZC:()=>m,_q:()=>g,ag:()=>T,e_:()=>k,jn:()=>l,kx:()=>M,l6:()=>a,lk:()=>y,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function h(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let f=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return f(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return f(()=>{n.current!==e&&(n.current=e)},t),n}function b(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function y(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function m(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=D(1),C=D(-1);function M(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function k(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let R=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[R.Translate.toString(e),R.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),S="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){return e.matches(S)?e:e.querySelector(S)}},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);