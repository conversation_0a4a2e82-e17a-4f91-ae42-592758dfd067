(()=>{var e={};e.id=4274,e.ids=[4274],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8590:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>c.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var l=a(65239),r=a(48088),t=a(88170),c=a.n(t),i=a(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(s,n);let d={children:["",{children:["dashboard",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,91508)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\users\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\users\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new l.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/users/page",pathname:"/dashboard/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23767:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>M});var l=a(60687),r=a(43210),t=a(34570),c=a(32884),i=a(29523),n=a(44493),d=a(89667),x=a(96834),o=a(15079),m=a(21342),h=a(80013),u=a(34729),p=a(63503),j=a(54987),v=a(99891),N=a(27351),f=a(7430),y=a(88059);a(58869);var b=a(12597),g=a(13861);function _({onSubmit:e,onCancel:s}){let[a,t]=(0,r.useState)({email:"",full_name:"",phone:"",role:"student",password:"",confirmPassword:"",status:"active",verified:!1,notes:""}),[c,x]=(0,r.useState)(!1),[m,_]=(0,r.useState)(!1),[w,k]=(0,r.useState)({}),[C,A]=(0,r.useState)(!1),Z=()=>{let e={};return a.email?/\S+@\S+\.\S+/.test(a.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",a.full_name?a.full_name.length<2&&(e.full_name="الاسم يجب أن يكون أكثر من حرفين"):e.full_name="الاسم الكامل مطلوب",a.password?a.password.length<6&&(e.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):e.password="كلمة المرور مطلوبة",a.password!==a.confirmPassword&&(e.confirmPassword="كلمة المرور غير متطابقة"),a.phone&&!/^\+?[1-9]\d{1,14}$/.test(a.phone)&&(e.phone="رقم الهاتف غير صحيح"),"student"===a.role&&a.student_id&&a.student_id.length<3&&(e.student_id="رقم الطالب يجب أن يكون 3 أحرف على الأقل"),"school"!==a.role||a.school_name||(e.school_name="اسم المدرسة مطلوب"),"delivery"!==a.role||a.delivery_company||(e.delivery_company="اسم شركة التوصيل مطلوب"),k(e),0===Object.keys(e).length},S=async s=>{if(s.preventDefault(),Z()){A(!0);try{let{confirmPassword:s,...l}=a;await e(l)}catch(e){console.error("Error creating user:",e)}finally{A(!1)}}},P=(e,s)=>{t(a=>({...a,[e]:s})),w[e]&&k(s=>({...s,[e]:""}))};return(0,l.jsx)(p.lG,{open:!0,onOpenChange:s,children:(0,l.jsxs)(p.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(p.c7,{children:[(0,l.jsx)(p.L3,{className:"arabic-text",children:"إضافة مستخدم جديد"}),(0,l.jsx)(p.rr,{className:"arabic-text",children:"أدخل بيانات المستخدم الجديد وحدد دوره في النظام"})]}),(0,l.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"المعلومات الأساسية"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"full_name",className:"arabic-text",children:"الاسم الكامل *"}),(0,l.jsx)(d.p,{id:"full_name",value:a.full_name,onChange:e=>P("full_name",e.target.value),placeholder:"أدخل الاسم الكامل",className:w.full_name?"border-red-500":""}),w.full_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.full_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني *"}),(0,l.jsx)(d.p,{id:"email",type:"email",value:a.email,onChange:e=>P("email",e.target.value),placeholder:"<EMAIL>",className:w.email?"border-red-500":""}),w.email&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.email})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,l.jsx)(d.p,{id:"phone",value:a.phone,onChange:e=>P("phone",e.target.value),placeholder:"+971501234567",className:w.phone?"border-red-500":""}),w.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.phone})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"role",className:"arabic-text",children:"الدور *"}),(0,l.jsxs)(o.l6,{value:a.role,onValueChange:e=>P("role",e),children:[(0,l.jsx)(o.bq,{children:(0,l.jsx)(o.yv,{placeholder:"اختر الدور"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"student",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4"}),"طالب"]})}),(0,l.jsx)(o.eb,{value:"school",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(f.A,{className:"h-4 w-4"}),"مدرسة"]})}),(0,l.jsx)(o.eb,{value:"delivery",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(y.A,{className:"h-4 w-4"}),"شركة توصيل"]})}),(0,l.jsx)(o.eb,{value:"admin",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"مدير"]})})]})]})]})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"كلمة المرور"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"password",className:"arabic-text",children:"كلمة المرور *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"password",type:c?"text":"password",value:a.password,onChange:e=>P("password",e.target.value),placeholder:"أدخل كلمة المرور",className:w.password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>x(!c),children:c?(0,l.jsx)(b.A,{className:"h-4 w-4"}):(0,l.jsx)(g.A,{className:"h-4 w-4"})})]}),w.password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.password})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"confirmPassword",className:"arabic-text",children:"تأكيد كلمة المرور *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"confirmPassword",type:m?"text":"password",value:a.confirmPassword,onChange:e=>P("confirmPassword",e.target.value),placeholder:"أعد إدخال كلمة المرور",className:w.confirmPassword?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>_(!m),children:m?(0,l.jsx)(b.A,{className:"h-4 w-4"}):(0,l.jsx)(g.A,{className:"h-4 w-4"})})]}),w.confirmPassword&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.confirmPassword})]})]})})]}),"student"===a.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات الطالب"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"student_id",className:"arabic-text",children:"رقم الطالب"}),(0,l.jsx)(d.p,{id:"student_id",value:a.student_id||"",onChange:e=>P("student_id",e.target.value),placeholder:"STU2024001",className:w.student_id?"border-red-500":""}),w.student_id&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.student_id})]})})]}),"school"===a.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات المدرسة"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"school_name",className:"arabic-text",children:"اسم المدرسة *"}),(0,l.jsx)(d.p,{id:"school_name",value:a.school_name||"",onChange:e=>P("school_name",e.target.value),placeholder:"جامعة الإمارات العربية المتحدة",className:w.school_name?"border-red-500":""}),w.school_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.school_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"school_address",className:"arabic-text",children:"عنوان المدرسة"}),(0,l.jsx)(u.T,{id:"school_address",value:a.school_address||"",onChange:e=>P("school_address",e.target.value),placeholder:"العنوان الكامل للمدرسة",rows:3})]})]})]}),"delivery"===a.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات شركة التوصيل"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"delivery_company",className:"arabic-text",children:"اسم الشركة *"}),(0,l.jsx)(d.p,{id:"delivery_company",value:a.delivery_company||"",onChange:e=>P("delivery_company",e.target.value),placeholder:"شركة التوصيل السريع",className:w.delivery_company?"border-red-500":""}),w.delivery_company&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.delivery_company})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"delivery_license",className:"arabic-text",children:"رقم الترخيص"}),(0,l.jsx)(d.p,{id:"delivery_license",value:a.delivery_license||"",onChange:e=>P("delivery_license",e.target.value),placeholder:"رقم ترخيص الشركة"})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"إعدادات الحساب"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(h.J,{className:"arabic-text",children:"حالة الحساب"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب نشطاً أم لا"})]}),(0,l.jsx)(j.d,{checked:"active"===a.status,onCheckedChange:e=>P("status",e?"active":"inactive")})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(h.J,{className:"arabic-text",children:"حساب محقق"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب محققاً أم لا"})]}),(0,l.jsx)(j.d,{checked:a.verified,onCheckedChange:e=>P("verified",e)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"notes",className:"arabic-text",children:"ملاحظات"}),(0,l.jsx)(u.T,{id:"notes",value:a.notes||"",onChange:e=>P("notes",e.target.value),placeholder:"ملاحظات إضافية حول المستخدم",rows:3})]})]})]}),(0,l.jsxs)(p.Es,{children:[(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:s,children:"إلغاء"}),(0,l.jsx)(i.$,{type:"submit",disabled:C,children:C?"جاري الإضافة...":"إضافة المستخدم"})]})]})]})})}function w({user:e,onSubmit:s,onCancel:a}){let[t,c]=(0,r.useState)({email:e.email,full_name:e.full_name,phone:e.phone||"",role:e.role,status:e.status,verified:e.verified,student_id:e.student_id||"",school_name:e.school_name||"",delivery_company:e.delivery_company||"",new_password:"",confirm_new_password:""}),[x,m]=(0,r.useState)(!1),[u,_]=(0,r.useState)(!1),[w,k]=(0,r.useState)({}),[C,A]=(0,r.useState)(!1),Z=()=>{let e={};return t.email?/\S+@\S+\.\S+/.test(t.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",t.full_name?t.full_name.length<2&&(e.full_name="الاسم يجب أن يكون أكثر من حرفين"):e.full_name="الاسم الكامل مطلوب",t.new_password&&(t.new_password.length<6&&(e.new_password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"),t.new_password!==t.confirm_new_password&&(e.confirm_new_password="كلمة المرور غير متطابقة")),t.phone&&!/^\+?[1-9]\d{1,14}$/.test(t.phone)&&(e.phone="رقم الهاتف غير صحيح"),"student"===t.role&&t.student_id&&t.student_id.length<3&&(e.student_id="رقم الطالب يجب أن يكون 3 أحرف على الأقل"),"school"!==t.role||t.school_name||(e.school_name="اسم المدرسة مطلوب"),"delivery"!==t.role||t.delivery_company||(e.delivery_company="اسم شركة التوصيل مطلوب"),k(e),0===Object.keys(e).length},S=async e=>{if(e.preventDefault(),Z()){A(!0);try{let{confirm_new_password:e,...a}=t;a.new_password||delete a.new_password,await s(a)}catch(e){console.error("Error updating user:",e)}finally{A(!1)}}},P=(e,s)=>{c(a=>({...a,[e]:s})),w[e]&&k(s=>({...s,[e]:""}))};return(0,l.jsx)(p.lG,{open:!0,onOpenChange:a,children:(0,l.jsxs)(p.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(p.c7,{children:[(0,l.jsx)(p.L3,{className:"arabic-text",children:"تعديل بيانات المستخدم"}),(0,l.jsxs)(p.rr,{className:"arabic-text",children:["تعديل بيانات المستخدم: ",e.full_name]})]}),(0,l.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"المعلومات الأساسية"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"full_name",className:"arabic-text",children:"الاسم الكامل *"}),(0,l.jsx)(d.p,{id:"full_name",value:t.full_name,onChange:e=>P("full_name",e.target.value),placeholder:"أدخل الاسم الكامل",className:w.full_name?"border-red-500":""}),w.full_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.full_name})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني *"}),(0,l.jsx)(d.p,{id:"email",type:"email",value:t.email,onChange:e=>P("email",e.target.value),placeholder:"<EMAIL>",className:w.email?"border-red-500":""}),w.email&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.email})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,l.jsx)(d.p,{id:"phone",value:t.phone,onChange:e=>P("phone",e.target.value),placeholder:"+971501234567",className:w.phone?"border-red-500":""}),w.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.phone})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"role",className:"arabic-text",children:"الدور *"}),(0,l.jsxs)(o.l6,{value:t.role,onValueChange:e=>P("role",e),children:[(0,l.jsx)(o.bq,{children:(0,l.jsx)(o.yv,{placeholder:"اختر الدور"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"student",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4"}),"طالب"]})}),(0,l.jsx)(o.eb,{value:"school",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(f.A,{className:"h-4 w-4"}),"مدرسة"]})}),(0,l.jsx)(o.eb,{value:"delivery",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(y.A,{className:"h-4 w-4"}),"شركة توصيل"]})}),(0,l.jsx)(o.eb,{value:"admin",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"مدير"]})})]})]})]})]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{children:[(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"تغيير كلمة المرور"}),(0,l.jsx)(n.BT,{className:"arabic-text",children:"اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور"})]}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"new_password",className:"arabic-text",children:"كلمة المرور الجديدة"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"new_password",type:x?"text":"password",value:t.new_password||"",onChange:e=>P("new_password",e.target.value),placeholder:"كلمة المرور الجديدة",className:w.new_password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>m(!x),children:x?(0,l.jsx)(b.A,{className:"h-4 w-4"}):(0,l.jsx)(g.A,{className:"h-4 w-4"})})]}),w.new_password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.new_password})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"confirm_new_password",className:"arabic-text",children:"تأكيد كلمة المرور الجديدة"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(d.p,{id:"confirm_new_password",type:u?"text":"password",value:t.confirm_new_password||"",onChange:e=>P("confirm_new_password",e.target.value),placeholder:"تأكيد كلمة المرور الجديدة",className:w.confirm_new_password?"border-red-500":""}),(0,l.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>_(!u),children:u?(0,l.jsx)(b.A,{className:"h-4 w-4"}):(0,l.jsx)(g.A,{className:"h-4 w-4"})})]}),w.confirm_new_password&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.confirm_new_password})]})]})})]}),"student"===t.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات الطالب"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"student_id",className:"arabic-text",children:"رقم الطالب"}),(0,l.jsx)(d.p,{id:"student_id",value:t.student_id||"",onChange:e=>P("student_id",e.target.value),placeholder:"STU2024001",className:w.student_id?"border-red-500":""}),w.student_id&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.student_id})]})})]}),"school"===t.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات المدرسة"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"school_name",className:"arabic-text",children:"اسم المدرسة *"}),(0,l.jsx)(d.p,{id:"school_name",value:t.school_name||"",onChange:e=>P("school_name",e.target.value),placeholder:"جامعة الإمارات العربية المتحدة",className:w.school_name?"border-red-500":""}),w.school_name&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.school_name})]})})]}),"delivery"===t.role&&(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"معلومات شركة التوصيل"})}),(0,l.jsx)(n.Wu,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{htmlFor:"delivery_company",className:"arabic-text",children:"اسم الشركة *"}),(0,l.jsx)(d.p,{id:"delivery_company",value:t.delivery_company||"",onChange:e=>P("delivery_company",e.target.value),placeholder:"شركة التوصيل السريع",className:w.delivery_company?"border-red-500":""}),w.delivery_company&&(0,l.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:w.delivery_company})]})})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"text-lg arabic-text",children:"إعدادات الحساب"})}),(0,l.jsxs)(n.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.J,{className:"arabic-text",children:"حالة الحساب"}),(0,l.jsxs)(o.l6,{value:t.status,onValueChange:e=>P("status",e),children:[(0,l.jsx)(o.bq,{children:(0,l.jsx)(o.yv,{placeholder:"اختر الحالة"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"active",children:"نشط"}),(0,l.jsx)(o.eb,{value:"inactive",children:"غير نشط"}),(0,l.jsx)(o.eb,{value:"suspended",children:"معلق"})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"space-y-0.5",children:[(0,l.jsx)(h.J,{className:"arabic-text",children:"حساب محقق"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"تحديد ما إذا كان الحساب محققاً أم لا"})]}),(0,l.jsx)(j.d,{checked:t.verified,onCheckedChange:e=>P("verified",e)})]})]})]}),(0,l.jsxs)(p.Es,{children:[(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:a,children:"إلغاء"}),(0,l.jsx)(i.$,{type:"submit",disabled:C,children:C?"جاري التحديث...":"حفظ التغييرات"})]})]})]})})}var k=a(75373),C=a(62694),A=a(85814),Z=a.n(A),S=a(41312),P=a(28559),q=a(96474),J=a(99270),T=a(31158),F=a(16023),R=a(78122),B=a(93661),W=a(63143),$=a(62688);let D=(0,$.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),G=(0,$.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var z=a(88233);let E=[{id:"1",email:"<EMAIL>",full_name:"مدير النظام",phone:"+971501234567",role:"admin",status:"active",created_at:"2024-01-15T10:00:00Z",last_login:"2024-01-20T14:30:00Z",verified:!0},{id:"2",email:"<EMAIL>",full_name:"أحمد محمد علي",phone:"+971507654321",role:"student",status:"active",created_at:"2024-01-16T09:15:00Z",last_login:"2024-01-20T12:45:00Z",student_id:"STU2024001",verified:!0},{id:"3",email:"<EMAIL>",full_name:"جامعة الإمارات",phone:"+97126123456",role:"school",status:"active",created_at:"2024-01-10T08:00:00Z",last_login:"2024-01-20T11:20:00Z",school_name:"جامعة الإمارات العربية المتحدة",verified:!0},{id:"4",email:"<EMAIL>",full_name:"شركة التوصيل السريع",phone:"+97144567890",role:"delivery",status:"active",created_at:"2024-01-12T07:30:00Z",last_login:"2024-01-19T16:10:00Z",delivery_company:"شركة التوصيل السريع",verified:!1}];function M(){let[e,s]=(0,r.useState)(E),[a,h]=(0,r.useState)(E),[u,p]=(0,r.useState)(""),[j,b]=(0,r.useState)("all"),[g,A]=(0,r.useState)("all"),[$,M]=(0,r.useState)(!1),[U,O]=(0,r.useState)(null),[V,H]=(0,r.useState)(null),[I,L]=(0,r.useState)(!1),{toasts:K,removeToast:Q,success:X}=(0,C.dj)(),Y=e=>{s(s=>s.map(s=>s.id===e?{...s,status:"active"===s.status?"suspended":"active"}:s)),X("تم تحديث حالة المستخدم","success")},ee=e=>{s(s=>s.map(s=>s.id===e?{...s,verified:!s.verified}:s)),X("تم تحديث حالة التحقق","success")},es=e=>{switch(e){case"admin":return(0,l.jsx)(v.A,{className:"h-4 w-4"});case"student":return(0,l.jsx)(N.A,{className:"h-4 w-4"});case"school":return(0,l.jsx)(f.A,{className:"h-4 w-4"});case"delivery":return(0,l.jsx)(y.A,{className:"h-4 w-4"});default:return(0,l.jsx)(S.A,{className:"h-4 w-4"})}},ea=e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"inactive":return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";case"suspended":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";default:return"bg-gray-100 text-gray-800"}},el=e=>{switch(e){case"active":return"نشط";case"inactive":return"غير نشط";case"suspended":return"معلق";default:return e}},er=e=>{switch(e){case"admin":return"مدير";case"student":return"طالب";case"school":return"مدرسة";case"delivery":return"توصيل";default:return e}};return(0,l.jsx)(t.O,{allowedRoles:["admin"],children:(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,l.jsx)(c.V,{}),(0,l.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,l.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,l.jsxs)(Z(),{href:"/dashboard/admin",children:[(0,l.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المستخدمين \uD83D\uDC65"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة حسابات المستخدمين والصلاحيات والأدوار"})]}),(0,l.jsxs)(i.$,{onClick:()=>M(!0),children:[(0,l.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"إضافة مستخدم"]})]})]}),(0,l.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"إجمالي المستخدمين"}),(0,l.jsx)(S.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.length}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground arabic-text",children:["+",e.filter(e=>"active"===e.status).length," نشط"]})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"الطلاب"}),(0,l.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"student"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"مستخدم طالب"})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"المدارس"}),(0,l.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"school"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"مؤسسة تعليمية"})]})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ZB,{className:"text-sm font-medium arabic-text",children:"شركات التوصيل"}),(0,l.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(n.Wu,{children:[(0,l.jsx)("div",{className:"number text-2xl font-bold",children:e.filter(e=>"delivery"===e.role).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground arabic-text",children:"شركة توصيل"})]})]})]}),(0,l.jsxs)(n.Zp,{className:"mb-8",children:[(0,l.jsx)(n.aR,{children:(0,l.jsx)(n.ZB,{className:"arabic-text",children:"البحث والتصفية"})}),(0,l.jsx)(n.Wu,{children:(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(J.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,l.jsx)(d.p,{placeholder:"البحث بالاسم، البريد الإلكتروني، الهاتف...",value:u,onChange:e=>p(e.target.value),className:"pl-10 arabic-text"})]})}),(0,l.jsxs)(o.l6,{value:j,onValueChange:b,children:[(0,l.jsx)(o.bq,{className:"w-full md:w-48",children:(0,l.jsx)(o.yv,{placeholder:"تصفية بالدور"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"all",children:"جميع الأدوار"}),(0,l.jsx)(o.eb,{value:"admin",children:"مدير"}),(0,l.jsx)(o.eb,{value:"student",children:"طالب"}),(0,l.jsx)(o.eb,{value:"school",children:"مدرسة"}),(0,l.jsx)(o.eb,{value:"delivery",children:"توصيل"})]})]}),(0,l.jsxs)(o.l6,{value:g,onValueChange:A,children:[(0,l.jsx)(o.bq,{className:"w-full md:w-48",children:(0,l.jsx)(o.yv,{placeholder:"تصفية بالحالة"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"all",children:"جميع الحالات"}),(0,l.jsx)(o.eb,{value:"active",children:"نشط"}),(0,l.jsx)(o.eb,{value:"inactive",children:"غير نشط"}),(0,l.jsx)(o.eb,{value:"suspended",children:"معلق"})]})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),(0,l.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"استيراد"]})]})]})})]}),(0,l.jsxs)(n.Zp,{children:[(0,l.jsx)(n.aR,{children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)(n.ZB,{className:"arabic-text",children:["قائمة المستخدمين (",a.length,")"]}),(0,l.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>window.location.reload(),children:[(0,l.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"تحديث"]})]})}),(0,l.jsx)(n.Wu,{children:(0,l.jsxs)("div",{className:"overflow-x-auto",children:[(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b",children:[(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"المستخدم"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الدور"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الحالة"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"التحقق"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"آخر دخول"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium arabic-text",children:"الإجراءات"})]})}),(0,l.jsx)("tbody",{children:a.map(e=>(0,l.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:e.full_name.charAt(0)})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text",children:e.full_name}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:e.email}),e.phone&&(0,l.jsx)("p",{className:"text-xs text-gray-400",children:e.phone})]})]})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[es(e.role),(0,l.jsx)("span",{className:"arabic-text",children:er(e.role)})]})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)(x.E,{className:ea(e.status),children:el(e.status)})}),(0,l.jsx)("td",{className:"py-4 px-4",children:e.verified?(0,l.jsx)(x.E,{className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:"محقق"}):(0,l.jsx)(x.E,{variant:"outline",children:"غير محقق"})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:e.last_login?new Date(e.last_login).toLocaleDateString("ar-AE"):"لم يدخل بعد"})}),(0,l.jsx)("td",{className:"py-4 px-4",children:(0,l.jsx)("div",{className:"actions flex items-center gap-2",children:(0,l.jsxs)(m.rI,{children:[(0,l.jsx)(m.ty,{asChild:!0,children:(0,l.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,l.jsx)(B.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(m.SQ,{align:"end",children:[(0,l.jsx)(m.lp,{className:"arabic-text",children:"الإجراءات"}),(0,l.jsx)(m.mB,{}),(0,l.jsxs)(m._2,{onClick:()=>O(e),children:[(0,l.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,l.jsx)(m._2,{onClick:()=>Y(e.id),children:"active"===e.status?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(D,{className:"h-4 w-4 mr-2"}),"تعليق"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(G,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,l.jsx)(m._2,{onClick:()=>ee(e.id),children:e.verified?"إلغاء التحقق":"تحقق"}),(0,l.jsx)(m.mB,{}),(0,l.jsxs)(m._2,{onClick:()=>H(e),className:"text-red-600",children:[(0,l.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})})]},e.id))})]}),0===a.length&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(S.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد مستخدمين"})]})]})})]})]}),$&&(0,l.jsx)(_,{onSubmit:e=>{let a={id:Date.now().toString(),email:e.email,full_name:e.full_name,phone:e.phone,role:e.role,status:"active",created_at:new Date().toISOString(),verified:!1,...e};s(e=>[a,...e]),M(!1),X("تم إضافة المستخدم بنجاح","success")},onCancel:()=>M(!1)}),U&&(0,l.jsx)(w,{user:U,onSubmit:e=>{U&&(s(s=>s.map(s=>s.id===U.id?{...s,...e}:s)),O(null),X("تم تحديث بيانات المستخدم بنجاح","success"))},onCancel:()=>O(null)}),V&&(0,l.jsx)(k.T,{title:"حذف المستخدم",message:`هل أنت متأكد من حذف المستخدم "${V.full_name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,onConfirm:()=>{V&&(s(e=>e.filter(e=>e.id!==V.id)),H(null),X("تم حذف المستخدم بنجاح","success"))},onCancel:()=>H(null),confirmText:"حذف",cancelText:"إلغاء",variant:"destructive"}),(0,l.jsx)(C.N9,{toasts:K,onRemove:Q})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34570:(e,s,a)=>{"use strict";a.d(s,{O:()=>d});var l=a(60687),r=a(43210),t=a(16189),c=a(63213),i=a(44493),n=a(41862);function d({children:e,requiredRole:s,redirectTo:a="/auth"}){let{user:d,profile:x,loading:o,hasRole:m}=(0,c.A)();(0,t.useRouter)();let[h,u]=(0,r.useState)(!1);return!h||o?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,l.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,l.jsx)(n.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):d&&(!s||m(s))?(0,l.jsx)(l.Fragment,{children:e}):null}},41312:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},46984:(e,s,a)=>{Promise.resolve().then(a.bind(a,23767))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},83432:(e,s,a)=>{Promise.resolve().then(a.bind(a,91508))},91508:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});let l=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\users\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),l=s.X(0,[4447,8773,1345,3044,4617,5336,2884,7068],()=>a(8590));module.exports=l})();