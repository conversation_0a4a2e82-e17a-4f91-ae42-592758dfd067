"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { OrderStatusBadge, PaymentStatusBadge } from './OrderStatusBadge'
import { useToast } from '@/components/ui/toast'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Package, 
  CreditCard,
  Calendar,
  FileText,
  Save,
  X
} from 'lucide-react'

interface OrderItem {
  id: string
  product_name: string
  product_image: string
  category: string
  quantity: number
  unit_price: number
  total_price: number
  customizations?: {
    color?: string
    size?: string
    embroidery?: string
    special_requests?: string
  }
}

interface Order {
  id: string
  order_number: string
  customer_name: string
  customer_email: string
  customer_phone?: string
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method?: string
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping_cost: number
  total: number
  shipping_address: {
    street: string
    city: string
    state: string
    postal_code: string
    country: string
  }
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  delivery_date?: string
  school_name?: string
}

interface OrderDetailsDialogProps {
  order: Order | null
  isOpen: boolean
  onClose: () => void
  onUpdate: (orderId: string, updates: any) => Promise<void>
}

export function OrderDetailsDialog({ order, isOpen, onClose, onUpdate }: OrderDetailsDialogProps) {
  const toast = useToast()
  const [isUpdating, setIsUpdating] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [newPaymentStatus, setNewPaymentStatus] = useState('')
  const [newNotes, setNewNotes] = useState('')

  if (!order) return null

  const handleUpdate = async () => {
    try {
      setIsUpdating(true)
      
      const updates: any = {}
      if (newStatus && newStatus !== order.status) {
        updates.status = newStatus
      }
      if (newPaymentStatus && newPaymentStatus !== order.payment_status) {
        updates.payment_status = newPaymentStatus
      }
      if (newNotes !== order.notes) {
        updates.notes = newNotes
      }

      if (Object.keys(updates).length > 0) {
        await onUpdate(order.id, updates)
        toast.success('تم تحديث الطلب بنجاح')
        onClose()
      } else {
        toast.info('لا توجد تغييرات للحفظ')
      }
    } catch (error) {
      toast.error('فشل في تحديث الطلب')
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="arabic-text">
            تفاصيل الطلب {order.order_number}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <User className="h-5 w-5" />
                معلومات العميل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium arabic-text">{order.customer_name}</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{order.customer_email}</span>
              </div>
              {order.customer_phone && (
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{order.customer_phone}</span>
                </div>
              )}
              {order.school_name && (
                <div className="flex items-center gap-3">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="text-sm arabic-text">{order.school_name}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Status */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <Package className="h-5 w-5" />
                حالة الطلب
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium arabic-text">حالة الطلب:</span>
                <OrderStatusBadge status={order.status} />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium arabic-text">حالة الدفع:</span>
                <PaymentStatusBadge status={order.payment_status} />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium arabic-text">طريقة الدفع:</span>
                <span className="text-sm">{order.payment_method || 'غير محدد'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium arabic-text">تاريخ الطلب:</span>
                <span className="text-sm">{new Date(order.created_at).toLocaleDateString('ar-SA')}</span>
              </div>
              {order.tracking_number && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium arabic-text">رقم التتبع:</span>
                  <span className="text-sm font-mono">{order.tracking_number}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                عنوان التوصيل
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-1">
                <div>{order.shipping_address.street}</div>
                <div>{order.shipping_address.city}, {order.shipping_address.state}</div>
                <div>{order.shipping_address.postal_code}</div>
                <div>{order.shipping_address.country}</div>
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                ملخص الطلب
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="arabic-text">المجموع الفرعي:</span>
                <span>{order.subtotal.toFixed(2)} د.إ</span>
              </div>
              <div className="flex justify-between">
                <span className="arabic-text">الضريبة:</span>
                <span>{order.tax.toFixed(2)} د.إ</span>
              </div>
              <div className="flex justify-between">
                <span className="arabic-text">الشحن:</span>
                <span>{order.shipping_cost.toFixed(2)} د.إ</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between font-bold">
                  <span className="arabic-text">الإجمالي:</span>
                  <span>{order.total.toFixed(2)} د.إ</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Items */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="arabic-text">عناصر الطلب</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <Package className="h-8 w-8 text-gray-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium arabic-text">{item.product_name}</h4>
                    <p className="text-sm text-gray-600">الكمية: {item.quantity}</p>
                    {item.customizations && (
                      <div className="text-xs text-gray-500 mt-1">
                        {item.customizations.color && <span>اللون: {item.customizations.color} </span>}
                        {item.customizations.size && <span>المقاس: {item.customizations.size} </span>}
                        {item.customizations.embroidery && <span>التطريز: {item.customizations.embroidery}</span>}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{item.total_price.toFixed(2)} د.إ</div>
                    <div className="text-sm text-gray-500">{item.unit_price.toFixed(2)} د.إ / قطعة</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Update Form */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="arabic-text">تحديث الطلب</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium arabic-text">حالة الطلب</label>
                <Select value={newStatus || order.status} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">في الانتظار</SelectItem>
                    <SelectItem value="confirmed">مؤكد</SelectItem>
                    <SelectItem value="in_production">قيد الإنتاج</SelectItem>
                    <SelectItem value="shipped">تم الشحن</SelectItem>
                    <SelectItem value="delivered">تم التسليم</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium arabic-text">حالة الدفع</label>
                <Select value={newPaymentStatus || order.payment_status} onValueChange={setNewPaymentStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">في الانتظار</SelectItem>
                    <SelectItem value="paid">مدفوع</SelectItem>
                    <SelectItem value="failed">فشل</SelectItem>
                    <SelectItem value="refunded">مسترد</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium arabic-text">ملاحظات</label>
              <Textarea
                value={newNotes || order.notes || ''}
                onChange={(e) => setNewNotes(e.target.value)}
                placeholder="إضافة ملاحظات..."
                className="arabic-text"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button onClick={handleUpdate} disabled={isUpdating}>
                <Save className="h-4 w-4 mr-2" />
                {isUpdating ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button variant="outline" onClick={onClose}>
                <X className="h-4 w-4 mr-2" />
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}
