"use strict";(()=>{var e={};e.id=6778,e.ids=[6778],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75742:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>d,PUT:()=>p});var n=r(96559),o=r(48088),i=r(37719),a=r(32190),u=r(38561);async function d(e,{params:t}){try{let{id:e}=await t,r=u.CN.getMenuItems().find(t=>t.id===e);if(!r)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});return a.NextResponse.json({menuItem:r})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=await t,{title_ar:s,title_en:n,title_fr:o,slug:i,icon:d,parent_id:p,order_index:c,is_active:l,target_type:x,target_value:m}=await e.json(),g=u.CN.getMenuItems(),v=g.findIndex(e=>e.id===r);if(-1===v)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});if(i&&i!==g[v].slug&&g.find(e=>e.slug===i&&e.id!==r))return a.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let f={...g[v],title_ar:s||g[v].title_ar,title_en:void 0!==n?n:g[v].title_en,title_fr:void 0!==o?o:g[v].title_fr,slug:i||g[v].slug,icon:void 0!==d?d:g[v].icon,parent_id:void 0!==p?p:g[v].parent_id,order_index:void 0!==c?c:g[v].order_index,is_active:void 0!==l?l:g[v].is_active,target_type:x||g[v].target_type,target_value:m||g[v].target_value,updated_at:new Date().toISOString()};return g[v]=f,u.CN.saveMenuItems(g),a.NextResponse.json({message:"تم تحديث عنصر القائمة بنجاح",menuItem:f})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e,{params:t}){try{let{id:e}=await t,r=u.CN.getMenuItems(),s=r.findIndex(t=>t.id===e);if(-1===s)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});if(r.filter(t=>t.parent_id===e).length>0)return a.NextResponse.json({error:"لا يمكن حذف عنصر يحتوي على عناصر فرعية"},{status:400});return r.splice(s,1),u.CN.saveMenuItems(r),a.NextResponse.json({message:"تم حذف عنصر القائمة بنجاح"})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/menu-items/[id]/route",pathname:"/api/menu-items/[id]",filename:"route",bundlePath:"app/api/menu-items/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=l;function v(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(75742));module.exports=s})();