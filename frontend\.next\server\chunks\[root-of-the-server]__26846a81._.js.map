{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;AAgG1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAAmE,EAAU;QACxG,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;AACF", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/schools/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager, MockSchool } from '@/lib/mockData'\n\n// GET - جلب جميع المدارس\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const includeInactive = searchParams.get('include_inactive') === 'true'\n    const city = searchParams.get('city')\n    const search = searchParams.get('search')\n    const sortBy = searchParams.get('sort_by') || 'created_at'\n    const sortOrder = searchParams.get('sort_order') || 'desc'\n\n    // جلب البيانات الوهمية\n    let schools = MockDataManager.getSchools()\n\n    // تطبيق الفلاتر\n    if (!includeInactive) {\n      schools = schools.filter(school => school.is_active)\n    }\n\n    if (city) {\n      schools = schools.filter(school => \n        school.city?.toLowerCase().includes(city.toLowerCase())\n      )\n    }\n\n    if (search) {\n      const searchLower = search.toLowerCase()\n      schools = schools.filter(school => \n        school.name.toLowerCase().includes(searchLower) ||\n        school.name_en?.toLowerCase().includes(searchLower) ||\n        school.email?.toLowerCase().includes(searchLower) ||\n        school.city?.toLowerCase().includes(searchLower)\n      )\n    }\n\n    // ترتيب النتائج\n    schools.sort((a, b) => {\n      let aValue: any = a[sortBy as keyof MockSchool]\n      let bValue: any = b[sortBy as keyof MockSchool]\n\n      // معالجة القيم الفارغة\n      if (aValue === undefined || aValue === null) aValue = ''\n      if (bValue === undefined || bValue === null) bValue = ''\n\n      // معالجة التواريخ\n      if (sortBy === 'created_at' || sortBy === 'updated_at' || sortBy === 'graduation_date') {\n        aValue = new Date(aValue).getTime()\n        bValue = new Date(bValue).getTime()\n      }\n\n      // معالجة الأرقام\n      if (sortBy === 'student_count') {\n        aValue = Number(aValue) || 0\n        bValue = Number(bValue) || 0\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1\n      } else {\n        return aValue < bValue ? 1 : -1\n      }\n    })\n\n    // إحصائيات سريعة\n    const stats = {\n      total: schools.length,\n      active: schools.filter(s => s.is_active).length,\n      inactive: schools.filter(s => !s.is_active).length,\n      totalStudents: schools.reduce((sum, s) => sum + s.student_count, 0),\n      averageStudents: schools.length > 0 ? Math.round(schools.reduce((sum, s) => sum + s.student_count, 0) / schools.length) : 0\n    }\n\n    return NextResponse.json({ \n      schools,\n      stats,\n      total: schools.length\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة مدرسة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name,\n      name_en,\n      name_fr,\n      address,\n      city,\n      phone,\n      email,\n      website,\n      logo_url,\n      graduation_date,\n      student_count,\n      is_active,\n      settings\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name) {\n      return NextResponse.json(\n        { error: 'اسم المدرسة مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    if (student_count !== undefined && (isNaN(student_count) || student_count < 0)) {\n      return NextResponse.json(\n        { error: 'عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من صحة البريد الإلكتروني\n    if (email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n      return NextResponse.json(\n        { error: 'البريد الإلكتروني غير صحيح' },\n        { status: 400 }\n      )\n    }\n\n    // جلب المدارس الحالية\n    const schools = MockDataManager.getSchools()\n\n    // التحقق من عدم تكرار البريد الإلكتروني\n    if (email) {\n      const existingSchool = schools.find(school => school.email === email)\n      if (existingSchool) {\n        return NextResponse.json(\n          { error: 'البريد الإلكتروني موجود بالفعل' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // إنشاء المدرسة الجديدة\n    const newSchool: MockSchool = {\n      id: MockDataManager.generateId(),\n      name,\n      name_en: name_en || undefined,\n      name_fr: name_fr || undefined,\n      address: address || undefined,\n      city: city || undefined,\n      phone: phone || undefined,\n      email: email || undefined,\n      website: website || undefined,\n      logo_url: logo_url || undefined,\n      graduation_date: graduation_date || undefined,\n      student_count: student_count || 0,\n      is_active: is_active ?? true,\n      settings: settings || {},\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n\n    // حفظ المدرسة\n    schools.push(newSchool)\n    MockDataManager.saveSchools(schools)\n\n    return NextResponse.json({ \n      message: 'تم إضافة المدرسة بنجاح',\n      school: newSchool \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,wBAAwB;QACjE,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC,cAAc;QAC9C,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QAEpD,uBAAuB;QACvB,IAAI,UAAU,wHAAA,CAAA,kBAAe,CAAC,UAAU;QAExC,gBAAgB;QAChB,IAAI,CAAC,iBAAiB;YACpB,UAAU,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS;QACrD;QAEA,IAAI,MAAM;YACR,UAAU,QAAQ,MAAM,CAAC,CAAA,SACvB,OAAO,IAAI,EAAE,cAAc,SAAS,KAAK,WAAW;QAExD;QAEA,IAAI,QAAQ;YACV,MAAM,cAAc,OAAO,WAAW;YACtC,UAAU,QAAQ,MAAM,CAAC,CAAA,SACvB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,OAAO,OAAO,EAAE,cAAc,SAAS,gBACvC,OAAO,KAAK,EAAE,cAAc,SAAS,gBACrC,OAAO,IAAI,EAAE,cAAc,SAAS;QAExC;QAEA,gBAAgB;QAChB,QAAQ,IAAI,CAAC,CAAC,GAAG;YACf,IAAI,SAAc,CAAC,CAAC,OAA2B;YAC/C,IAAI,SAAc,CAAC,CAAC,OAA2B;YAE/C,uBAAuB;YACvB,IAAI,WAAW,aAAa,WAAW,MAAM,SAAS;YACtD,IAAI,WAAW,aAAa,WAAW,MAAM,SAAS;YAEtD,kBAAkB;YAClB,IAAI,WAAW,gBAAgB,WAAW,gBAAgB,WAAW,mBAAmB;gBACtF,SAAS,IAAI,KAAK,QAAQ,OAAO;gBACjC,SAAS,IAAI,KAAK,QAAQ,OAAO;YACnC;YAEA,iBAAiB;YACjB,IAAI,WAAW,iBAAiB;gBAC9B,SAAS,OAAO,WAAW;gBAC3B,SAAS,OAAO,WAAW;YAC7B;YAEA,IAAI,cAAc,OAAO;gBACvB,OAAO,SAAS,SAAS,IAAI,CAAC;YAChC,OAAO;gBACL,OAAO,SAAS,SAAS,IAAI,CAAC;YAChC;QACF;QAEA,iBAAiB;QACjB,MAAM,QAAQ;YACZ,OAAO,QAAQ,MAAM;YACrB,QAAQ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;YAC/C,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;YAClD,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;YACjE,iBAAiB,QAAQ,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE,KAAK,QAAQ,MAAM,IAAI;QAC5H;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA,OAAO,QAAQ,MAAM;QACvB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,eAAe,EACf,aAAa,EACb,SAAS,EACT,QAAQ,EACT,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,kBAAkB,aAAa,CAAC,MAAM,kBAAkB,gBAAgB,CAAC,GAAG;YAC9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,SAAS,CAAC,6BAA6B,IAAI,CAAC,QAAQ;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,UAAU,wHAAA,CAAA,kBAAe,CAAC,UAAU;QAE1C,wCAAwC;QACxC,IAAI,OAAO;YACT,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;YAC/D,IAAI,gBAAgB;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiC,GAC1C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,wBAAwB;QACxB,MAAM,YAAwB;YAC5B,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B;YACA,SAAS,WAAW;YACpB,SAAS,WAAW;YACpB,SAAS,WAAW;YACpB,MAAM,QAAQ;YACd,OAAO,SAAS;YAChB,OAAO,SAAS;YAChB,SAAS,WAAW;YACpB,UAAU,YAAY;YACtB,iBAAiB,mBAAmB;YACpC,eAAe,iBAAiB;YAChC,WAAW,aAAa;YACxB,UAAU,YAAY,CAAC;YACvB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,cAAc;QACd,QAAQ,IAAI,CAAC;QACb,wHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;QACV,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}