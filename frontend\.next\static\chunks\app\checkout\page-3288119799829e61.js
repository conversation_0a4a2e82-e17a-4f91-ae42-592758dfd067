(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>t});var i=s(95155);s(12115);var r=s(59434);function t(e){let{className:a,type:s,...t}=e;return(0,i.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...t})}},81093:(e,a,s)=>{Promise.resolve().then(s.bind(s,93369))},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var i=s(95155);s(12115);var r=s(40968),t=s(59434);function l(e){let{className:a,...s}=e;return(0,i.jsx)(r.b,{"data-slot":"label",className:(0,t.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>t});var i=s(95155);s(12115);var r=s(59434);function t(e){let{className:a,...s}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...s})}},93369:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>F});var i=s(95155),r=s(12115),t=s(40283),l=s(37784),c=s(66695),d=s(30285),n=s(62523),x=s(85057),m=s(88539),o=s(22346),h=s(54059),u=s(9428),j=s(59434);function p(e){let{className:a,...s}=e;return(0,i.jsx)(h.bL,{"data-slot":"radio-group",className:(0,j.cn)("grid gap-3",a),...s})}function b(e){let{className:a,...s}=e;return(0,i.jsx)(h.q7,{"data-slot":"radio-group-item",className:(0,j.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,i.jsx)(h.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,i.jsx)(u.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}var N=s(76981),v=s(5196);function g(e){let{className:a,...s}=e;return(0,i.jsx)(N.bL,{"data-slot":"checkbox",className:(0,j.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,i.jsx)(N.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(v.A,{className:"size-3.5"})})})}var f=s(29799),y=s(81586),w=s(40646),k=s(75525),C=s(4516),A=s(35169),J=s(92138);function F(){let{user:e,profile:a}=(0,t.A)(),[s,h]=(0,r.useState)(1),[u,j]=(0,r.useState)({fullName:(null==a?void 0:a.full_name)||"",phone:(null==a?void 0:a.phone)||"",email:(null==a?void 0:a.email)||"",address:"",city:"",state:"",zipCode:"",country:"الإمارات العربية المتحدة"}),[N,v]=(0,r.useState)({type:"card"}),[F,z]=(0,r.useState)("standard"),[B,Z]=(0,r.useState)(""),[_,T]=(0,r.useState)(!1),[D,S]=(0,r.useState)(!1),$={items:[{name:"زي التخرج الكلاسيكي",quantity:1,price:299.99},{name:"قبعة التخرج المميزة",quantity:1,price:89.99}],subtotal:389.98,shipping:25,tax:20.75,total:435.73},q=[{id:"standard",name:"التوصيل العادي",description:"3-5 أيام عمل",price:25,icon:(0,i.jsx)(f.A,{className:"h-5 w-5"})},{id:"express",name:"التوصيل السريع",description:"1-2 أيام عمل",price:50,icon:(0,i.jsx)(f.A,{className:"h-5 w-5"})},{id:"same_day",name:"التوصيل في نفس اليوم",description:"خلال 6 ساعات",price:100,icon:(0,i.jsx)(f.A,{className:"h-5 w-5"})}],V=[{id:"card",name:"بطاقة ائتمان/خصم",description:"Visa, Mastercard, American Express",icon:(0,i.jsx)(y.A,{className:"h-5 w-5"})},{id:"cash",name:"الدفع عند الاستلام",description:"ادفع نقداً عند وصول الطلب",icon:(0,i.jsx)(w.A,{className:"h-5 w-5"})},{id:"bank_transfer",name:"تحويل بنكي",description:"تحويل مباشر إلى حساب البنك",icon:(0,i.jsx)(k.A,{className:"h-5 w-5"})}],R=async()=>{if(!_)return void alert("يرجى الموافقة على الشروط والأحكام");S(!0),setTimeout(()=>{S(!1),window.location.href="/order-confirmation"},2e3)},W=[{id:1,name:"معلومات الشحن",icon:(0,i.jsx)(C.A,{className:"h-4 w-4"})},{id:2,name:"طريقة التوصيل",icon:(0,i.jsx)(f.A,{className:"h-4 w-4"})},{id:3,name:"طريقة الدفع",icon:(0,i.jsx)(y.A,{className:"h-4 w-4"})},{id:4,name:"مراجعة الطلب",icon:(0,i.jsx)(w.A,{className:"h-4 w-4"})}];return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,i.jsx)(l.V,{}),(0,i.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إتمام الطلب \uD83D\uDCB3"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"أكمل معلوماتك لإتمام عملية الشراء"})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)("div",{className:"flex items-center justify-between",children:W.map((e,a)=>(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full border-2 ".concat(s>=e.id?"bg-blue-600 border-blue-600 text-white":"border-gray-300 text-gray-400"),children:s>e.id?(0,i.jsx)(w.A,{className:"h-5 w-5"}):e.icon}),(0,i.jsx)("span",{className:"ml-3 text-sm font-medium arabic-text ".concat(s>=e.id?"text-blue-600":"text-gray-500"),children:e.name}),a<W.length-1&&(0,i.jsx)("div",{className:"w-16 h-0.5 mx-4 ".concat(s>e.id?"bg-blue-600":"bg-gray-300")})]},e.id))})}),(0,i.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2",children:[1===s&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,i.jsx)(C.A,{className:"h-5 w-5"}),"معلومات الشحن"]}),(0,i.jsx)(c.BT,{className:"arabic-text",children:"أدخل عنوان التوصيل ومعلومات الاتصال"})]}),(0,i.jsxs)(c.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"fullName",className:"arabic-text",children:"الاسم الكامل"}),(0,i.jsx)(n.p,{id:"fullName",value:u.fullName,onChange:e=>j(a=>({...a,fullName:e.target.value})),className:"arabic-text"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"phone",className:"arabic-text",children:"رقم الهاتف"}),(0,i.jsx)(n.p,{id:"phone",value:u.phone,onChange:e=>j(a=>({...a,phone:e.target.value})),className:"arabic-text"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"email",className:"arabic-text",children:"البريد الإلكتروني"}),(0,i.jsx)(n.p,{id:"email",type:"email",value:u.email,onChange:e=>j(a=>({...a,email:e.target.value}))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"address",className:"arabic-text",children:"العنوان التفصيلي"}),(0,i.jsx)(m.T,{id:"address",value:u.address,onChange:e=>j(a=>({...a,address:e.target.value})),placeholder:"رقم المبنى، اسم الشارع، المنطقة...",className:"arabic-text"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"city",className:"arabic-text",children:"المدينة"}),(0,i.jsx)(n.p,{id:"city",value:u.city,onChange:e=>j(a=>({...a,city:e.target.value})),className:"arabic-text"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"state",className:"arabic-text",children:"الإمارة"}),(0,i.jsx)(n.p,{id:"state",value:u.state,onChange:e=>j(a=>({...a,state:e.target.value})),className:"arabic-text"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"zipCode",className:"arabic-text",children:"الرمز البريدي"}),(0,i.jsx)(n.p,{id:"zipCode",value:u.zipCode,onChange:e=>j(a=>({...a,zipCode:e.target.value}))})]})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsxs)(d.$,{onClick:()=>h(2),className:"arabic-text",children:["التالي",(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})})]})]}),2===s&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,i.jsx)(f.A,{className:"h-5 w-5"}),"طريقة التوصيل"]}),(0,i.jsx)(c.BT,{className:"arabic-text",children:"اختر طريقة التوصيل المناسبة لك"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)(p,{value:F,onValueChange:z,children:(0,i.jsx)("div",{className:"space-y-4",children:q.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(b,{value:e.id,id:e.id}),(0,i.jsx)(x.J,{htmlFor:e.id,className:"flex-1 cursor-pointer",children:(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon,(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]}),(0,i.jsxs)("span",{className:"font-bold",children:[e.price," درهم"]})]})})]},e.id))})}),(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsx)(x.J,{htmlFor:"instructions",className:"arabic-text",children:"تعليمات خاصة (اختياري)"}),(0,i.jsx)(m.T,{id:"instructions",value:B,onChange:e=>Z(e.target.value),placeholder:"أي تعليمات خاصة للتوصيل...",className:"arabic-text"})]}),(0,i.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,i.jsxs)(d.$,{variant:"outline",onClick:()=>h(1),className:"arabic-text",children:[(0,i.jsx)(J.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,i.jsxs)(d.$,{onClick:()=>h(3),className:"arabic-text",children:["التالي",(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})]})]})]}),3===s&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,i.jsx)(y.A,{className:"h-5 w-5"}),"طريقة الدفع"]}),(0,i.jsx)(c.BT,{className:"arabic-text",children:"اختر طريقة الدفع المفضلة لديك"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)(p,{value:N.type,onValueChange:e=>v(a=>({...a,type:e})),children:(0,i.jsx)("div",{className:"space-y-4",children:V.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(b,{value:e.id,id:e.id}),(0,i.jsx)(x.J,{htmlFor:e.id,className:"flex-1 cursor-pointer",children:(0,i.jsxs)("div",{className:"flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800",children:[e.icon,(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]})})]},e.id))})}),"card"===N.type&&(0,i.jsxs)("div",{className:"mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800",children:[(0,i.jsx)("h3",{className:"font-medium arabic-text",children:"معلومات البطاقة"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(x.J,{htmlFor:"cardNumber",className:"arabic-text",children:"رقم البطاقة"}),(0,i.jsx)(n.p,{id:"cardNumber",placeholder:"1234 5678 9012 3456",value:N.cardNumber||"",onChange:e=>v(a=>({...a,cardNumber:e.target.value}))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"expiryDate",className:"arabic-text",children:"تاريخ الانتهاء"}),(0,i.jsx)(n.p,{id:"expiryDate",placeholder:"MM/YY",value:N.expiryDate||"",onChange:e=>v(a=>({...a,expiryDate:e.target.value}))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(x.J,{htmlFor:"cvv",className:"arabic-text",children:"CVV"}),(0,i.jsx)(n.p,{id:"cvv",placeholder:"123",value:N.cvv||"",onChange:e=>v(a=>({...a,cvv:e.target.value}))})]}),(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)(x.J,{htmlFor:"cardholderName",className:"arabic-text",children:"اسم حامل البطاقة"}),(0,i.jsx)(n.p,{id:"cardholderName",value:N.cardholderName||"",onChange:e=>v(a=>({...a,cardholderName:e.target.value})),className:"arabic-text"})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,i.jsxs)(d.$,{variant:"outline",onClick:()=>h(2),className:"arabic-text",children:[(0,i.jsx)(J.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,i.jsxs)(d.$,{onClick:()=>h(4),className:"arabic-text",children:["التالي",(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})]})]})]}),4===s&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,i.jsx)(w.A,{className:"h-5 w-5"}),"مراجعة الطلب"]}),(0,i.jsx)(c.BT,{className:"arabic-text",children:"راجع تفاصيل طلبك قبل التأكيد"})]}),(0,i.jsxs)(c.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-3 arabic-text",children:"المنتجات"}),(0,i.jsx)("div",{className:"space-y-2",children:$.items.map((e,a)=>(0,i.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,i.jsxs)("span",{className:"arabic-text",children:[e.name," \xd7 ",e.quantity]}),(0,i.jsxs)("span",{children:[e.price," درهم"]})]},a))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-3 arabic-text",children:"عنوان التوصيل"}),(0,i.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,i.jsx)("p",{className:"arabic-text",children:u.fullName}),(0,i.jsx)("p",{className:"arabic-text",children:u.address}),(0,i.jsxs)("p",{className:"arabic-text",children:[u.city,", ",u.state," ",u.zipCode]}),(0,i.jsx)("p",{children:u.phone})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(g,{id:"terms",checked:_,onCheckedChange:T}),(0,i.jsxs)(x.J,{htmlFor:"terms",className:"text-sm arabic-text",children:["أوافق على ",(0,i.jsx)("a",{href:"/terms",className:"text-blue-600 hover:underline",children:"الشروط والأحكام"})," و",(0,i.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:underline",children:"سياسة الخصوصية"})]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsxs)(d.$,{variant:"outline",onClick:()=>h(3),className:"arabic-text",children:[(0,i.jsx)(J.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,i.jsxs)(d.$,{onClick:R,disabled:!_||D,className:"arabic-text",children:[D?"جاري المعالجة...":"تأكيد الطلب",(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"})]})]})]})]})]}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)(c.Zp,{className:"sticky top-24",children:[(0,i.jsx)(c.aR,{children:(0,i.jsx)(c.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,i.jsxs)(c.Wu,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"space-y-2",children:$.items.map((e,a)=>(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"arabic-text",children:[e.name," \xd7 ",e.quantity]}),(0,i.jsxs)("span",{children:[e.price," درهم"]})]},a))}),(0,i.jsx)(o.w,{}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,i.jsxs)("span",{children:[$.subtotal," درهم"]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,i.jsxs)("span",{children:[$.shipping," درهم"]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"arabic-text",children:"الضريبة:"}),(0,i.jsxs)("span",{children:[$.tax," درهم"]})]})]}),(0,i.jsx)(o.w,{}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,i.jsx)("span",{className:"arabic-text",children:"الإجمالي:"}),(0,i.jsxs)("span",{children:[$.total," درهم"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,i.jsx)(k.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"arabic-text",children:"دفع آمن ومضمون"})]})]})]})})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,5148,8698,6874,7889,1475,7277,2632,7443,7784,8441,1684,7358],()=>a(81093)),_N_E=e.O()}]);