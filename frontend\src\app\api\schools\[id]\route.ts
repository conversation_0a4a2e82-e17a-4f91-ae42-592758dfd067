import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockSchool } from '@/lib/mockData'

// GET - جلب مدرسة واحدة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const schools = MockDataManager.getSchools()
    const school = schools.find(s => s.id === params.id)

    if (!school) {
      return NextResponse.json(
        { error: 'المدرسة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({ school })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مدرسة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      name,
      name_en,
      name_fr,
      address,
      city,
      phone,
      email,
      website,
      logo_url,
      graduation_date,
      student_count,
      is_active,
      settings
    } = body

    // جلب المدارس الحالية
    const schools = MockDataManager.getSchools()
    const schoolIndex = schools.findIndex(s => s.id === params.id)

    if (schoolIndex === -1) {
      return NextResponse.json(
        { error: 'المدرسة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json(
        { error: 'اسم المدرسة مطلوب' },
        { status: 400 }
      )
    }

    if (student_count !== undefined && (isNaN(student_count) || student_count < 0)) {
      return NextResponse.json(
        { error: 'عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً' },
        { status: 400 }
      )
    }

    // التحقق من صحة البريد الإلكتروني
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار البريد الإلكتروني (باستثناء المدرسة الحالية)
    if (email) {
      const existingSchool = schools.find(school => school.email === email && school.id !== params.id)
      if (existingSchool) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث المدرسة
    const updatedSchool: MockSchool = {
      ...schools[schoolIndex],
      name,
      name_en: name_en || undefined,
      name_fr: name_fr || undefined,
      address: address || undefined,
      city: city || undefined,
      phone: phone || undefined,
      email: email || undefined,
      website: website || undefined,
      logo_url: logo_url || undefined,
      graduation_date: graduation_date || undefined,
      student_count: student_count ?? schools[schoolIndex].student_count,
      is_active: is_active ?? schools[schoolIndex].is_active,
      settings: settings || schools[schoolIndex].settings,
      updated_at: new Date().toISOString()
    }

    schools[schoolIndex] = updatedSchool
    MockDataManager.saveSchools(schools)

    return NextResponse.json({ 
      message: 'تم تحديث المدرسة بنجاح',
      school: updatedSchool 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مدرسة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const schools = MockDataManager.getSchools()
    const schoolIndex = schools.findIndex(s => s.id === params.id)

    if (schoolIndex === -1) {
      return NextResponse.json(
        { error: 'المدرسة غير موجودة' },
        { status: 404 }
      )
    }

    // حذف المدرسة
    const deletedSchool = schools.splice(schoolIndex, 1)[0]
    MockDataManager.saveSchools(schools)

    return NextResponse.json({ 
      message: 'تم حذف المدرسة بنجاح',
      school: deletedSchool 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
