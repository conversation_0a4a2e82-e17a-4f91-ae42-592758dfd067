(()=>{var e={};e.id=5894,e.ids=[5894],e.modules={1574:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\categories\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,r)=>{"use strict";r.d(a,{A0:()=>d,BF:()=>o,Hj:()=>c,XI:()=>n,nA:()=>u,nd:()=>l});var t=r(60687),s=r(43210),i=r(4780);let n=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:r,className:(0,i.cn)("w-full caption-bottom text-sm",e),...a})}));n.displayName="Table";let d=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("thead",{ref:r,className:(0,i.cn)("[&_tr]:border-b",e),...a}));d.displayName="TableHeader";let o=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tbody",{ref:r,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...a}));o.displayName="TableBody",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tfoot",{ref:r,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let c=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tr",{ref:r,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));c.displayName="TableRow";let l=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("th",{ref:r,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));l.displayName="TableHead";let u=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("td",{ref:r,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));u.displayName="TableCell",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("caption",{ref:r,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29770:(e,a,r)=>{Promise.resolve().then(r.bind(r,96920))},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,r)=>{"use strict";r.d(a,{T:()=>i});var t=r(60687);r(43210);var s=r(4780);function i({className:e,...a}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},45864:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var t=r(65239),s=r(48088),i=r(88170),n=r.n(i),d=r(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(a,o);let c={children:["",{children:["dashboard",{children:["admin",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1574)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/admin/categories/page",pathname:"/dashboard/admin/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54987:(e,a,r)=>{"use strict";r.d(a,{d:()=>n});var t=r(60687);r(43210);var s=r(90270),i=r(4780);function n({className:e,...a}){return(0,t.jsx)(s.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,a,r)=>{"use strict";r.d(a,{Cf:()=>u,Es:()=>m,L3:()=>h,c7:()=>x,lG:()=>d,rr:()=>p,zM:()=>o});var t=r(60687);r(43210);var s=r(26134),i=r(11860),n=r(4780);function d({...e}){return(0,t.jsx)(s.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function l({className:e,...a}){return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function u({className:e,children:a,showCloseButton:r=!0,...d}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(l,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...d,children:[a,r&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function m({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,r)=>{"use strict";r.d(a,{J:()=>n});var t=r(60687);r(43210);var s=r(78148),i=r(4780);function n({className:e,...a}){return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},83721:(e,a,r)=>{"use strict";r.d(a,{Z:()=>s});var t=r(43210);function s(e){let a=t.useRef({value:e,previous:e});return t.useMemo(()=>(a.current.value!==e&&(a.current.previous=a.current.value,a.current.value=e),a.current.previous),[e])}},93661:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},95266:(e,a,r)=>{Promise.resolve().then(r.bind(r,1574))},96474:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,a,r)=>{"use strict";r.d(a,{E:()=>o});var t=r(60687);r(43210);var s=r(8730),i=r(24224),n=r(4780);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:r=!1,...i}){let o=r?s.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(d({variant:a}),e),...i})}},96920:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>k});var t=r(60687),s=r(43210),i=r(29523),n=r(44493),d=r(96834),o=r(89667),c=r(80013),l=r(34729),u=r(54987),x=r(63503),m=r(6211),h=r(21342),p=r(28559),g=r(96474),f=r(93661),b=r(63143),v=r(12597),j=r(13861),y=r(88233),N=r(85814),w=r.n(N);function k(){let[e,a]=(0,s.useState)([]),[r,N]=(0,s.useState)(!0),[k,_]=(0,s.useState)(!1),[C,A]=(0,s.useState)(null),[E,T]=(0,s.useState)({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:1}),P=async()=>{try{N(!0);let e=await fetch("/api/categories?include_inactive=true");if(e.ok){let r=await e.json();a(r.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{N(!1)}},z=()=>{T({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:e.length+1}),A(null)},q=e=>{T({name_ar:e.name_ar,name_en:e.name_en||"",name_fr:e.name_fr||"",slug:e.slug,icon:e.icon||"",description:e.description||"",is_active:e.is_active,order_index:e.order_index}),A(e),_(!0)},D=e=>e.toLowerCase().replace(/[أإآ]/g,"a").replace(/[ة]/g,"h").replace(/[ى]/g,"y").replace(/[ء]/g,"").replace(/\s+/g,"-").replace(/[^\w\-]/g,""),G=e=>{T(a=>({...a,name_ar:e,slug:a.slug||D(e)}))},M=async()=>{try{let e=C?`/api/categories/${C.id}`:"/api/categories",a=C?"PUT":"POST",r=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(E)});if(!r.ok){let e=await r.json();throw Error(e.error||"فشل في حفظ الفئة")}await P(),_(!1),z(),alert(C?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!")}catch(e){console.error("Error saving category:",e),alert(e instanceof Error?e.message:"فشل في حفظ الفئة")}},R=async e=>{if(confirm("هل أنت متأكد من حذف هذه الفئة؟"))try{let a=await fetch(`/api/categories/${e}`,{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في حذف الفئة")}await P(),alert("تم حذف الفئة بنجاح!")}catch(e){console.error("Error deleting category:",e),alert(e instanceof Error?e.message:"فشل في حذف الفئة")}},F=async a=>{let r=e.find(e=>e.id===a);if(r)try{let e=await fetch(`/api/categories/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!r.is_active})});if(!e.ok){let a=await e.json();throw Error(a.error||"فشل في تحديث حالة الفئة")}await P()}catch(e){console.error("Error toggling category status:",e),alert(e instanceof Error?e.message:"فشل في تحديث حالة الفئة")}};return r?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 arabic-text",children:"جاري تحميل الفئات..."})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(w(),{href:"/dashboard/admin",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة الفئات \uD83D\uDCC2"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,t.jsxs)(x.lG,{open:k,onOpenChange:_,children:[(0,t.jsx)(x.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{onClick:z,children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})}),(0,t.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,t.jsx)(x.c7,{children:(0,t.jsx)(x.L3,{className:"arabic-text",children:C?"تعديل الفئة":"إضافة فئة جديدة"})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"name_ar",className:"arabic-text",children:"الاسم بالعربية *"}),(0,t.jsx)(o.p,{id:"name_ar",value:E.name_ar,onChange:e=>G(e.target.value),placeholder:"أدخل اسم الفئة بالعربية",className:"arabic-text"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"name_en",children:"الاسم بالإنجليزية"}),(0,t.jsx)(o.p,{id:"name_en",value:E.name_en,onChange:e=>T(a=>({...a,name_en:e.target.value})),placeholder:"Enter category name in English"})]})]}),(0,t.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,t.jsx)(o.p,{id:"slug",value:E.slug,onChange:e=>T(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة"}),(0,t.jsx)(o.p,{id:"icon",value:E.icon,onChange:e=>T(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDF93"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"description",className:"arabic-text",children:"الوصف"}),(0,t.jsx)(l.T,{id:"description",value:E.description,onChange:e=>T(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة...",className:"arabic-text"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.d,{id:"is_active",checked:E.is_active,onCheckedChange:e=>T(a=>({...a,is_active:e}))}),(0,t.jsx)(c.J,{htmlFor:"is_active",className:"arabic-text",children:"فئة نشطة"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>_(!1),children:"إلغاء"}),(0,t.jsx)(i.$,{onClick:M,children:C?"تحديث":"إضافة"})]})]})]})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"قائمة الفئات"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)(m.XI,{children:[(0,t.jsx)(m.A0,{children:(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nd,{className:"arabic-text",children:"الاسم"}),(0,t.jsx)(m.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,t.jsx)(m.nd,{className:"arabic-text",children:"الحالة"}),(0,t.jsx)(m.nd,{className:"arabic-text",children:"الترتيب"}),(0,t.jsx)(m.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,t.jsx)(m.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)(m.BF,{children:e.map(e=>(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.name_en&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.name_en})]})]})}),(0,t.jsx)(m.nA,{children:(0,t.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,t.jsx)(m.nA,{children:(0,t.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,t.jsx)(m.nA,{children:e.order_index}),(0,t.jsx)(m.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)(h.rI,{children:[(0,t.jsx)(h.ty,{asChild:!0,children:(0,t.jsx)(i.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(h.SQ,{align:"end",children:[(0,t.jsxs)(h._2,{onClick:()=>q(e),children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsx)(h._2,{onClick:()=>F(e.id),children:e.is_active?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"إلغاء التفعيل"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,t.jsxs)(h._2,{onClick:()=>R(e.id),className:"text-red-600",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})]})})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[4447,8773,3044,5336],()=>r(45864));module.exports=t})();