"use strict";(()=>{var e={};e.id=9722,e.ids=[9722],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45750:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(38561);async function p(e){try{let{searchParams:r}=new URL(e.url),t="true"===r.get("include_inactive"),s=u.CN.getCategories();return t||(s=s.filter(e=>e.is_active)),s.sort((e,r)=>e.order_index-r.order_index),i.NextResponse.json({categories:s,total:s.length})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function d(e){try{let{name_ar:r,name_en:t,name_fr:s,slug:o,icon:n,description:a,is_active:p,order_index:d}=await e.json();if(!r||!o)return i.NextResponse.json({error:"الاسم العربي والرابط المختصر مطلوبان"},{status:400});let c=u.CN.getCategories();if(c.find(e=>e.slug===o))return i.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let x={id:u.CN.generateId(),name_ar:r,name_en:t||void 0,name_fr:s||void 0,slug:o,icon:n||void 0,description:a||void 0,is_active:p??!0,order_index:d||c.length+1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return c.push(x),u.CN.saveCategories(c),i.NextResponse.json({message:"تم إضافة الفئة بنجاح",category:x},{status:201})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:l}=c;function v(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(45750));module.exports=s})();