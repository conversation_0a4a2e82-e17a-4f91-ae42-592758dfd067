"use strict";(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},87661:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),d=r(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=t.get("customer"),n=t.get("school"),a=t.get("date_from"),o=t.get("date_to"),u=t.get("limit"),l=t.get("offset"),p=t.get("sort_by")||"created_at",c=t.get("sort_order")||"desc",g=d.CN.getOrders();r&&"all"!==r&&(g=g.filter(e=>e.status===r)),s&&(g=g.filter(e=>e.customer_name.toLowerCase().includes(s.toLowerCase())||e.customer_email.toLowerCase().includes(s.toLowerCase())||e.order_number.toLowerCase().includes(s.toLowerCase()))),n&&"all"!==n&&(g=g.filter(e=>e.school_id===n)),a&&(g=g.filter(e=>e.created_at>=a)),o&&(g=g.filter(e=>e.created_at<=o)),g.sort((e,t)=>{let r=e[p],s=t[p];return(("created_at"===p||"updated_at"===p)&&(r=new Date(r).getTime(),s=new Date(s).getTime()),"desc"===c)?s>r?1:-1:r>s?1:-1});let m=g.length;if(u&&l){let e=parseInt(u),t=parseInt(l);g=g.slice(t,t+e)}let f=d.CN.getOrders(),_={total:f.length,pending:f.filter(e=>"pending"===e.status).length,confirmed:f.filter(e=>"confirmed"===e.status).length,in_production:f.filter(e=>"in_production"===e.status).length,shipped:f.filter(e=>"shipped"===e.status).length,delivered:f.filter(e=>"delivered"===e.status).length,cancelled:f.filter(e=>"cancelled"===e.status).length,total_revenue:f.filter(e=>"paid"===e.payment_status).reduce((e,t)=>e+t.total,0),pending_payments:f.filter(e=>"pending"===e.payment_status).reduce((e,t)=>e+t.total,0)};return i.NextResponse.json({orders:g,total:m,stats:_})}catch(e){return console.error("Error fetching orders:",e),i.NextResponse.json({error:"خطأ في جلب الطلبات"},{status:500})}}async function l(e){try{let{customer_id:t,customer_name:r,customer_email:s,customer_phone:n,items:a,shipping_address:o,payment_method:u,notes:l,school_id:p,school_name:c}=await e.json();if(!r||!s||!a||0===a.length||!o)return i.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let g=a.reduce((e,t)=>e+t.unit_price*t.quantity,0),m=.05*g,f=g>500?0:25,_=d.CN.getOrders(),h={id:d.CN.generateId(),order_number:d.CN.generateOrderNumber(),customer_id:t||d.CN.generateId(),customer_name:r,customer_email:s,customer_phone:n,status:"pending",items:a.map(e=>({...e,id:d.CN.generateId(),order_id:d.CN.generateId()})),subtotal:g,tax:m,shipping_cost:f,total:g+m+f,payment_status:"pending",payment_method:u,shipping_address:o,notes:l,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),school_id:p,school_name:c};return _.push(h),d.CN.saveOrders(_),i.NextResponse.json({message:"تم إنشاء الطلب بنجاح",order:h},{status:201})}catch(e){return console.error("Error creating order:",e),i.NextResponse.json({error:"خطأ في إنشاء الطلب"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:g,serverHooks:m}=p;function f(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(87661));module.exports=s})();