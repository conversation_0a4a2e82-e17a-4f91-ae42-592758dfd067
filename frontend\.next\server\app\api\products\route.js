"use strict";(()=>{var e={};e.id=3146,e.ids=[3146],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53262:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),p=r(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("category"),s=t.get("available"),a=t.get("limit"),o=t.get("offset"),n=p.CN.getProducts();r&&"all"!==r&&(n=n.filter(e=>e.category===r)),"true"===s?n=n.filter(e=>!0===e.is_available):"false"===s&&(n=n.filter(e=>!1===e.is_available)),n.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());let u=a?parseInt(a):n.length,c=o?parseInt(o):0,d=n.slice(c,c+u);return i.NextResponse.json({products:d,total:n.length})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e){try{let{name:t,description:r,category:s,price:a,rental_price:o,colors:n,sizes:u,images:c,stock_quantity:d,is_available:l,features:g,specifications:x}=await e.json();if(!t||!r||!s||!a)return i.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let f=p.CN.getProducts(),v={id:p.CN.generateId(),name:t,description:r,category:s,price:parseFloat(a),rental_price:o?parseFloat(o):void 0,colors:n||[],sizes:u||[],images:c||[],stock_quantity:parseInt(d)||0,is_available:l??!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),features:g||[],specifications:x||{}};return f.push(v),p.CN.saveProducts(f),i.NextResponse.json({message:"تم إضافة المنتج بنجاح",product:v},{status:201})}catch(e){return console.error("Unexpected error:",e),i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:x}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(53262));module.exports=s})();