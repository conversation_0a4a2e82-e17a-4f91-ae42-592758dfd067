(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5894],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11296:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var r=a(95155),s=a(12115),n=a(30285),i=a(66695),d=a(26126),o=a(62523),l=a(85057),c=a(88539),u=a(80333),x=a(54165),m=a(85127),g=a(44838),h=a(35169),f=a(84616),p=a(5623),v=a(13717),b=a(78749),j=a(92657),y=a(62525),N=a(6874),w=a.n(N);function k(){let[e,t]=(0,s.useState)([]),[a,N]=(0,s.useState)(!0),[k,_]=(0,s.useState)(!1),[A,C]=(0,s.useState)(null),[z,E]=(0,s.useState)({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:1}),T=async()=>{try{N(!0);let e=await fetch("/api/categories?include_inactive=true");if(e.ok){let a=await e.json();t(a.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{N(!1)}};(0,s.useEffect)(()=>{T()},[]);let M=()=>{E({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:e.length+1}),C(null)},F=e=>{E({name_ar:e.name_ar,name_en:e.name_en||"",name_fr:e.name_fr||"",slug:e.slug,icon:e.icon||"",description:e.description||"",is_active:e.is_active,order_index:e.order_index}),C(e),_(!0)},R=e=>e.toLowerCase().replace(/[أإآ]/g,"a").replace(/[ة]/g,"h").replace(/[ى]/g,"y").replace(/[ء]/g,"").replace(/\s+/g,"-").replace(/[^\w\-]/g,""),S=e=>{E(t=>({...t,name_ar:e,slug:t.slug||R(e)}))},J=async()=>{try{let e=A?"/api/categories/".concat(A.id):"/api/categories",t=A?"PUT":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(z)});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في حفظ الفئة")}await T(),_(!1),M(),alert(A?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!")}catch(e){console.error("Error saving category:",e),alert(e instanceof Error?e.message:"فشل في حفظ الفئة")}},D=async e=>{if(confirm("هل أنت متأكد من حذف هذه الفئة؟"))try{let t=await fetch("/api/categories/".concat(e),{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في حذف الفئة")}await T(),alert("تم حذف الفئة بنجاح!")}catch(e){console.error("Error deleting category:",e),alert(e instanceof Error?e.message:"فشل في حذف الفئة")}},L=async t=>{let a=e.find(e=>e.id===t);if(a)try{let e=await fetch("/api/categories/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!a.is_active})});if(!e.ok){let t=await e.json();throw Error(t.error||"فشل في تحديث حالة الفئة")}await T()}catch(e){console.error("Error toggling category status:",e),alert(e instanceof Error?e.message:"فشل في تحديث حالة الفئة")}};return a?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 arabic-text",children:"جاري تحميل الفئات..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(w(),{href:"/dashboard/admin",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة الفئات \uD83D\uDCC2"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,r.jsxs)(x.lG,{open:k,onOpenChange:_,children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsxs)(n.$,{onClick:M,children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})}),(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsx)(x.c7,{children:(0,r.jsx)(x.L3,{className:"arabic-text",children:A?"تعديل الفئة":"إضافة فئة جديدة"})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"name_ar",className:"arabic-text",children:"الاسم بالعربية *"}),(0,r.jsx)(o.p,{id:"name_ar",value:z.name_ar,onChange:e=>S(e.target.value),placeholder:"أدخل اسم الفئة بالعربية",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"name_en",children:"الاسم بالإنجليزية"}),(0,r.jsx)(o.p,{id:"name_en",value:z.name_en,onChange:e=>E(t=>({...t,name_en:e.target.value})),placeholder:"Enter category name in English"})]})]}),(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,r.jsx)(o.p,{id:"slug",value:z.slug,onChange:e=>E(t=>({...t,slug:e.target.value})),placeholder:"category-slug"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة"}),(0,r.jsx)(o.p,{id:"icon",value:z.icon,onChange:e=>E(t=>({...t,icon:e.target.value})),placeholder:"\uD83C\uDF93"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"description",className:"arabic-text",children:"الوصف"}),(0,r.jsx)(c.T,{id:"description",value:z.description,onChange:e=>E(t=>({...t,description:e.target.value})),placeholder:"وصف الفئة...",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.d,{id:"is_active",checked:z.is_active,onCheckedChange:e=>E(t=>({...t,is_active:e}))}),(0,r.jsx)(l.J,{htmlFor:"is_active",className:"arabic-text",children:"فئة نشطة"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>_(!1),children:"إلغاء"}),(0,r.jsx)(n.$,{onClick:J,children:A?"تحديث":"إضافة"})]})]})]})]})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{className:"arabic-text",children:"قائمة الفئات"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)(m.XI,{children:[(0,r.jsx)(m.A0,{children:(0,r.jsxs)(m.Hj,{children:[(0,r.jsx)(m.nd,{className:"arabic-text",children:"الاسم"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الحالة"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الترتيب"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,r.jsx)(m.BF,{children:e.map(e=>(0,r.jsxs)(m.Hj,{children:[(0,r.jsx)(m.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,r.jsx)("span",{className:"text-lg",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.name_en&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.name_en})]})]})}),(0,r.jsx)(m.nA,{children:(0,r.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,r.jsx)(m.nA,{children:(0,r.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,r.jsx)(m.nA,{children:e.order_index}),(0,r.jsx)(m.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,r.jsx)(m.nA,{children:(0,r.jsxs)(g.rI,{children:[(0,r.jsx)(g.ty,{asChild:!0,children:(0,r.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(g.SQ,{align:"end",children:[(0,r.jsxs)(g._2,{onClick:()=>F(e),children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,r.jsx)(g._2,{onClick:()=>L(e.id),children:e.is_active?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إلغاء التفعيل"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,r.jsxs)(g._2,{onClick:()=>D(e.id),className:"text-red-600",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})]})})}},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},18454:(e,t,a)=>{Promise.resolve().then(a.bind(a,11296))},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(59434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,l=n?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),t),...o})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>d});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,c=o?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...l})}},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},44838:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>o,_2:()=>l,lp:()=>c,mB:()=>u,rI:()=>i,ty:()=>d});var r=a(95155);a(12115);var s=a(48698),n=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function l(e){let{className:t,inset:a,variant:i="default",...d}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":i,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d})}function c(e){let{className:t,inset:a,...i}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},45503:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});var r=a(12115);function s(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>g,c7:()=>x,lG:()=>d,rr:()=>h,zM:()=>o});var r=a(95155);a(12115);var s=a(15452),n=a(54416),i=a(59434);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,showCloseButton:d=!0,...o}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,d&&(0,r.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},78749:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},80333:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var r=a(95155);a(12115);var s=a(4884),n=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155);a(12115);var s=a(40968),n=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>d,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>u,nd:()=>c});var r=a(95155),s=a(12115),n=a(59434);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",a),...s})})});i.displayName="Table";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",a),...s})});d.displayName="TableHeader";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",a),...s})});o.displayName="TableBody",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...s})}).displayName="TableFooter";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...s})});l.displayName="TableRow";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...s})});c.displayName="TableHead";let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...s})});u.displayName="TableCell",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",a),...s})}).displayName="TableCaption"},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,5486,5148,8698,6874,1056,8441,1684,7358],()=>t(18454)),_N_E=e.O()}]);