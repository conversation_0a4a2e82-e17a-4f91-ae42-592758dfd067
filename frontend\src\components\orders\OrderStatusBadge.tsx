"use client"

import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Clock,
  CheckCircle,
  Package,
  Truck,
  AlertCircle,
  XCircle,
  RefreshCw
} from 'lucide-react'

// أنواع حالات الطلبات
export type OrderStatus = 
  | 'pending'           // في الانتظار
  | 'confirmed'         // مؤكد
  | 'processing'        // قيد المعالجة
  | 'in_production'     // قيد الإنتاج
  | 'ready_to_ship'     // جاهز للشحن
  | 'shipped'           // تم الشحن
  | 'out_for_delivery'  // خرج للتوصيل
  | 'delivered'         // تم التسليم
  | 'cancelled'         // ملغي
  | 'returned'          // مرتجع
  | 'refunded'          // مسترد

interface OrderStatusConfig {
  label: string
  color: string
  bgColor: string
  icon: React.ReactNode
  progress: number
  description: string
}

// إعدادات حالات الطلبات
const statusConfig: Record<OrderStatus, OrderStatusConfig> = {
  pending: {
    label: 'في الانتظار',
    color: 'text-yellow-800',
    bgColor: 'bg-yellow-100',
    icon: <Clock className="h-4 w-4" />,
    progress: 10,
    description: 'طلبك في انتظار التأكيد'
  },
  confirmed: {
    label: 'مؤكد',
    color: 'text-blue-800',
    bgColor: 'bg-blue-100',
    icon: <CheckCircle className="h-4 w-4" />,
    progress: 20,
    description: 'تم تأكيد طلبك بنجاح'
  },
  processing: {
    label: 'قيد المعالجة',
    color: 'text-purple-800',
    bgColor: 'bg-purple-100',
    icon: <RefreshCw className="h-4 w-4" />,
    progress: 30,
    description: 'جاري معالجة طلبك'
  },
  in_production: {
    label: 'قيد الإنتاج',
    color: 'text-orange-800',
    bgColor: 'bg-orange-100',
    icon: <Package className="h-4 w-4" />,
    progress: 50,
    description: 'جاري تحضير منتجاتك'
  },
  ready_to_ship: {
    label: 'جاهز للشحن',
    color: 'text-indigo-800',
    bgColor: 'bg-indigo-100',
    icon: <Package className="h-4 w-4" />,
    progress: 70,
    description: 'طلبك جاهز للشحن'
  },
  shipped: {
    label: 'تم الشحن',
    color: 'text-blue-800',
    bgColor: 'bg-blue-100',
    icon: <Truck className="h-4 w-4" />,
    progress: 80,
    description: 'تم شحن طلبك'
  },
  out_for_delivery: {
    label: 'خرج للتوصيل',
    color: 'text-green-800',
    bgColor: 'bg-green-100',
    icon: <Truck className="h-4 w-4" />,
    progress: 90,
    description: 'طلبك في طريقه إليك'
  },
  delivered: {
    label: 'تم التسليم',
    color: 'text-green-800',
    bgColor: 'bg-green-100',
    icon: <CheckCircle className="h-4 w-4" />,
    progress: 100,
    description: 'تم تسليم طلبك بنجاح'
  },
  cancelled: {
    label: 'ملغي',
    color: 'text-red-800',
    bgColor: 'bg-red-100',
    icon: <XCircle className="h-4 w-4" />,
    progress: 0,
    description: 'تم إلغاء الطلب'
  },
  returned: {
    label: 'مرتجع',
    color: 'text-gray-800',
    bgColor: 'bg-gray-100',
    icon: <RefreshCw className="h-4 w-4" />,
    progress: 0,
    description: 'تم إرجاع الطلب'
  },
  refunded: {
    label: 'مسترد',
    color: 'text-gray-800',
    bgColor: 'bg-gray-100',
    icon: <RefreshCw className="h-4 w-4" />,
    progress: 0,
    description: 'تم استرداد المبلغ'
  }
}

interface OrderStatusBadgeProps {
  status: OrderStatus
  showIcon?: boolean
  showProgress?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function OrderStatusBadge({
  status,
  showIcon = true,
  showProgress = false,
  size = 'md',
  className = ""
}: OrderStatusBadgeProps) {
  const config = statusConfig[status]
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  }

  return (
    <div className={`inline-flex flex-col gap-2 ${className}`}>
      <Badge 
        variant="secondary" 
        className={`${config.bgColor} ${config.color} ${sizeClasses[size]} arabic-text`}
      >
        {showIcon && (
          <span className="mr-1">
            {config.icon}
          </span>
        )}
        {config.label}
      </Badge>
      
      {showProgress && (
        <div className="w-full">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-gray-600 dark:text-gray-400 arabic-text">
              {config.description}
            </span>
            <span className="text-xs text-gray-500">
              {config.progress}%
            </span>
          </div>
          <Progress value={config.progress} className="h-2" />
        </div>
      )}
    </div>
  )
}

// مكون تفصيلي لحالة الطلب
interface OrderStatusDetailProps {
  status: OrderStatus
  estimatedDate?: string
  trackingNumber?: string
  className?: string
}

export function OrderStatusDetail({
  status,
  estimatedDate,
  trackingNumber,
  className = ""
}: OrderStatusDetailProps) {
  const config = statusConfig[status]

  return (
    <div className={`p-4 rounded-lg border ${config.bgColor} ${className}`}>
      <div className="flex items-center gap-3 mb-3">
        <div className={`p-2 rounded-full bg-white ${config.color}`}>
          {config.icon}
        </div>
        <div>
          <h3 className={`font-medium ${config.color} arabic-text`}>
            {config.label}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
            {config.description}
          </p>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-gray-600 dark:text-gray-400 arabic-text">
            تقدم الطلب
          </span>
          <span className="text-xs text-gray-500">
            {config.progress}%
          </span>
        </div>
        <Progress value={config.progress} className="h-2" />
      </div>

      {/* Additional Info */}
      <div className="space-y-2 text-sm">
        {estimatedDate && (
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400 arabic-text">
              التاريخ المتوقع:
            </span>
            <span className="font-medium">
              {new Date(estimatedDate).toLocaleDateString('en-US')}
            </span>
          </div>
        )}
        
        {trackingNumber && (
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400 arabic-text">
              رقم التتبع:
            </span>
            <span className="font-mono font-medium">
              {trackingNumber}
            </span>
          </div>
        )}
      </div>
    </div>
  )
}

// دالة مساعدة للحصول على الحالة التالية
export function getNextStatus(currentStatus: OrderStatus): OrderStatus | null {
  const statusFlow: OrderStatus[] = [
    'pending',
    'confirmed',
    'processing',
    'in_production',
    'ready_to_ship',
    'shipped',
    'out_for_delivery',
    'delivered'
  ]

  const currentIndex = statusFlow.indexOf(currentStatus)
  if (currentIndex === -1 || currentIndex === statusFlow.length - 1) {
    return null
  }
  
  return statusFlow[currentIndex + 1]
}

// دالة مساعدة للتحقق من إمكانية الإلغاء
export function canCancelOrder(status: OrderStatus): boolean {
  return ['pending', 'confirmed', 'processing'].includes(status)
}

// دالة مساعدة للتحقق من إمكانية الإرجاع
export function canReturnOrder(status: OrderStatus): boolean {
  return status === 'delivered'
}

// دالة مساعدة للحصول على لون الحالة
export function getStatusColor(status: OrderStatus): string {
  return statusConfig[status]?.color || 'text-gray-600'
}

// دالة مساعدة للحصول على أيقونة الحالة
export function getStatusIcon(status: OrderStatus): React.ReactNode {
  return statusConfig[status]?.icon || <Clock className="h-4 w-4" />
}

// دالة مساعدة للحصول على نسبة التقدم
export function getStatusProgress(status: OrderStatus): number {
  return statusConfig[status]?.progress || 0
}
