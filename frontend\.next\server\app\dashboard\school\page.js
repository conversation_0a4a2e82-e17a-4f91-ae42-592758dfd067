(()=>{var e={};e.id=5758,e.ids=[5758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21136:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(60687),r=a(43210),l=a(63213),i=a(32884),c=a(44493),n=a(29523),d=a(96834),x=a(46657),m=a(85763),o=a(89667),h=a(31158),j=a(96474),u=a(41312),p=a(28561),v=a(23928),N=a(25541),f=a(53411),g=a(19080),b=a(99270),y=a(10022),w=a(27235),k=a(80462),A=a(16023),_=a(13861),$=a(63143),Z=a(48340),z=a(33872),P=a(97992);function C(){let{user:e,profile:s,loading:a}=(0,l.A)(),[C,q]=(0,r.useState)("overview"),[B,T]=(0,r.useState)([]),[M,R]=(0,r.useState)([]),[G,W]=(0,r.useState)({total_students:0,active_orders:0,total_revenue:0,completion_rate:0}),[D,E]=(0,r.useState)(!0),[U,L]=(0,r.useState)(""),S=e=>{switch(e){case"active":case"completed":return"bg-green-100 text-green-800";case"graduated":case"confirmed":return"bg-blue-100 text-blue-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"pending":return"bg-yellow-100 text-yellow-800"}},X=e=>{switch(e){case"active":return"نشط";case"graduated":return"متخرج";case"inactive":return"غير نشط";case"pending":return"في الانتظار";case"confirmed":return"مؤكد";case"completed":return"مكتمل";default:return"غير معروف"}},F=B.filter(e=>e.name.toLowerCase().includes(U.toLowerCase())||e.class.toLowerCase().includes(U.toLowerCase()));return a||D?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"لوحة تحكم المدرسة \uD83C\uDFEB"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة طلاب المدرسة وطلبات أزياء التخرج"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تصدير البيانات"]}),(0,t.jsxs)(n.$,{size:"sm",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إضافة طالب"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلاب"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:G.total_students})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات النشطة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:G.active_orders})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الإيرادات"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[G.total_revenue.toFixed(2)," درهم"]})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"معدل الإنجاز"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[G.completion_rate,"%"]})]}),(0,t.jsx)(N.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsxs)(m.tU,{value:C,onValueChange:q,children:[(0,t.jsxs)(m.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(m.Xi,{value:"overview",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"نظرة عامة"]}),(0,t.jsxs)(m.Xi,{value:"students",className:"arabic-text",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الطلاب"]}),(0,t.jsxs)(m.Xi,{value:"orders",className:"arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"track-orders",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"تتبع الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"reports",className:"arabic-text",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"التقارير"]})]}),(0,t.jsxs)(m.av,{value:"overview",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إحصائيات الطلبات الشهرية"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(f.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"رسم بياني للطلبات"})]})})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"توزيع الطلبات حسب الصف"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(w.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"رسم دائري للتوزيع"})]})})})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"arabic-text",children:"النشاط الأخير"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:M.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,t.jsx)(p.A,{className:"h-5 w-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.student_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.student_class," - ",e.items.join(", ")]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-medium",children:[e.total," درهم"]}),(0,t.jsx)(d.E,{className:S(e.status),children:X(e.status)})]})]},e.id))})})]})]}),(0,t.jsxs)(m.av,{value:"students",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"البحث عن طالب...",value:U,onChange:e=>L(e.target.value),className:"pl-10 arabic-text"})]})}),(0,t.jsxs)(n.$,{variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"فلترة"]}),(0,t.jsxs)(n.$,{variant:"outline",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"استيراد"]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"قائمة الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إدارة طلاب المدرسة وطلباتهم"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:F.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.class}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)(d.E,{className:S(e.status),children:X(e.status)}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text",children:[e.orders_count," طلب - ",e.total_spent," درهم"]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)($.A,{className:"h-4 w-4"})})]})]})]},e.id))})})]})]}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلبات المدرسة"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"طلب جماعي جديد"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:M.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:[e.student_name," - ",e.student_class]})]}),(0,t.jsx)(d.E,{className:S(e.status),children:X(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text mb-2",children:"العناصر المطلوبة:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.items.map((e,s)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"تعديل الطلب"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل الفاتورة"]})]})]})})]},e.id))})]}),(0,t.jsx)(m.av,{value:"reports",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلبات الشهري"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات مفصلة عن طلبات الشهر الحالي"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-bold",children:M.length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المكتملة:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:M.filter(e=>"completed"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المعلقة:"}),(0,t.jsx)("span",{className:"font-bold text-yellow-600",children:M.filter(e=>"pending"===e.status).length})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات عن نشاط الطلاب"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلاب النشطون:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:B.filter(e=>"active"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المتخرجون:"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:B.filter(e=>"graduated"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"متوسط الإنفاق:"}),(0,t.jsxs)("span",{className:"font-bold",children:[(B.reduce((e,s)=>e+s.total_spent,0)/B.length).toFixed(2)," درهم"]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]})]})}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلبات المدرسة"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"طلب جماعي جديد"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:M.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:[e.student_name," - ",e.student_class]})]}),(0,t.jsx)(d.E,{className:S(e.status),children:X(e.status)})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text mb-2",children:"العناصر المطلوبة:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.items.map((e,s)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"تعديل الطلب"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل الفاتورة"]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"track-orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تتبع طلبات المدرسة"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{placeholder:"بحث برقم الطلب أو اسم الطالب...",className:"w-80"}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"بحث"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"حالة الطلب:"}),(0,t.jsxs)("select",{className:"px-3 py-1 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"جميع الحالات"}),(0,t.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,t.jsx)("option",{value:"processing",children:"قيد التحضير"}),(0,t.jsx)("option",{value:"shipped",children:"تم الشحن"}),(0,t.jsx)("option",{value:"delivered",children:"تم التسليم"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"الفترة الزمنية:"}),(0,t.jsxs)("select",{className:"px-3 py-1 border rounded-md",children:[(0,t.jsx)("option",{value:"week",children:"آخر أسبوع"}),(0,t.jsx)("option",{value:"month",children:"آخر شهر"}),(0,t.jsx)("option",{value:"quarter",children:"آخر 3 أشهر"})]})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:M.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(c.BT,{className:"arabic-text",children:["الطالب: ",e.student_name," | تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.E,{className:S(e.status),children:X(e.status)}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تفاصيل"})]})]})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:"مراحل الطلب"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الاستلام"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${["processing","shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300"}`}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"قيد التحضير"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${["shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300"}`}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الشحن"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${"delivered"===e.status?"bg-green-500":"bg-gray-300"}`}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم التسليم"})]})]}),(0,t.jsx)(x.k,{value:getOrderProgress(e.status),className:"h-2"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"عدد القطع"}),(0,t.jsx)("p",{className:"font-medium",children:e.items.length})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المبلغ الإجمالي"}),(0,t.jsxs)("p",{className:"font-medium",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"طريقة الدفع"}),(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.payment_method})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"اتصال بالطالب"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"إرسال رسالة"})]}),"shipped"===e.status&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الشحنة"})]})]})]})})]},e.id))})]}),(0,t.jsx)(m.av,{value:"reports",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلبات الشهري"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات مفصلة عن طلبات الشهر الحالي"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-bold",children:M.length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المكتملة:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:M.filter(e=>"completed"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلبات المعلقة:"}),(0,t.jsx)("span",{className:"font-bold text-yellow-600",children:M.filter(e=>"pending"===e.status).length})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"تقرير الطلاب"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إحصائيات عن نشاط الطلاب"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الطلاب النشطون:"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:B.filter(e=>"active"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المتخرجون:"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:B.filter(e=>"graduated"===e.status).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"متوسط الإنفاق:"}),(0,t.jsxs)("span",{className:"font-bold",children:[(B.reduce((e,s)=>e+s.total_spent,0)/B.length).toFixed(2)," درهم"]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(n.$,{className:"w-full",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تحميل التقرير"]})})]})})]})]})})]})]})]})}},27235:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33872:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},35266:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\school\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\school\\page.tsx","default")},46657:(e,s,a)=>{"use strict";a.d(s,{k:()=>b});var t=a(60687),r=a(43210),l=a(11273),i=a(14163),c="Progress",[n,d]=(0,l.A)(c),[x,m]=n(c),o=r.forwardRef((e,s)=>{var a,r;let{__scopeProgress:l,value:c=null,max:n,getValueLabel:d=u,...m}=e;(n||0===n)&&!N(n)&&console.error((a=`${n}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let o=N(n)?n:100;null===c||f(c,o)||console.error((r=`${c}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=f(c,o)?c:null,j=v(h)?d(h,o):void 0;return(0,t.jsx)(x,{scope:l,value:h,max:o,children:(0,t.jsx)(i.sG.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":v(h)?h:void 0,"aria-valuetext":j,role:"progressbar","data-state":p(h,o),"data-value":h??void 0,"data-max":o,...m,ref:s})})});o.displayName=c;var h="ProgressIndicator",j=r.forwardRef((e,s)=>{let{__scopeProgress:a,...r}=e,l=m(h,a);return(0,t.jsx)(i.sG.div,{"data-state":p(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function u(e,s){return`${Math.round(e/s*100)}%`}function p(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function v(e){return"number"==typeof e}function N(e){return v(e)&&!isNaN(e)&&e>0}function f(e,s){return v(e)&&!isNaN(e)&&e<=s&&e>=0}j.displayName=h;var g=a(4780);function b({className:e,value:s,...a}){return(0,t.jsx)(o,{"data-slot":"progress",className:(0,g.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...a,children:(0,t.jsx)(j,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},49009:(e,s,a)=>{Promise.resolve().then(a.bind(a,21136))},58737:(e,s,a)=>{Promise.resolve().then(a.bind(a,35266))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},76036:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>x,routeModule:()=>o,tree:()=>d});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),c=a(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(s,n);let d={children:["",{children:["dashboard",{children:["school",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,35266)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\school\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\school\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},o=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/school/page",pathname:"/dashboard/school",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},85763:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>i});var t=a(60687);a(43210);var r=a(55146),l=a(4780);function i({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...s})}function c({className:e,...s}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...s})}function n({className:e,...s}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function d({className:e,...s}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...s})}},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,5374,5336,2884],()=>a(76036));module.exports=t})();