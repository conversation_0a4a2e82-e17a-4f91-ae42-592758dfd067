(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8365],{5196:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7982:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var s=a(95155),r=a(12115),l=a(40283),o=a(11581),n=a(30285),i=a(62523),c=a(85057),d=a(66695),m=a(55365),u=a(28883),h=a(32919),p=a(78749),x=a(92657),g=a(6874),v=a.n(g);function f(e){let{onToggleMode:t}=e,{signIn:a,loading:g}=(0,l.A)(),{t:f}=(0,o.B)(),[y,b]=(0,r.useState)({email:"",password:""}),[j,w]=(0,r.useState)(!1),[N,k]=(0,r.useState)(""),[S,A]=(0,r.useState)(!1),C=async e=>{if(e.preventDefault(),k(""),!y.email||!y.password)return void k("يرجى ملء جميع الحقول المطلوبة");let{error:t}=await a(y.email,y.password);t&&k(t.message)},T=(e,t)=>{b(a=>({...a,[e]:t}))};return(0,s.jsxs)(d.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-2xl font-bold",children:f("auth.login")}),(0,s.jsx)(d.BT,{children:"أدخل بياناتك للوصول إلى حسابك"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[N&&(0,s.jsx)(m.Fc,{variant:"destructive",children:(0,s.jsx)(m.TN,{children:N})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",children:f("auth.email")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:y.email,onChange:e=>T("email",e.target.value),className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"password",children:f("auth.password")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"password",type:j?"text":"password",placeholder:"••••••••",value:y.password,onChange:e=>T("password",e.target.value),className:"pl-10 pr-10",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>w(!j),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:j?(0,s.jsx)(p.A,{className:"h-4 w-4"}):(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{id:"remember",type:"checkbox",checked:S,onChange:e=>A(e.target.checked),className:"rounded border-gray-300"}),(0,s.jsx)(c.J,{htmlFor:"remember",className:"text-sm",children:f("auth.rememberMe")})]}),(0,s.jsx)(v(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:underline",children:f("auth.forgotPassword")})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",disabled:g,children:f(g?"common.loading":"auth.login")}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[f("auth.dontHaveAccount")," ",(0,s.jsx)("button",{type:"button",onClick:t,className:"text-blue-600 hover:underline font-medium",children:f("auth.register")})]})})]})})]})}var y=a(59409),b=a(71007),j=a(19420),w=a(53896);function N(e){let{onToggleMode:t}=e,{signUp:a,loading:g}=(0,l.A)(),{t:v}=(0,o.B)(),[f,N]=(0,r.useState)({email:"",password:"",confirmPassword:"",full_name:"",phone:"",role:l.g.STUDENT,school_name:""}),[k,S]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),[T,_]=(0,r.useState)(""),[E,M]=(0,r.useState)(""),I=async e=>{if(e.preventDefault(),_(""),M(""),!f.email||!f.password||!f.full_name)return void _("يرجى ملء جميع الحقول المطلوبة");if(f.password!==f.confirmPassword)return void _("كلمات المرور غير متطابقة");if(f.password.length<6)return void _("كلمة المرور يجب أن تكون 6 أحرف على الأقل");if(f.role===l.g.SCHOOL&&!f.school_name)return void _("اسم المدرسة مطلوب للمدارس");let t={full_name:f.full_name,role:f.role,phone:f.phone||void 0,school_name:f.role===l.g.SCHOOL?f.school_name:void 0},{error:s}=await a(f.email,f.password,t);s?_(s.message):M("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني")},L=(e,t)=>{N(a=>({...a,[e]:t}))},P=[{value:l.g.STUDENT,label:"طالب",icon:"\uD83C\uDF93"},{value:l.g.SCHOOL,label:"مدرسة",icon:"\uD83C\uDFEB"},{value:l.g.DELIVERY,label:"شريك توصيل",icon:"\uD83D\uDE9A"}];return(0,s.jsxs)(d.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-2xl font-bold",children:v("auth.register")}),(0,s.jsx)(d.BT,{children:"أنشئ حساباً جديداً للبدء"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[T&&(0,s.jsx)(m.Fc,{variant:"destructive",children:(0,s.jsx)(m.TN,{children:T})}),E&&(0,s.jsx)(m.Fc,{children:(0,s.jsx)(m.TN,{children:E})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"full_name",children:v("auth.firstName")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"full_name",type:"text",placeholder:"الاسم الكامل",value:f.full_name,onChange:e=>L("full_name",e.target.value),className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",children:v("auth.email")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:f.email,onChange:e=>L("email",e.target.value),className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"phone",children:v("auth.phone")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"phone",type:"tel",placeholder:"+212 6XX XXX XXX",value:f.phone,onChange:e=>L("phone",e.target.value),className:"pl-10"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"role",children:"نوع الحساب"}),(0,s.jsxs)(y.l6,{value:f.role,onValueChange:e=>L("role",e),children:[(0,s.jsx)(y.bq,{children:(0,s.jsx)(y.yv,{placeholder:"اختر نوع الحساب"})}),(0,s.jsx)(y.gC,{children:P.map(e=>(0,s.jsx)(y.eb,{value:e.value,children:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.icon}),e.label]})},e.value))})]})]}),f.role===l.g.SCHOOL&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"school_name",children:"اسم المدرسة"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"school_name",type:"text",placeholder:"اسم المدرسة أو المؤسسة التعليمية",value:f.school_name,onChange:e=>L("school_name",e.target.value),className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"password",children:v("auth.password")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"password",type:k?"text":"password",placeholder:"••••••••",value:f.password,onChange:e=>L("password",e.target.value),className:"pl-10 pr-10",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>S(!k),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:k?(0,s.jsx)(p.A,{className:"h-4 w-4"}):(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"confirmPassword",children:v("auth.confirmPassword")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{id:"confirmPassword",type:A?"text":"password",placeholder:"••••••••",value:f.confirmPassword,onChange:e=>L("confirmPassword",e.target.value),className:"pl-10 pr-10",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!A),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:A?(0,s.jsx)(p.A,{className:"h-4 w-4"}):(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",disabled:g,children:v(g?"common.loading":"auth.register")}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[v("auth.alreadyHaveAccount")," ",(0,s.jsx)("button",{type:"button",onClick:t,className:"text-blue-600 hover:underline font-medium",children:v("auth.login")})]})})]})})]})}var k=a(66688),S=a(19407),A=a(87949);function C(){let[e,t]=(0,r.useState)(!0);return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,s.jsxs)("header",{className:"container mx-auto px-4 py-6 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(A.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Graduation Toqs"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(S.J,{}),(0,s.jsx)(k.U,{})]})]}),(0,s.jsx)("main",{className:"container mx-auto px-4 py-12 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:e?(0,s.jsx)(f,{onToggleMode:()=>t(!1)}):(0,s.jsx)(N,{onToggleMode:()=>t(!0)})})}),(0,s.jsx)("footer",{className:"absolute bottom-0 w-full bg-gray-100 dark:bg-gray-800 py-4",children:(0,s.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"\xa9 2024 Graduation Toqs - أول منصة مغربية لأزياء التخرج"})})})]})}},19420:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},28883:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},32919:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},34869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},40283:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,AuthProvider:()=>n,g:()=>l});var s=a(95155),r=a(12115),l=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});let o=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[a,l]=(0,r.useState)(null),[n,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[m,u]=(0,r.useState)(!1);(0,r.useEffect)(()=>{u(!0)},[]),(0,r.useEffect)(()=>{m&&(async()=>{try{let e=localStorage.getItem("mockUser"),t=localStorage.getItem("mockProfile");if(e&&t){let a=JSON.parse(e),s=JSON.parse(t);if(a&&s&&a.id&&s.id){let e=localStorage.getItem("sessionTimestamp"),t=Date.now(),r=e?t-parseInt(e):0;e&&r<864e5?(l(a),i(s),console.log("User data loaded from localStorage:",{userData:a,profileData:s})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{d(!1)}})()},[m]),(0,r.useEffect)(()=>{if(!a||!n)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},t=["click","keypress","scroll","mousemove"];return t.forEach(t=>{document.addEventListener(t,e,{passive:!0})}),()=>{t.forEach(t=>{document.removeEventListener(t,e)})}},[a,n]);let h=async(e,t,a)=>(console.log("Sign up:",e,a),{data:{user:{id:"1",email:e}},error:null}),p=async(e,t)=>{console.log("Sign in:",e);let a={id:"1",email:e},s="student";e.includes("admin")?s="admin":e.includes("school")?s="school":e.includes("delivery")&&(s="delivery");let r={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:s,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};l(a),i(r);try{localStorage.setItem("mockUser",JSON.stringify(a)),localStorage.setItem("mockProfile",JSON.stringify(r)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:a,mockProfile:r})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{"admin"===s?window.location.href="/dashboard/admin":"school"===s?window.location.href="/dashboard/school":"delivery"===s?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:a},error:null}},x=async()=>{try{return l(null),i(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},g=async e=>{if(!a)return{data:null,error:"No user logged in"};let t={...n,...e};return i(t),{data:t,error:null}};return(0,s.jsx)(o.Provider,{value:{user:a,profile:n,loading:c,signUp:h,signIn:p,signOut:x,updateProfile:g,hasRole:e=>{if(!n)return!1;let t={admin:4,school:3,delivery:2,student:1};return t[n.role]>=t[e]}},children:t})}function i(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var s=a(12115),r=a(63655),l=a(95155),o=s.forwardRef((e,t)=>(0,l.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var n=o},51362:(e,t,a)=>{"use strict";a.d(t,{D:()=>c,N:()=>d});var s=a(12115),r=(e,t,a,s,r,l,o,n)=>{let i=document.documentElement,c=["light","dark"];function d(t){var a;(Array.isArray(e)?e:[e]).forEach(e=>{let a="class"===e,s=a&&l?r.map(e=>l[e]||e):r;a?(i.classList.remove(...s),i.classList.add(l&&l[t]?l[t]:t)):i.setAttribute(e,t)}),a=t,n&&c.includes(a)&&(i.style.colorScheme=a)}if(s)d(s);else try{let e=localStorage.getItem(t)||a,s=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(s)}catch(e){}},l=["light","dark"],o="(prefers-color-scheme: dark)",n=s.createContext(void 0),i={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=s.useContext(n))?e:i},d=e=>s.useContext(n)?s.createElement(s.Fragment,null,e.children):s.createElement(u,{...e}),m=["light","dark"],u=e=>{let{forcedTheme:t,disableTransitionOnChange:a=!1,enableSystem:r=!0,enableColorScheme:i=!0,storageKey:c="theme",themes:d=m,defaultTheme:u=r?"system":"light",attribute:v="data-theme",value:f,children:y,nonce:b,scriptProps:j}=e,[w,N]=s.useState(()=>p(c,u)),[k,S]=s.useState(()=>"system"===w?g():w),A=f?Object.values(f):d,C=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&r&&(t=g());let s=f?f[t]:t,o=a?x(b):null,n=document.documentElement,c=e=>{"class"===e?(n.classList.remove(...A),s&&n.classList.add(s)):e.startsWith("data-")&&(s?n.setAttribute(e,s):n.removeAttribute(e))};if(Array.isArray(v)?v.forEach(c):c(v),i){let e=l.includes(u)?u:null,a=l.includes(t)?t:e;n.style.colorScheme=a}null==o||o()},[b]),T=s.useCallback(e=>{let t="function"==typeof e?e(w):e;N(t);try{localStorage.setItem(c,t)}catch(e){}},[w]),_=s.useCallback(e=>{S(g(e)),"system"===w&&r&&!t&&C("system")},[w,t]);s.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(_),_(e),()=>e.removeListener(_)},[_]),s.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?N(e.newValue):T(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),s.useEffect(()=>{C(null!=t?t:w)},[t,w]);let E=s.useMemo(()=>({theme:w,setTheme:T,forcedTheme:t,resolvedTheme:"system"===w?k:w,themes:r?[...d,"system"]:d,systemTheme:r?k:void 0}),[w,T,t,k,r,d]);return s.createElement(n.Provider,{value:E},s.createElement(h,{forcedTheme:t,storageKey:c,attribute:v,enableSystem:r,enableColorScheme:i,defaultTheme:u,value:f,themes:d,nonce:b,scriptProps:j}),y)},h=s.memo(e=>{let{forcedTheme:t,storageKey:a,attribute:l,enableSystem:o,enableColorScheme:n,defaultTheme:i,value:c,themes:d,nonce:m,scriptProps:u}=e,h=JSON.stringify([l,a,i,t,d,c,o,n]).slice(1,-1);return s.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let a;try{a=localStorage.getItem(e)||void 0}catch(e){}return a||t},x=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},53896:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("school",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10",key:"1xqip1"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6",key:"9d2mlk"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>n,TN:()=>i});var s=a(95155);a(12115);var r=a(74466),l=a(59434);let o=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(o({variant:a}),t),...r})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},57599:(e,t,a)=>{Promise.resolve().then(a.bind(a,7982))},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>d});var s=a(95155);a(12115);var r=a(22918),l=a(66474),o=a(5196),n=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,size:a="default",children:o,...n}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[o,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:t,children:a,position:l="popper",...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...o,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(x,{})]})})}function h(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}},62098:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var s=a(95155);a(12115);var r=a(59434);function l(e){let{className:t,type:a,...l}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},66474:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78749:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var s=a(95155);a(12115);var r=a(40968),l=a(59434);function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},87949:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89367:(e,t,a)=>{"use strict";function s(e,[t,a]){return Math.min(a,Math.max(t,e))}a.d(t,{q:()=>s})},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93509:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,5486,5148,8698,6874,1672,2632,8441,1684,7358],()=>t(57599)),_N_E=e.O()}]);