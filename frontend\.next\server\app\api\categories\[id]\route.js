"use strict";(()=>{var e={};e.id=4096,e.ids=[4096],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56727:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>u,PUT:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),d=t(38561);async function u(e,{params:r}){try{let e=d.CN.getCategories().find(e=>e.id===r.id);if(!e)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});return a.NextResponse.json({category:e})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:r}){try{let{name_ar:t,name_en:s,name_fr:n,slug:o,icon:i,description:u,is_active:p,order_index:c}=await e.json(),x=d.CN.getCategories(),g=x.findIndex(e=>e.id===r.id);if(-1===g)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});if(o&&o!==x[g].slug&&x.find(e=>e.slug===o&&e.id!==r.id))return a.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let l={...x[g],name_ar:t||x[g].name_ar,name_en:void 0!==s?s:x[g].name_en,name_fr:void 0!==n?n:x[g].name_fr,slug:o||x[g].slug,icon:void 0!==i?i:x[g].icon,description:void 0!==u?u:x[g].description,is_active:void 0!==p?p:x[g].is_active,order_index:void 0!==c?c:x[g].order_index,updated_at:new Date().toISOString()};return x[g]=l,d.CN.saveCategories(x),a.NextResponse.json({message:"تم تحديث الفئة بنجاح",category:l})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e,{params:r}){try{let e=d.CN.getCategories(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});let s=d.CN.getProducts(),n=e[t].slug,o=s.filter(e=>e.category===n);if(o.length>0)return a.NextResponse.json({error:`لا يمكن حذف الفئة لأنها مستخدمة في ${o.length} منتج`},{status:400});return e.splice(t,1),d.CN.saveCategories(e),a.NextResponse.json({message:"تم حذف الفئة بنجاح"})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:l,serverHooks:f}=x;function v(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:l})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(56727));module.exports=s})();